#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多智能体协作机制测试脚本

测试新实现的多智能体协作功能，包括：
1. 消息总线系统
2. 共享知识库
3. 依赖关系管理
4. 协作推理引擎
5. 协作式信息抽取
"""

import sys
import os
import time
import json
from typing import Dict, List

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from judicial_ie_coordinator import (
    JudicialIECoordinator,
    MessageBus,
    SharedKnowledgeBase,
    AgentDependencyManager,
    CollaborativeReasoningEngine,
    MessageType,
    AgentMessage
)
from extraction_types import ExtractionTaskType

def test_message_bus():
    """测试异步消息总线系统"""
    print("🔄 测试异步消息总线系统")

    message_bus = MessageBus(max_workers=2)

    # 注册智能体
    message_bus.register_agent("entity", {"type": "entity_extractor"})
    message_bus.register_agent("legal", {"type": "legal_extractor"})

    # 测试消息发布和订阅
    received_messages = []

    def message_handler(message: AgentMessage):
        received_messages.append(message)
        print(f"  📨 收到消息: {message.content}")

    # 订阅消息
    message_bus.subscribe("legal", MessageType.KNOWLEDGE_SHARE, message_handler)

    # 发布高优先级消息
    high_priority_message = AgentMessage(
        sender_id="entity",
        receiver_id="legal",
        message_type=MessageType.KNOWLEDGE_SHARE,
        content={"test": "高优先级协作消息"},
        priority=5
    )

    # 发布低优先级消息
    low_priority_message = AgentMessage(
        sender_id="entity",
        receiver_id="legal",
        message_type=MessageType.KNOWLEDGE_SHARE,
        content={"test": "低优先级协作消息"},
        priority=1
    )

    success1 = message_bus.publish(high_priority_message)
    success2 = message_bus.publish(low_priority_message)

    # 等待异步处理完成
    import time
    time.sleep(2)

    # 获取处理统计
    stats = message_bus.get_processing_stats()

    print(f"  ✅ 消息发布成功: {success1 and success2}")
    print(f"  ✅ 收到消息数量: {len(received_messages)}")
    print(f"  📊 处理统计: 成功 {stats['processed_count']}, 失败 {stats['failed_count']}")
    print(f"  🔄 队列大小: {stats['queue_size']}")

    # 关闭消息总线
    message_bus.shutdown()

    return len(received_messages) > 0 and stats['processed_count'] > 0

def test_shared_knowledge_base():
    """测试智能共享知识库系统"""
    print("\n📚 测试智能共享知识库系统")

    knowledge_base = SharedKnowledgeBase()

    # 存储多个测试知识
    test_knowledge_1 = {
        "content": "被告人张某犯盗窃罪，盗窃手机价值3000元",
        "confidence": 0.9,
        "tags": ["entity", "defendant", "crime"]
    }

    test_knowledge_2 = {
        "content": "张某于2023年3月15日在商店内实施盗窃行为",
        "confidence": 0.85,
        "tags": ["fact", "time", "location"]
    }

    test_knowledge_3 = {
        "content": "根据刑法第264条，盗窃罪判处有期徒刑",
        "confidence": 0.95,
        "tags": ["legal", "law", "sentence"]
    }

    # 存储知识
    success1 = knowledge_base.store_knowledge("knowledge_1", test_knowledge_1, "entity", "entity_extraction")
    success2 = knowledge_base.store_knowledge("knowledge_2", test_knowledge_2, "fact", "fact_extraction")
    success3 = knowledge_base.store_knowledge("knowledge_3", test_knowledge_3, "legal", "legal_extraction")

    print(f"  ✅ 知识存储成功: {success1 and success2 and success3}")

    # 测试智能搜索 - 基于语义相似度
    print("  🔍 测试智能搜索算法:")

    # 1. 基于内容相似度的搜索
    content_search = knowledge_base.search_knowledge({
        "content": "张某盗窃案件",
        "keywords": ["张某", "盗窃"],
        "min_confidence": 0.8,
        "min_match_score": 0.3
    }, "legal")

    print(f"    📝 内容相似度搜索: {len(content_search)} 个结果")
    if content_search:
        best_match = content_search[0]
        print(f"    🎯 最佳匹配分数: {best_match['match_score']:.3f}")
        print(f"    📊 匹配组件: {list(best_match['match_components'].keys())}")

    # 2. 基于标签的搜索
    tag_search = knowledge_base.search_knowledge({
        "tags": ["crime", "legal"],
        "type": "entity_extraction",
        "time_preference": "recent"
    }, "legal")

    print(f"    🏷️  标签匹配搜索: {len(tag_search)} 个结果")

    # 3. 基于类型的搜索
    type_search = knowledge_base.search_knowledge({
        "type": "legal_extraction",
        "preferred_confidence": 0.9
    }, "legal")

    print(f"    📋 类型匹配搜索: {len(type_search)} 个结果")

    # 检索知识
    retrieved = knowledge_base.retrieve_knowledge("knowledge_1", "legal")
    print(f"  ✅ 知识检索成功: {retrieved is not None}")

    # 获取统计信息
    stats = knowledge_base.get_knowledge_stats()
    print(f"  📊 知识库统计: 总计 {stats['total_knowledge_items']} 个知识项")
    print(f"  📈 平均匹配分数: {sum(r['match_score'] for r in content_search) / len(content_search):.3f}" if content_search else "  📈 无搜索结果")

    return (success1 and success2 and success3 and
            retrieved is not None and
            len(content_search) > 0)

def test_dependency_manager():
    """测试依赖关系管理器"""
    print("\n🔗 测试依赖关系管理器")

    dependency_manager = AgentDependencyManager()

    # 添加依赖关系
    dependency_manager.add_dependency("legal", "entity", weight=0.8)
    dependency_manager.add_dependency("sentence", "legal", weight=0.9)

    # 获取执行顺序
    agents = ["entity", "legal", "sentence"]
    execution_order = dependency_manager.get_execution_order(agents)

    print(f"  ✅ 执行顺序: {execution_order}")

    # 检查依赖关系满足
    satisfied = dependency_manager.check_dependency_satisfaction("legal", ["entity"])
    print(f"  ✅ 依赖关系满足: {satisfied}")

    return execution_order == ["entity", "legal", "sentence"]

def test_collaborative_reasoning():
    """测试协作推理引擎 - 真正的共识计算"""
    print("\n🧠 测试协作推理引擎 - 真正的共识计算")

    message_bus = MessageBus()
    knowledge_base = SharedKnowledgeBase()
    reasoning_engine = CollaborativeReasoningEngine(message_bus, knowledge_base)

    # 启动协作推理会话
    session = reasoning_engine.start_collaborative_reasoning(
        "test_session",
        ["entity", "legal", "fact"],
        {"type": "consensus_test"},
        {"test": "advanced_reasoning"}
    )

    print(f"  ✅ 协作会话创建: {session['id']}")

    # 添加多个推理步骤测试真正的共识计算
    reasoning1 = {
        "topic": "crime_analysis",
        "conclusion": "被告人张某犯盗窃罪",
        "position": "基于证据分析，张某确实实施了盗窃行为",
        "justification": "现场监控录像显示张某拿取他人财物",
        "confidence": 0.9,
        "evidence": ["监控录像", "证人证言", "物证"]
    }

    reasoning2 = {
        "topic": "crime_analysis",
        "conclusion": "张某犯盗窃罪事实清楚",
        "position": "法律分析确认构成盗窃罪",
        "justification": "符合刑法第264条盗窃罪构成要件",
        "confidence": 0.85,
        "evidence": ["法条依据", "案例参考"]
    }

    reasoning3 = {
        "topic": "crime_analysis",
        "conclusion": "张某盗窃行为属实",
        "position": "事实调查证实盗窃行为发生",
        "justification": "时间地点人物均有确凿证据",
        "confidence": 0.88,
        "evidence": ["时间记录", "地点确认", "监控录像"]
    }

    # 添加一个不同观点的推理步骤
    reasoning4 = {
        "topic": "crime_analysis",
        "conclusion": "需要进一步核实证据",
        "position": "部分证据存在疑点需要澄清",
        "justification": "证人证言存在时间上的矛盾",
        "confidence": 0.6,
        "evidence": ["证人证言矛盾"]
    }

    success1 = reasoning_engine.add_reasoning_step("test_session", "entity", reasoning1)
    success2 = reasoning_engine.add_reasoning_step("test_session", "legal", reasoning2)
    success3 = reasoning_engine.add_reasoning_step("test_session", "fact", reasoning3)
    success4 = reasoning_engine.add_reasoning_step("test_session", "entity", reasoning4)

    print(f"  ✅ 推理步骤添加: {success1 and success2 and success3 and success4}")

    # 综合洞察 - 测试真正的共识计算
    insights = reasoning_engine.synthesize_collaborative_insights("test_session")

    print(f"  🎯 协作洞察分析:")
    print(f"    📊 总体共识水平: {insights.get('consensus_level', 0):.3f}")
    print(f"    👥 活跃参与者: {insights.get('active_participants', 0)}")
    print(f"    📝 推理步骤总数: {insights.get('total_reasoning_steps', 0)}")

    if insights.get('insights'):
        for i, insight in enumerate(insights['insights']):
            print(f"    💡 洞察 {i+1}: 主题 '{insight['topic']}', 共识分数 {insight['consensus_score']:.3f}")
            print(f"       参与者数量: {insight['participant_count']}, 平均置信度: {insight['average_confidence']:.3f}")

    # 等待异步消息处理
    import time
    time.sleep(1)

    # 关闭消息总线
    message_bus.shutdown()

    return (session['id'] == "test_session" and
            insights.get('consensus_level', 0) > 0 and
            insights.get('total_reasoning_steps', 0) == 4)

def test_full_collaboration_system():
    """测试完整的协作系统"""
    print("\n🚀 测试完整协作系统")

    try:
        # 初始化协调器（启用协作）
        coordinator = JudicialIECoordinator(
            enable_parallel=False,  # 简化测试
            enable_cache=False,     # 简化测试
            enable_collaboration=True
        )

        print("  ✅ 协调器初始化成功")

        # 测试文本
        test_text = "被告人张某于2023年3月15日在北京市朝阳区某商店内盗窃手机一部，价值3000元。经审理，认定张某犯盗窃罪，判处有期徒刑六个月。"

        # 执行协作式信息抽取
        start_time = time.time()
        result = coordinator.extract_information(test_text, ExtractionTaskType.COMPREHENSIVE_EXTRACTION)
        extraction_time = time.time() - start_time

        print(f"  ✅ 协作抽取完成，耗时: {extraction_time:.2f}秒")
        print(f"  📊 抽取状态: {result['status']}")

        if result['status'] == 'success':
            results = result['results']
            print(f"  📈 抽取结果数量: {results['quality_metrics']['total_extractions']}")
            print(f"  🎯 平均置信度: {results['quality_metrics']['average_confidence']:.2f}")

            # 检查协作指标
            if 'collaboration_insights' in results:
                insights = results['collaboration_insights']
                print(f"  🤝 协作共识水平: {insights.get('consensus_level', 0):.2f}")
                print(f"  👥 活跃参与者: {insights.get('active_participants', 0)}")

        # 获取性能报告
        performance_report = coordinator.get_comprehensive_performance_report()
        collaboration_metrics = performance_report['extraction_metrics']

        print(f"  📊 协作会话数: {collaboration_metrics.get('collaboration_sessions', 0)}")
        print(f"  📤 知识分享事件: {collaboration_metrics.get('knowledge_sharing_events', 0)}")
        print(f"  ✅ 达成共识次数: {collaboration_metrics.get('consensus_achieved', 0)}")

        return result['status'] == 'success'

    except Exception as e:
        print(f"  ❌ 协作系统测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 多智能体协作机制测试开始\n")

    test_results = []

    # 运行各项测试
    test_results.append(("消息总线", test_message_bus()))
    test_results.append(("共享知识库", test_shared_knowledge_base()))
    test_results.append(("依赖管理器", test_dependency_manager()))
    test_results.append(("协作推理", test_collaborative_reasoning()))
    test_results.append(("完整协作系统", test_full_collaboration_system()))

    # 输出测试结果
    print("\n" + "="*50)
    print("📋 测试结果总结")
    print("="*50)

    passed = 0
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15} : {status}")
        if result:
            passed += 1

    print(f"\n总计: {passed}/{len(test_results)} 个测试通过")

    if passed == len(test_results):
        print("🎉 所有测试通过！多智能体协作机制实现成功！")
    else:
        print("⚠️  部分测试失败，需要进一步调试")

if __name__ == "__main__":
    main()
