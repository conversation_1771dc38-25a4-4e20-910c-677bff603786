"""
法律要素抽取智能体 (LegalElementAgent)

基于LLM的智能法律要素抽取专家，专门负责从司法文本中智能抽取法律要素信息，包括：
- 罪名认定（具体罪名、法条依据）
- 构成要件（客观要件、主观要件）
- 量刑情节（从轻、从重、减轻情节）
- 法律适用（适用条文、司法解释）

核心特色：
1. 基于GPT-4的智能法律推理和要素识别
2. 继承统一的智能体基类架构
3. 专业化的法律知识和逻辑分析能力
4. 智能化的法条匹配和适用分析
5. 与证据权重分析系统的深度集成
"""

import logging
import json
from typing import Dict, List

# 导入基类和相关类型
from .base_agent import BaseExtractionAgent
from extraction_types import ExtractionTaskType, InformationType, ExtractionResult

# 配置日志
logger = logging.getLogger(__name__)


class LegalElementAgent(BaseExtractionAgent):
    """基于LLM的法律要素抽取智能体 - 专门负责法律要素信息的智能抽取"""

    def __init__(self, legal_knowledge_base: Dict = None):
        super().__init__("llm_legal_element_extractor", legal_knowledge_base)
        
        # 专业化领域：法律要素信息
        self.specialization = [
            InformationType.CHARGES,
            InformationType.LEGAL_ARTICLES,
            InformationType.LEGAL_ELEMENTS,
            InformationType.SENTENCING_FACTORS
        ]
        
        # 扩展性能统计
        self.performance_stats.update({
            "legal_accuracy_scores": [],
            "charges_identified": 0,
            "legal_articles_found": 0,
            "elements_analyzed": 0
        })
        
        logger.info(f"基于LLM的法律要素抽取智能体 {self.agent_id} 初始化完成")

    def extract(self, text: str, task_type: ExtractionTaskType) -> List[ExtractionResult]:
        """
        执行基于LLM的智能法律要素抽取

        Args:
            text: 待抽取的司法文本
            task_type: 抽取任务类型

        Returns:
            法律要素抽取结果列表
        """
        logger.info(f"开始基于LLM的法律要素抽取，文本长度: {len(text)} 字符")

        try:
            # 构建LLM法律要素抽取提示
            prompt = self._build_extraction_prompt(text, task_type)

            # 调用LLM进行法律要素抽取
            logger.info("调用LLM进行法律要素抽取...")
            response = self._call_llm(prompt, "judicial legal element extraction expert")

            # 解析LLM响应
            parsed_results = self._parse_llm_response(response, text)

            # 转换为ExtractionResult对象
            extraction_results = self._convert_to_extraction_results(parsed_results, text)

            # 后处理：法律准确性验证和质量评估
            validated_results = self._validate_legal_accuracy(extraction_results, text)

            # 更新性能统计
            self._update_performance_stats(validated_results)

            logger.info(f"基于LLM的法律要素抽取完成，共抽取 {len(validated_results)} 个法律要素")
            return validated_results

        except Exception as e:
            logger.error(f"基于LLM的法律要素抽取过程出错: {e}")
            return []

    def _build_extraction_prompt(self, text: str, task_type: ExtractionTaskType) -> str:
        """构建LLM法律要素抽取提示"""
        
        prompt = f"""作为一名专业的司法法律要素抽取专家，请从以下司法文本中准确抽取法律要素信息。

【司法文本】
{text}

【抽取任务】
请从上述文本中抽取以下类型的法律要素信息：

1. **罪名认定**：
   - 具体的罪名及其法律依据
   - 罪名的构成要件分析
   - 罪名之间的关系（主罪、从罪等）

2. **法条适用**：
   - 适用的具体法条和司法解释
   - 法条的具体内容和适用条件
   - 法条适用的逻辑和依据

3. **构成要件**：
   - 客观要件的认定和分析
   - 主观要件的认定和分析
   - 要件满足情况的判断

4. **量刑情节**：
   - 从轻、从重、减轻等量刑情节
   - 情节的法律依据和适用条件
   - 情节对量刑的具体影响

【抽取要求】
1. **准确性**：确保法律要素的准确性，不要添加文本中不存在的法律信息
2. **完整性**：尽可能抽取所有相关的法律要素，不要遗漏重要信息
3. **专业性**：使用准确的法律术语，保持专业性
4. **逻辑性**：确保法律要素之间的逻辑关系正确

【输出格式】
请严格按照以下JSON格式输出抽取结果：
{{
    "charges": [
        {{
            "charge_name": "具体罪名",
            "legal_basis": "法律依据",
            "elements_analysis": "构成要件分析",
            "confidence": 0.95
        }}
    ],
    "legal_articles": [
        {{
            "article": "具体法条",
            "content": "法条内容",
            "application_reason": "适用理由",
            "confidence": 0.90
        }}
    ],
    "legal_elements": [
        {{
            "element_type": "客观要件/主观要件",
            "description": "要件描述",
            "satisfied": true/false,
            "analysis": "分析说明",
            "confidence": 0.85
        }}
    ],
    "sentencing_factors": [
        {{
            "factor_type": "从轻/从重/减轻",
            "description": "情节描述",
            "legal_basis": "法律依据",
            "impact": "对量刑的影响",
            "confidence": 0.80
        }}
    ]
}}

【注意事项】
1. 如果某类法律要素在文本中不存在，请返回空数组[]
2. 置信度应根据文本中法律信息的明确程度进行评估
3. 只返回JSON格式的结果，不要添加其他说明文字

请开始抽取："""

        return prompt

    def _convert_to_extraction_results(self, parsed_data: Dict, original_text: str) -> List[ExtractionResult]:
        """将解析后的数据转换为ExtractionResult对象"""
        results = []
        
        # 处理罪名
        charges = parsed_data.get("charges", [])
        for charge in charges:
            if charge and charge.get("charge_name"):
                confidence = charge.get("confidence", 0.8)
                evidence_weight = self._calculate_evidence_weight(charge.get("charge_name", ""), InformationType.CHARGES)
                source_span = self._find_source_span(charge.get("charge_name", ""), original_text)
                context = self._extract_context(charge.get("charge_name", ""), original_text)

                result = ExtractionResult(
                    info_type=InformationType.CHARGES,
                    content=charge.get("charge_name", ""),
                    confidence=confidence,
                    evidence_weight=evidence_weight,
                    source_span=source_span,
                    context=context,
                    agent_id=self.agent_id,
                    metadata={
                        "extraction_method": "llm_based",
                        "legal_category": "charge",
                        "legal_basis": charge.get("legal_basis", ""),
                        "elements_analysis": charge.get("elements_analysis", "")
                    }
                )
                results.append(result)

        # 处理法条
        legal_articles = parsed_data.get("legal_articles", [])
        for article in legal_articles:
            if article and article.get("article"):
                confidence = article.get("confidence", 0.8)
                evidence_weight = self._calculate_evidence_weight(article.get("article", ""), InformationType.LEGAL_ARTICLES)
                source_span = self._find_source_span(article.get("article", ""), original_text)
                context = self._extract_context(article.get("article", ""), original_text)

                result = ExtractionResult(
                    info_type=InformationType.LEGAL_ARTICLES,
                    content=article.get("article", ""),
                    confidence=confidence,
                    evidence_weight=evidence_weight,
                    source_span=source_span,
                    context=context,
                    agent_id=self.agent_id,
                    metadata={
                        "extraction_method": "llm_based",
                        "legal_category": "legal_article",
                        "article_content": article.get("content", ""),
                        "application_reason": article.get("application_reason", "")
                    }
                )
                results.append(result)

        # 处理构成要件
        legal_elements = parsed_data.get("legal_elements", [])
        for element in legal_elements:
            if element and element.get("description"):
                confidence = element.get("confidence", 0.8)
                evidence_weight = self._calculate_evidence_weight(element.get("description", ""), InformationType.LEGAL_ELEMENTS)
                source_span = self._find_source_span(element.get("description", ""), original_text)
                context = self._extract_context(element.get("description", ""), original_text)

                result = ExtractionResult(
                    info_type=InformationType.LEGAL_ELEMENTS,
                    content=element.get("description", ""),
                    confidence=confidence,
                    evidence_weight=evidence_weight,
                    source_span=source_span,
                    context=context,
                    agent_id=self.agent_id,
                    metadata={
                        "extraction_method": "llm_based",
                        "legal_category": "legal_element",
                        "element_type": element.get("element_type", ""),
                        "satisfied": element.get("satisfied", False),
                        "analysis": element.get("analysis", "")
                    }
                )
                results.append(result)

        # 处理量刑情节
        sentencing_factors = parsed_data.get("sentencing_factors", [])
        for factor in sentencing_factors:
            if factor and factor.get("description"):
                confidence = factor.get("confidence", 0.8)
                evidence_weight = self._calculate_evidence_weight(factor.get("description", ""), InformationType.SENTENCING_FACTORS)
                source_span = self._find_source_span(factor.get("description", ""), original_text)
                context = self._extract_context(factor.get("description", ""), original_text)

                result = ExtractionResult(
                    info_type=InformationType.SENTENCING_FACTORS,
                    content=factor.get("description", ""),
                    confidence=confidence,
                    evidence_weight=evidence_weight,
                    source_span=source_span,
                    context=context,
                    agent_id=self.agent_id,
                    metadata={
                        "extraction_method": "llm_based",
                        "legal_category": "sentencing_factor",
                        "factor_type": factor.get("factor_type", ""),
                        "legal_basis": factor.get("legal_basis", ""),
                        "impact": factor.get("impact", "")
                    }
                )
                results.append(result)

        return results

    def _validate_legal_accuracy(self, results: List[ExtractionResult], text: str) -> List[ExtractionResult]:
        """验证法律准确性"""
        # 计算法律准确性分数
        accuracy_score = self._calculate_legal_accuracy_score(results, text)
        self.performance_stats["legal_accuracy_scores"].append(accuracy_score)
        
        # 过滤低质量结果
        validated_results = []
        for result in results:
            if self._is_legally_valid(result):
                validated_results.append(result)
        
        logger.info(f"法律准确性验证完成，保留 {len(validated_results)}/{len(results)} 个结果")
        return validated_results

    def _calculate_legal_accuracy_score(self, results: List[ExtractionResult], text: str) -> float:
        """计算法律准确性分数"""
        if not results:
            return 0.0
        
        # 基于置信度和证据权重计算
        total_score = 0.0
        for result in results:
            score = 0.6 * result.confidence + 0.4 * result.evidence_weight
            total_score += score
        
        return total_score / len(results)

    def _is_legally_valid(self, result: ExtractionResult) -> bool:
        """检查法律要素的有效性"""
        # 基本质量检查
        if len(result.content) < 2:
            return False
        
        # 置信度阈值
        if result.confidence < 0.3:
            return False
        
        # 法律术语合理性检查
        legal_keywords = ["法", "条", "款", "项", "罪", "刑", "判", "决"]
        if result.info_type in [InformationType.CHARGES, InformationType.LEGAL_ARTICLES]:
            if not any(keyword in result.content for keyword in legal_keywords):
                return False
        
        return True
