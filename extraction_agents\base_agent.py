"""
智能体基类 (BaseExtractionAgent)

为所有司法信息抽取智能体提供统一的基础架构和通用功能，包括：
- 统一的LLM调用接口
- 标准化的结果处理流程
- 通用的性能统计机制
- 一致的错误处理策略

核心设计原则：
1. 统一技术架构，确保所有智能体的一致性
2. 模块化设计，便于扩展和维护
3. 标准化接口，支持协作和集成
4. 高质量代码，符合学术发表标准
"""

import json
import logging
import time
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from abc import ABC, abstractmethod

# 导入现有系统组件
from judicial_cola import get_completion
from extraction_types import ExtractionTaskType, InformationType, ExtractionResult
from core.evidence_weight_engine import EvidenceWeightEngine, EvidenceItem, EvidenceType

# 配置日志
logger = logging.getLogger(__name__)

class BaseExtractionAgent(ABC):
    """司法信息抽取智能体基类"""

    def __init__(self, agent_id: str, legal_knowledge_base: Dict = None):
        """
        初始化智能体基类

        Args:
            agent_id: 智能体唯一标识
            legal_knowledge_base: 法律知识库
        """
        self.agent_id = agent_id
        self.legal_knowledge = legal_knowledge_base or {}

        # 专业化领域（由子类定义）
        self.specialization = []

        # 性能统计
        self.performance_stats = {
            "total_extractions": 0,
            "successful_extractions": 0,
            "high_confidence_extractions": 0,
            "api_calls": 0,
            "total_processing_time": 0.0,
            "average_confidence": 0.0
        }

        # 权重引导机制
        self.weight_engine = EvidenceWeightEngine(get_completion)
        self.weight_guidance_enabled = True
        self.weight_threshold = 0.5
        self.guidance_strategy = "threshold_filter"

        logger.info(f"智能体 {self.agent_id} 初始化完成")

    @abstractmethod
    def extract(self, text: str, task_type: ExtractionTaskType) -> List[ExtractionResult]:
        """
        执行信息抽取（抽象方法，由子类实现）

        Args:
            text: 待抽取的司法文本
            task_type: 抽取任务类型

        Returns:
            抽取结果列表
        """
        pass

    @abstractmethod
    def _build_extraction_prompt(self, text: str, task_type: ExtractionTaskType) -> str:
        """
        构建LLM抽取提示（抽象方法，由子类实现）

        Args:
            text: 待抽取的司法文本
            task_type: 抽取任务类型

        Returns:
            LLM提示字符串
        """
        pass

    def _call_llm(self, prompt: str, role: str = "judicial information extraction expert") -> str:
        """
        统一的LLM调用接口

        Args:
            prompt: LLM提示
            role: 角色设定

        Returns:
            LLM响应
        """
        try:
            self.performance_stats["api_calls"] += 1
            start_time = time.time()

            response = get_completion(prompt, role=role)

            processing_time = time.time() - start_time
            self.performance_stats["total_processing_time"] += processing_time

            logger.debug(f"LLM调用完成，耗时: {processing_time:.2f}秒")
            return response

        except Exception as e:
            logger.error(f"LLM调用失败: {e}")
            raise

    def _parse_llm_response(self, response: str, original_text: str) -> Dict:
        """
        解析LLM响应的通用方法

        Args:
            response: LLM响应文本
            original_text: 原始文本

        Returns:
            解析后的结果字典
        """
        try:
            # 尝试提取JSON格式的响应
            json_start = response.find('{')
            json_end = response.rfind('}') + 1

            if json_start != -1 and json_end > json_start:
                json_str = response[json_start:json_end]
                return json.loads(json_str)
            else:
                # 如果没有找到JSON，使用文本解析
                return self._parse_text_response(response)

        except json.JSONDecodeError as e:
            logger.warning(f"JSON解析失败: {e}，使用文本解析")
            return self._parse_text_response(response)

    def _parse_text_response(self, response: str) -> Dict:
        """
        文本响应解析的备用方法

        Args:
            response: LLM响应文本

        Returns:
            解析后的结果字典
        """
        # 基础的文本解析逻辑
        result = {}
        lines = response.split('\n')

        for line in lines:
            line = line.strip()
            if ':' in line and len(line) > 5:
                key, value = line.split(':', 1)
                key = key.strip().lower()
                value = value.strip()

                if value:
                    result[key] = [value] if key not in result else result[key] + [value]

        return result

    def _calculate_confidence(self, content: str, info_type: InformationType,
                            original_text: str) -> float:
        """
        计算抽取结果的置信度

        Args:
            content: 抽取的内容
            info_type: 信息类型
            original_text: 原始文本

        Returns:
            置信度分数 (0-1)
        """
        confidence = 0.5  # 基础置信度

        # 基于内容长度的置信度调整
        if len(content) > 2:
            confidence += 0.1
        if len(content) > 10:
            confidence += 0.1

        # 基于是否在原文中出现的置信度调整
        if content in original_text:
            confidence += 0.2

        # 基于信息类型的置信度调整
        type_confidence_map = {
            InformationType.DEFENDANT: 0.1,
            InformationType.VICTIM: 0.1,
            InformationType.CASE_FACTS: 0.05,
            InformationType.CHARGES: 0.15,
            InformationType.LEGAL_ARTICLES: 0.1
        }
        confidence += type_confidence_map.get(info_type, 0.0)

        return min(1.0, max(0.0, confidence))

    def _calculate_evidence_weight(self, content: str, info_type: InformationType) -> float:
        """
        计算证据权重

        Args:
            content: 抽取的内容
            info_type: 信息类型

        Returns:
            证据权重 (0-1)
        """
        # 基础权重映射
        type_weight_map = {
            InformationType.DEFENDANT: 0.9,
            InformationType.VICTIM: 0.8,
            InformationType.CASE_FACTS: 0.7,
            InformationType.CHARGES: 0.9,
            InformationType.LEGAL_ARTICLES: 0.8,
            InformationType.EVIDENCE: 0.85
        }

        base_weight = type_weight_map.get(info_type, 0.5)

        # 基于内容质量的权重调整
        content_factor = min(0.2, len(content) / 100)

        return min(1.0, base_weight + content_factor)

    def _find_source_span(self, content: str, original_text: str) -> Tuple[int, int]:
        """
        查找内容在原文中的位置

        Args:
            content: 抽取的内容
            original_text: 原始文本

        Returns:
            (开始位置, 结束位置)
        """
        start_pos = original_text.find(content)
        if start_pos != -1:
            return (start_pos, start_pos + len(content))
        else:
            return (0, 0)

    def _extract_context(self, content: str, original_text: str,
                        context_window: int = 50) -> str:
        """
        提取内容的上下文

        Args:
            content: 抽取的内容
            original_text: 原始文本
            context_window: 上下文窗口大小

        Returns:
            上下文字符串
        """
        start_pos = original_text.find(content)
        if start_pos == -1:
            return content

        context_start = max(0, start_pos - context_window)
        context_end = min(len(original_text), start_pos + len(content) + context_window)

        return original_text[context_start:context_end]

    def _update_performance_stats(self, results: List[ExtractionResult]):
        """
        更新性能统计

        Args:
            results: 抽取结果列表
        """
        self.performance_stats["total_extractions"] += 1

        if results:
            self.performance_stats["successful_extractions"] += 1

            # 计算平均置信度
            if results:
                avg_confidence = sum(r.confidence for r in results) / len(results)
                self.performance_stats["average_confidence"] = avg_confidence

                # 统计高置信度结果
                high_conf_count = sum(1 for r in results if r.confidence > 0.8)
                if high_conf_count > 0:
                    self.performance_stats["high_confidence_extractions"] += 1

    def get_performance_report(self) -> Dict:
        """
        获取性能报告

        Returns:
            性能统计字典
        """
        stats = self.performance_stats.copy()

        # 计算成功率
        if stats["total_extractions"] > 0:
            stats["success_rate"] = stats["successful_extractions"] / stats["total_extractions"]
        else:
            stats["success_rate"] = 0.0

        # 计算平均处理时间
        if stats["api_calls"] > 0:
            stats["average_processing_time"] = stats["total_processing_time"] / stats["api_calls"]
        else:
            stats["average_processing_time"] = 0.0

        return stats

    def apply_weight_guidance(self, results: List[ExtractionResult],
                            case_context: str = "") -> List[ExtractionResult]:
        """
        应用权重引导机制优化抽取结果

        Args:
            results: 原始抽取结果
            case_context: 案件上下文

        Returns:
            权重引导后的抽取结果
        """
        if not self.weight_guidance_enabled or not results:
            return results

        logger.info(f"对 {len(results)} 个抽取结果应用权重引导")

        # 1. 转换为EvidenceItem格式
        evidence_items = self._convert_to_evidence_items(results)

        # 2. 计算证据权重
        weighted_items = self.weight_engine.calculate_evidence_weights(evidence_items, case_context)

        # 3. 应用引导策略
        guided_items = self.weight_engine.apply_weight_guidance(weighted_items, self.guidance_strategy)

        # 4. 转换回ExtractionResult格式
        guided_results = self._convert_evidence_items_to_results(guided_items)

        # 5. 更新性能统计
        self._update_weight_guidance_stats(results, guided_results)

        logger.info(f"权重引导完成，保留 {len(guided_results)}/{len(results)} 个结果")
        return guided_results

    def _convert_to_evidence_items(self, results: List[ExtractionResult]) -> List[EvidenceItem]:
        """将ExtractionResult转换为EvidenceItem"""
        evidence_items = []

        for i, result in enumerate(results):
            # 映射信息类型到证据类型
            evidence_type = self._map_info_type_to_evidence_type(result.info_type)

            evidence_item = EvidenceItem(
                id=f"E{i+1}",
                content=result.content,
                evidence_type=evidence_type,
                source=result.context,
                metadata={
                    "original_confidence": result.confidence,
                    "original_evidence_weight": result.evidence_weight,
                    "agent_id": result.agent_id,
                    "info_type": result.info_type.value
                }
            )

            evidence_items.append(evidence_item)

        return evidence_items

    def _map_info_type_to_evidence_type(self, info_type: InformationType) -> EvidenceType:
        """映射信息类型到证据类型"""
        mapping = {
            InformationType.DEFENDANT: EvidenceType.TESTIMONIAL,
            InformationType.VICTIM: EvidenceType.TESTIMONIAL,
            InformationType.WITNESS: EvidenceType.TESTIMONIAL,
            InformationType.CASE_FACTS: EvidenceType.DOCUMENTARY,
            InformationType.EVIDENCE: EvidenceType.PHYSICAL,
            InformationType.CHARGES: EvidenceType.DOCUMENTARY,
            InformationType.LEGAL_ARTICLES: EvidenceType.DOCUMENTARY,
            InformationType.VERDICT: EvidenceType.DOCUMENTARY,
            InformationType.SENTENCE: EvidenceType.DOCUMENTARY,
            InformationType.IMPRISONMENT: EvidenceType.DOCUMENTARY
        }

        return mapping.get(info_type, EvidenceType.OTHER)

    def _convert_evidence_items_to_results(self, evidence_items: List[EvidenceItem]) -> List[ExtractionResult]:
        """将EvidenceItem转换回ExtractionResult"""
        results = []

        for item in evidence_items:
            # 从元数据中恢复原始信息
            original_info_type = InformationType(item.metadata.get("info_type", "other"))

            result = ExtractionResult(
                info_type=original_info_type,
                content=item.content,
                confidence=item.confidence if item.confidence > 0 else item.metadata.get("original_confidence", 0.5),
                evidence_weight=item.final_weight,
                source_span=(0, len(item.content)),
                context=item.source or "",
                agent_id=item.metadata.get("agent_id", self.agent_id),
                metadata={
                    "weight_guided": True,
                    "original_weight": item.metadata.get("original_evidence_weight", 0.0),
                    "guidance_strategy": self.guidance_strategy,
                    "weight_dimensions": {
                        "type_weight": item.type_weight,
                        "reliability_score": item.reliability_score,
                        "completeness_score": item.completeness_score,
                        "corroboration_score": item.corroboration_score,
                        "relevance_score": item.relevance_score
                    }
                }
            )

            results.append(result)

        return results

    def _update_weight_guidance_stats(self, original_results: List[ExtractionResult],
                                    guided_results: List[ExtractionResult]):
        """更新权重引导统计"""
        if "weight_guidance_stats" not in self.performance_stats:
            self.performance_stats["weight_guidance_stats"] = {
                "total_applications": 0,
                "total_filtered": 0,
                "average_retention_rate": 0.0,
                "weight_improvements": 0
            }

        stats = self.performance_stats["weight_guidance_stats"]
        stats["total_applications"] += 1

        filtered_count = len(original_results) - len(guided_results)
        stats["total_filtered"] += filtered_count

        retention_rate = len(guided_results) / len(original_results) if original_results else 0.0
        stats["average_retention_rate"] = (
            (stats["average_retention_rate"] * (stats["total_applications"] - 1) + retention_rate) /
            stats["total_applications"]
        )

        # 检查权重改进
        if guided_results:
            avg_guided_weight = sum(r.evidence_weight for r in guided_results) / len(guided_results)
            avg_original_weight = sum(r.evidence_weight for r in original_results) / len(original_results)

            if avg_guided_weight > avg_original_weight:
                stats["weight_improvements"] += 1

    def configure_weight_guidance(self, enabled: bool = True, threshold: float = 0.5,
                                strategy: str = "threshold_filter"):
        """
        配置权重引导参数

        Args:
            enabled: 是否启用权重引导
            threshold: 权重阈值
            strategy: 引导策略
        """
        self.weight_guidance_enabled = enabled
        self.weight_threshold = threshold
        self.guidance_strategy = strategy

        logger.info(f"权重引导配置更新: enabled={enabled}, threshold={threshold}, strategy={strategy}")

    def get_weight_analysis_report(self, results: List[ExtractionResult],
                                 case_context: str = "") -> Dict:
        """
        获取权重分析报告

        Args:
            results: 抽取结果
            case_context: 案件上下文

        Returns:
            权重分析报告
        """
        if not results:
            return {"error": "没有抽取结果可分析"}

        # 转换为证据项
        evidence_items = self._convert_to_evidence_items(results)

        # 计算权重
        weighted_items = self.weight_engine.calculate_evidence_weights(evidence_items, case_context)

        # 生成报告
        report = self.weight_engine.get_weight_analysis_report(weighted_items)

        # 添加智能体特定信息
        report["agent_id"] = self.agent_id
        report["specialization"] = self.specialization
        report["guidance_config"] = {
            "enabled": self.weight_guidance_enabled,
            "threshold": self.weight_threshold,
            "strategy": self.guidance_strategy
        }

        return report
