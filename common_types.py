"""
司法信息抽取系统通用类型定义

统一定义所有智能体使用的枚举类型和数据结构，避免循环导入和类型不一致问题。
"""

from enum import Enum
from dataclasses import dataclass
from typing import Dict, Any

class InformationType(Enum):
    """司法信息类型枚举 - 统一定义"""
    
    # 基础实体类型
    DEFENDANT = "defendant"                # 被告人
    VICTIM = "victim"                      # 被害人
    WITNESS = "witness"                    # 证人
    JUDGE = "judge"                        # 法官
    LAWYER = "lawyer"                      # 律师
    
    # 时空信息
    CRIME_TIME = "crime_time"              # 犯罪时间
    TRIAL_TIME = "trial_time"              # 审理时间
    CRIME_LOCATION = "crime_location"      # 犯罪地点
    
    # 案件事实
    CASE_FACTS = "case_facts"              # 案件事实
    EVIDENCE = "evidence"                  # 证据
    
    # 法律要素
    CHARGES = "charges"                    # 罪名
    LEGAL_ARTICLES = "legal_articles"      # 法条
    LEGAL_ELEMENTS = "legal_elements"      # 法律要素
    SENTENCING_FACTORS = "sentencing_factors"  # 量刑情节
    
    # 判决信息
    SENTENCE = "sentence"                  # 判决结果
    IMPRISONMENT = "imprisonment"          # 刑期
    JUDGMENT_REASONING = "judgment_reasoning"  # 判决理由
    EXECUTION_METHOD = "execution_method"  # 执行方式
    VERDICT = "verdict"                    # 裁决
    FINE = "fine"                         # 罚金
    PROBATION = "probation"               # 缓刑

class ExtractionTaskType(Enum):
    """信息抽取任务类型"""
    ENTITY_EXTRACTION = "entity_extraction"
    FACT_EXTRACTION = "fact_extraction"
    LEGAL_ELEMENT_EXTRACTION = "legal_element_extraction"
    SENTENCE_EXTRACTION = "sentence_extraction"
    COMPREHENSIVE_EXTRACTION = "comprehensive_extraction"

@dataclass
class ExtractionResult:
    """信息抽取结果统一数据结构"""
    info_type: InformationType
    content: str
    confidence: float
    evidence_weight: float
    source_span: tuple
    context: str
    agent_id: str
    metadata: Dict[str, Any] = None

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "info_type": self.info_type.value,
            "content": self.content,
            "confidence": self.confidence,
            "evidence_weight": self.evidence_weight,
            "source_span": self.source_span,
            "context": self.context,
            "agent_id": self.agent_id,
            "metadata": self.metadata or {}
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ExtractionResult':
        """从字典创建ExtractionResult对象"""
        return cls(
            info_type=InformationType(data["info_type"]),
            content=data["content"],
            confidence=data["confidence"],
            evidence_weight=data["evidence_weight"],
            source_span=tuple(data["source_span"]),
            context=data["context"],
            agent_id=data["agent_id"],
            metadata=data.get("metadata", {})
        )

# 信息类型分组映射
INFO_TYPE_GROUPS = {
    "entity": [
        InformationType.DEFENDANT,
        InformationType.VICTIM,
        InformationType.WITNESS,
        InformationType.JUDGE,
        InformationType.LAWYER,
        InformationType.CRIME_TIME,
        InformationType.TRIAL_TIME,
        InformationType.CRIME_LOCATION
    ],
    "fact": [
        InformationType.CASE_FACTS,
        InformationType.EVIDENCE
    ],
    "legal": [
        InformationType.CHARGES,
        InformationType.LEGAL_ARTICLES,
        InformationType.LEGAL_ELEMENTS,
        InformationType.SENTENCING_FACTORS
    ],
    "sentence": [
        InformationType.SENTENCE,
        InformationType.IMPRISONMENT,
        InformationType.JUDGMENT_REASONING,
        InformationType.EXECUTION_METHOD,
        InformationType.VERDICT,
        InformationType.FINE,
        InformationType.PROBATION
    ]
}

# 证据类型映射
EVIDENCE_TYPE_MAPPING = {
    # 实体类型
    InformationType.DEFENDANT: "entity_evidence",
    InformationType.VICTIM: "entity_evidence",
    InformationType.WITNESS: "entity_evidence",
    InformationType.JUDGE: "entity_evidence",
    InformationType.LAWYER: "entity_evidence",
    InformationType.CRIME_TIME: "entity_evidence",
    InformationType.TRIAL_TIME: "entity_evidence",
    InformationType.CRIME_LOCATION: "entity_evidence",
    
    # 事实类型
    InformationType.CASE_FACTS: "fact_evidence",
    InformationType.EVIDENCE: "fact_evidence",
    
    # 法律类型
    InformationType.CHARGES: "legal_evidence",
    InformationType.LEGAL_ARTICLES: "legal_evidence",
    InformationType.LEGAL_ELEMENTS: "legal_evidence",
    InformationType.SENTENCING_FACTORS: "sentencing_evidence",
    
    # 判决类型
    InformationType.SENTENCE: "judgment_evidence",
    InformationType.IMPRISONMENT: "judgment_evidence",
    InformationType.JUDGMENT_REASONING: "judgment_evidence",
    InformationType.EXECUTION_METHOD: "judgment_evidence",
    InformationType.VERDICT: "judgment_evidence",
    InformationType.FINE: "judgment_evidence",
    InformationType.PROBATION: "judgment_evidence"
}

def get_info_types_by_agent(agent_type: str) -> list:
    """根据智能体类型获取对应的信息类型列表"""
    return INFO_TYPE_GROUPS.get(agent_type, [])

def get_evidence_type(info_type: InformationType) -> str:
    """获取信息类型对应的证据类型"""
    return EVIDENCE_TYPE_MAPPING.get(info_type, "general_evidence")

def is_valid_info_type(info_type_str: str) -> bool:
    """检查信息类型字符串是否有效"""
    try:
        InformationType(info_type_str)
        return True
    except ValueError:
        return False
