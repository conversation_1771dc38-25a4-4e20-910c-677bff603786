"""
自适应多层次辩论框架(Adaptive Multi-level Debate Framework, AMDF)

该框架实现了一种能够根据案件复杂度和争议点自动调整辩论深度、广度和策略的机制，
将辩论过程分为事实层、法律适用层和量刑层三个层次，实现更精确的法律推理。

主要创新点：
1. 争议点自动检测算法：设计基于语义对比的算法，自动识别各智能体观点中的分歧点，量化分歧程度
2. 辩论深度控制机制：根据争议点的重要性和分歧程度，动态决定辩论轮次和深入程度
3. 层次化辩论协议：实现三层辩论结构（事实层→法律适用层→量刑层），确保辩论按照逻辑顺序进行
4. 层间依赖管理：设计层间信息传递机制，确保上层辩论基于下层辩论的共识结果
5. 辩论终止条件：开发基于收敛度的辩论终止机制，在达成共识或无法进一步缩小分歧时结束辩论
"""

import re
import json
import math
import time
import logging
import numpy as np
import jieba
import jieba.analyse
from typing import Dict, List

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 辩论层次定义
DEBATE_LAYERS = {
    "FACT": {
        "name": "事实层",
        "description": "讨论案件的基本事实、证据和事实认定",
        "key_aspects": ["时间", "地点", "人物", "行为", "动机", "证据", "证据链"],
        "priority": 1,  # 最高优先级
        "min_rounds": 1,
        "max_rounds": 3,
        "convergence_threshold": 0.8,  # 80%共识即可进入下一层
        "dependencies": [],  # 事实层没有依赖
        "key_outputs": ["案件事实认定", "证据采信结论", "主观心理状态认定"],
        "failure_impact": "如果事实层辩论失败，将无法进行后续辩论，因为法律适用和量刑都依赖于对事实的共识"
    },
    "LAW": {
        "name": "法律适用层",
        "description": "讨论法律条款的适用、构成要件的满足情况和罪名认定",
        "key_aspects": ["客观要件", "主观要件", "法条适用", "罪名认定", "法律解释"],
        "priority": 2,
        "min_rounds": 1,
        "max_rounds": 3,
        "convergence_threshold": 0.7,  # 70%共识即可进入下一层
        "dependencies": ["FACT"],  # 法律适用层依赖事实层
        "key_outputs": ["罪名认定结论", "构成要件分析", "法律适用依据"],
        "failure_impact": "如果法律适用层辩论失败，将无法进行量刑辩论，因为量刑依赖于对罪名的认定"
    },
    "SENTENCING": {
        "name": "量刑层",
        "description": "讨论量刑情节、量刑标准和具体刑期",
        "key_aspects": ["从重情节", "从轻情节", "基准刑期", "刑期调整", "最终刑期"],
        "priority": 3,
        "min_rounds": 1,
        "max_rounds": 3,
        "convergence_threshold": 0.6,  # 60%共识即可结束辩论
        "dependencies": ["FACT", "LAW"],  # 量刑层依赖事实层和法律适用层
        "key_outputs": ["量刑情节认定", "基准刑确定", "最终刑期建议"],
        "failure_impact": "如果量刑层辩论失败，将无法给出合理的刑期建议，最终判决将缺乏量刑依据"
    }
}

# 层次间关键信息传递定义
LAYER_DEPENDENCIES = {
    "FACT_to_LAW": {
        "description": "事实层到法律适用层的信息传递",
        "key_information": ["案件事实认定", "证据采信结论", "主观心理状态认定"],
        "impact": "事实层的共识直接影响法律适用层对构成要件的分析"
    },
    "LAW_to_SENTENCING": {
        "description": "法律适用层到量刑层的信息传递",
        "key_information": ["罪名认定结论", "构成要件分析", "法律适用依据"],
        "impact": "法律适用层的共识直接影响量刑层对量刑情节的认定和基准刑的确定"
    },
    "FACT_to_SENTENCING": {
        "description": "事实层到量刑层的信息传递",
        "key_information": ["案件事实认定", "主观心理状态认定"],
        "impact": "事实层的共识直接影响量刑层对量刑情节的认定"
    }
}

class DebatePoint:
    """辩论点类，表示辩论中的一个争议点"""

    def __init__(self, topic: str, layer: str, importance: float = 0.5):
        """
        初始化辩论点

        Args:
            topic: 争议点主题
            layer: 所属辩论层次 (FACT, LAW, SENTENCING)
            importance: 重要性分数 (0.0-1.0)
        """
        self.topic = topic
        self.layer = layer
        self.importance = importance
        self.opinions = {}  # 各方观点
        self.divergence_score = 0.0  # 分歧程度
        self.consensus = None  # 达成的共识
        self.consensus_score = 0.0  # 共识程度

    def add_opinion(self, agent: str, opinion: str):
        """添加智能体对该争议点的观点"""
        self.opinions[agent] = opinion

    def calculate_divergence(self):
        """计算该争议点的分歧程度"""
        if len(self.opinions) <= 1:
            self.divergence_score = 0.0
            return self.divergence_score

        # 使用更复杂的语义相似度算法计算分歧程度
        opinion_texts = list(self.opinions.values())

        # 1. 长度差异
        lengths = [len(text) for text in opinion_texts]
        length_variance = np.var(lengths) / (np.mean(lengths) ** 2) if np.mean(lengths) > 0 else 0

        # 2. 关键词差异 - 使用jieba进行中文分词和关键词提取
        keyword_sets = []
        tfidf_keywords = []  # 使用TF-IDF提取的关键词

        # 法律领域停用词
        legal_stopwords = set(["的", "了", "是", "在", "有", "和", "与", "对", "等", "中", "为", "以", "及", "上", "下", "由", "于", "该", "从", "或", "被"])

        for text in opinion_texts:
            # 使用jieba分词
            words = jieba.lcut(text)
            # 过滤停用词
            filtered_words = [w for w in words if len(w) > 1 and w not in legal_stopwords]
            keyword_sets.append(set(filtered_words))

            # 使用TF-IDF提取关键词（取前10个）
            try:
                tfidf_words = jieba.analyse.extract_tags(text, topK=10, withWeight=False)
                tfidf_keywords.append(set(tfidf_words))
            except:
                # 如果TF-IDF提取失败，使用普通分词结果
                tfidf_keywords.append(set(filtered_words[:10] if len(filtered_words) > 10 else filtered_words))

        # 计算普通关键词集合的Jaccard相似度
        basic_similarities = []
        for i in range(len(keyword_sets)):
            for j in range(i+1, len(keyword_sets)):
                set_i = keyword_sets[i]
                set_j = keyword_sets[j]
                if not set_i or not set_j:
                    continue
                intersection = len(set_i.intersection(set_j))
                union = len(set_i.union(set_j))
                similarity = intersection / union if union > 0 else 0
                basic_similarities.append(similarity)

        # 计算TF-IDF关键词集合的Jaccard相似度
        tfidf_similarities = []
        for i in range(len(tfidf_keywords)):
            for j in range(i+1, len(tfidf_keywords)):
                set_i = tfidf_keywords[i]
                set_j = tfidf_keywords[j]
                if not set_i or not set_j:
                    continue
                intersection = len(set_i.intersection(set_j))
                union = len(set_i.union(set_j))
                similarity = intersection / union if union > 0 else 0
                tfidf_similarities.append(similarity)

        # 综合计算关键词分歧度
        basic_keyword_divergence = 1 - (sum(basic_similarities) / len(basic_similarities) if basic_similarities else 0)
        tfidf_keyword_divergence = 1 - (sum(tfidf_similarities) / len(tfidf_similarities) if tfidf_similarities else 0)

        # 加权平均两种关键词分歧度
        keyword_divergence = 0.4 * basic_keyword_divergence + 0.6 * tfidf_keyword_divergence

        # 3. 情感差异 - 增强版
        sentiment_divergence = 0.0

        # 扩展法律领域的情感词典
        positive_keywords = [
            "同意", "支持", "认可", "肯定", "是", "构成", "成立", "满足", "符合", "证实",
            "证明", "确认", "确定", "应当", "必须", "明确", "充分", "足够", "有效"
        ]
        negative_keywords = [
            "反对", "否认", "质疑", "否定", "不", "不构成", "不成立", "不满足", "不符合",
            "无法证实", "无法证明", "不确定", "不应当", "不必须", "不明确", "不充分", "不足够", "无效"
        ]

        # 法律推理关键词
        reasoning_keywords = [
            "因为", "所以", "如果", "那么", "但是", "然而", "虽然", "尽管", "假设",
            "推断", "推理", "根据", "依据", "按照", "参照", "考虑", "分析", "判断"
        ]

        sentiments = []
        reasoning_counts = []

        for text in opinion_texts:
            # 情感分析
            pos_count = sum(1 for word in positive_keywords if word in text)
            neg_count = sum(1 for word in negative_keywords if word in text)
            sentiment = (pos_count - neg_count) / (pos_count + neg_count + 1)  # 归一化到[-1, 1]
            sentiments.append(sentiment)

            # 推理复杂度分析
            reasoning_count = sum(1 for word in reasoning_keywords if word in text)
            reasoning_counts.append(reasoning_count / len(text.split()) if text.split() else 0)

        # 情感方差
        sentiment_variance = np.var(sentiments) if sentiments else 0

        # 推理复杂度差异
        reasoning_variance = np.var(reasoning_counts) if reasoning_counts else 0

        # 综合情感分歧度
        sentiment_divergence = 0.7 * sentiment_variance + 0.3 * reasoning_variance

        # 4. 法律观点差异 - 检测法条引用和法律术语
        legal_terms = [
            "法律", "法规", "条例", "规定", "条款", "条文", "第", "条", "款", "项", "解释",
            "司法解释", "判例", "罪", "犯罪", "构成要件", "主观", "客观", "情节", "从重", "从轻"
        ]

        legal_term_counts = []
        for text in opinion_texts:
            count = sum(1 for term in legal_terms if term in text)
            legal_term_counts.append(count / len(text.split()) if text.split() else 0)

        legal_term_variance = np.var(legal_term_counts) if legal_term_counts else 0

        # 检测法条引用模式
        law_article_pattern = r'第[一二三四五六七八九十百千零\d]+条'
        law_article_counts = []

        for text in opinion_texts:
            matches = re.findall(law_article_pattern, text)
            law_article_counts.append(len(matches))

        # 法条引用差异
        if sum(law_article_counts) > 0:
            # 如果有法条引用，计算引用差异
            law_article_variance = np.var(law_article_counts) / (np.mean(law_article_counts) ** 2) if np.mean(law_article_counts) > 0 else 0
        else:
            # 如果没有法条引用，设为0
            law_article_variance = 0

        # 综合法律观点分歧度
        legal_divergence = 0.6 * legal_term_variance + 0.4 * law_article_variance

        # 综合计算分歧程度 (加权平均)
        # 根据辩论层次调整权重
        if self.layer == "FACT":
            # 事实层更关注关键词和情感
            self.divergence_score = 0.2 * length_variance + 0.4 * keyword_divergence + 0.3 * sentiment_divergence + 0.1 * legal_divergence
        elif self.layer == "LAW":
            # 法律适用层更关注法律观点和关键词
            self.divergence_score = 0.1 * length_variance + 0.3 * keyword_divergence + 0.2 * sentiment_divergence + 0.4 * legal_divergence
        elif self.layer == "SENTENCING":
            # 量刑层平衡各因素
            self.divergence_score = 0.1 * length_variance + 0.3 * keyword_divergence + 0.3 * sentiment_divergence + 0.3 * legal_divergence
        else:
            # 默认权重
            self.divergence_score = 0.2 * length_variance + 0.4 * keyword_divergence + 0.2 * sentiment_divergence + 0.2 * legal_divergence

        # 确保分歧分数在[0, 1]范围内
        self.divergence_score = min(1.0, max(0.0, self.divergence_score))

        return self.divergence_score

    def set_consensus(self, consensus: str, score: float):
        """设置达成的共识及共识程度"""
        self.consensus = consensus
        self.consensus_score = score

    def __str__(self):
        return f"DebatePoint(topic='{self.topic}', layer='{self.layer}', importance={self.importance:.2f}, divergence={self.divergence_score:.2f})"

    def to_dict(self):
        """转换为字典表示"""
        return {
            "topic": self.topic,
            "layer": self.layer,
            "importance": self.importance,
            "opinions": self.opinions,
            "divergence_score": self.divergence_score,
            "consensus": self.consensus,
            "consensus_score": self.consensus_score
        }

class AdaptiveDebateFramework:
    """自适应多层次辩论框架的主类"""

    def __init__(self, get_completion_func, case_complexity: float = 0.5):
        """
        初始化辩论框架

        Args:
            get_completion_func: 获取LLM回复的函数
            case_complexity: 案件复杂度 (0.0-1.0)
        """
        self.get_completion = get_completion_func
        self.case_complexity = case_complexity
        self.debate_points = []  # 所有辩论点
        self.current_layer = "FACT"  # 当前辩论层次
        self.layer_results = {  # 各层辩论结果
            "FACT": {"consensus": {}, "completed": False, "metrics": {}},
            "LAW": {"consensus": {}, "completed": False, "metrics": {}},
            "SENTENCING": {"consensus": {}, "completed": False, "metrics": {}}
        }
        self.debate_history = []  # 辩论历史
        self.agents = []  # 参与辩论的智能体
        self.debate_metrics = {  # 辩论过程指标
            "total_rounds": 0,  # 总辩论轮次
            "total_debate_points": 0,  # 总辩论点数
            "avg_consensus_score": 0.0,  # 平均共识程度
            "avg_rounds_per_point": 0.0,  # 每个辩论点的平均轮次
            "layer_metrics": {},  # 各层次指标
            "debate_point_metrics": {},  # 各辩论点指标
            "convergence_rate": 0.0,  # 收敛率（达到收敛条件的辩论点比例）
            "efficiency_score": 0.0  # 辩论效率分数
        }

    def add_agent(self, agent_id: str, agent_role: str):
        """添加参与辩论的智能体"""
        self.agents.append({"id": agent_id, "role": agent_role})

    def detect_debate_points(self, fact: str, accusation: str, agent_responses: Dict[str, str]) -> List[DebatePoint]:
        """
        自动检测辩论点

        Args:
            fact: 案件事实
            accusation: 指控罪名
            agent_responses: 各智能体的初始分析结果

        Returns:
            检测到的辩论点列表
        """
        # 构建提示词，让LLM帮助识别辩论点
        prompt = self._build_debate_points_detection_prompt(fact, accusation, agent_responses)

        # 获取LLM回复
        response = self.get_completion(
            prompt,
            role="debate points analyzer specialized in identifying key disagreements in legal discussions"
        )

        # 解析回复，提取辩论点
        debate_points = self._parse_debate_points(response)

        # 为每个辩论点添加各方观点
        for point in debate_points:
            for agent_id, response in agent_responses.items():
                # 提取该智能体对该辩论点的观点
                opinion = self._extract_opinion_on_topic(response, point.topic)
                if opinion:
                    point.add_opinion(agent_id, opinion)

            # 计算分歧程度
            point.calculate_divergence()

        # 按重要性和分歧程度排序
        debate_points.sort(key=lambda p: p.importance * p.divergence_score, reverse=True)

        self.debate_points = debate_points
        return debate_points

    def _build_debate_points_detection_prompt(self, fact: str, accusation: str, agent_responses: Dict[str, str]) -> str:
        """构建用于检测辩论点的提示词"""
        # 构建各智能体回复的文本
        responses_text = ""
        for agent_id, response in agent_responses.items():
            # 获取智能体角色
            agent_role = next((a["role"] for a in self.agents if a["id"] == agent_id), "专家")
            responses_text += f"【{agent_role}({agent_id})的分析】\n{response}\n\n"

        # 提取案件关键信息，用于辅助分析
        fact_summary = fact[:800] + "..." if len(fact) > 800 else fact

        detection_prompt = "作为一名资深法律辩论点分析专家，请分析以下刑事案件中各方专家观点的主要分歧点。\n\n"
        detection_prompt += "案件事实概要：\n" + fact_summary + "\n\n"
        detection_prompt += "指控罪名：" + accusation + "\n\n"
        detection_prompt += "各方专家分析：\n" + responses_text + "\n\n"
        detection_prompt += """请按照以下三个层次，识别出各方观点中的主要分歧点：

1. 事实层分歧点：
   - 涉及案件基本事实认定的分歧（如行为是否发生、时间地点人物等）
   - 涉及证据采信和证明力的分歧
   - 涉及案件事实因果关系的分歧
   - 涉及主观心理状态认定的分歧

2. 法律适用层分歧点：
   - 涉及罪名认定的分歧（是否构成""" + accusation + """）
   - 涉及犯罪构成要件满足情况的分歧
   - 涉及法律条款解释和适用的分歧
   - 涉及法律责任性质和程度的分歧

3. 量刑层分歧点：
   - 涉及量刑情节认定的分歧（如从重、从轻、减轻情节）
   - 涉及量刑标准和基准刑的分歧
   - 涉及具体刑期确定的分歧
   - 涉及其他处罚措施的分歧

分析要求：
1. 每个层次至少识别1-3个关键分歧点
2. 分歧点应当是实质性的，而非表述差异
3. 分歧点应当对案件结果有重要影响
4. 准确区分不同层次的分歧点，避免混淆

对于每个分歧点，请详细提供：
- 分歧点主题：简明扼要地描述分歧的核心问题
- 所属层次：明确标注为"FACT"、"LAW"或"SENTENCING"
- 重要性评分：0-1之间的数值，1表示对案件结果影响最大
- 各方对该分歧点的具体观点概述：准确提炼各方观点，不要遗漏关键内容
- 分歧程度评估：0-1之间的数值，1表示观点完全对立

请以JSON格式输出，格式如下：
```json
[
  {
    "topic": "分歧点主题",
    "layer": "FACT|LAW|SENTENCING",
    "importance": 0.8,
    "opinions": {
      "agent1": "该智能体对此分歧点的观点概述",
      "agent2": "该智能体对此分歧点的观点概述"
    },
    "divergence_score": 0.7
  },
  ...
]
```

请确保分析全面、客观、专业，不要遗漏重要分歧点，特别是那些可能对案件最终判决结果产生重大影响的分歧。"""

        prompt = detection_prompt

        return prompt

    def _parse_debate_points(self, response: str) -> List[DebatePoint]:
        """从LLM回复中解析辩论点"""
        debate_points = []

        # 提取JSON部分
        json_match = re.search(r'```json\s*([\s\S]*?)\s*```', response)
        if not json_match:
            # 尝试直接解析整个回复
            try:
                # 尝试找到可能的JSON数组开始和结束位置
                array_start = response.find('[')
                array_end = response.rfind(']')

                if array_start != -1 and array_end != -1 and array_end > array_start:
                    potential_json = response[array_start:array_end+1]
                    data = json.loads(potential_json)
                else:
                    # 如果找不到数组标记，尝试解析整个回复
                    data = json.loads(response)
            except:
                logger.warning("无法从回复中解析JSON，尝试使用正则表达式提取")
                # 使用更强大的正则表达式提取关键信息

                # 尝试提取完整的JSON对象
                json_objects = re.findall(r'\{\s*"topic"\s*:\s*"([^"]+)"[^}]+\}', response)
                if json_objects:
                    for json_obj in json_objects:
                        try:
                            # 尝试修复和解析JSON对象
                            fixed_json = '{' + json_obj + '}'
                            fixed_json = re.sub(r'(\w+):', r'"\1":', fixed_json)  # 修复没有引号的键
                            fixed_json = re.sub(r',\s*}', '}', fixed_json)  # 修复尾部逗号
                            item = json.loads(fixed_json)

                            debate_point = DebatePoint(
                                topic=item.get("topic", "未知主题"),
                                layer=item.get("layer", "FACT"),
                                importance=float(item.get("importance", 0.5))
                            )
                            debate_points.append(debate_point)
                        except:
                            continue

                # 如果无法提取完整JSON对象，使用简单正则提取关键字段
                if not debate_points:
                    topics = re.findall(r'"topic"\s*:\s*"([^"]+)"', response)
                    layers = re.findall(r'"layer"\s*:\s*"([^"]+)"', response)
                    importances = re.findall(r'"importance"\s*:\s*([0-9.]+)', response)

                    # 创建辩论点
                    for i in range(min(len(topics), len(layers), len(importances))):
                        try:
                            debate_point = DebatePoint(
                                topic=topics[i],
                                layer=layers[i],
                                importance=float(importances[i])
                            )
                            debate_points.append(debate_point)
                        except:
                            continue

                # 如果仍然无法提取，尝试从文本中识别辩论点
                if not debate_points:
                    # 查找可能的辩论点标题
                    potential_topics = re.findall(r'(?:分歧点|辩论点)\s*\d*\s*[:：]\s*(.+?)(?:\n|$)', response)
                    for i, topic in enumerate(potential_topics):
                        # 尝试确定层次
                        if "事实" in topic or "证据" in topic:
                            layer = "FACT"
                        elif "法律" in topic or "罪名" in topic or "构成" in topic:
                            layer = "LAW"
                        elif "量刑" in topic or "刑期" in topic:
                            layer = "SENTENCING"
                        else:
                            layer = "FACT"  # 默认为事实层

                        debate_point = DebatePoint(
                            topic=topic.strip(),
                            layer=layer,
                            importance=0.5  # 默认中等重要性
                        )
                        debate_points.append(debate_point)

                return debate_points
        else:
            json_str = json_match.group(1)
            try:
                data = json.loads(json_str)
            except json.JSONDecodeError as e:
                logger.error(f"JSON解析失败: {e}")
                # 尝试修复常见的JSON错误
                try:
                    # 修复尾部逗号
                    fixed_json = re.sub(r',\s*]', ']', json_str)
                    # 修复没有引号的键
                    fixed_json = re.sub(r'(\w+):', r'"\1":', fixed_json)
                    data = json.loads(fixed_json)
                except:
                    logger.error("JSON修复失败，无法解析")
                    return debate_points

        # 确保data是列表
        if not isinstance(data, list):
            if isinstance(data, dict):
                data = [data]  # 如果是单个对象，转换为列表
            else:
                logger.error(f"解析的数据不是列表或字典: {type(data)}")
                return debate_points

        # 创建辩论点对象
        for item in data:
            try:
                # 验证必要字段
                if "topic" not in item:
                    logger.warning(f"辩论点缺少topic字段: {item}")
                    continue

                # 规范化层次值
                layer = item.get("layer", "FACT").upper()
                if layer not in ["FACT", "LAW", "SENTENCING"]:
                    # 尝试映射层次
                    if "事实" in layer or "证据" in layer:
                        layer = "FACT"
                    elif "法律" in layer or "罪名" in layer or "构成" in layer:
                        layer = "LAW"
                    elif "量刑" in layer or "刑期" in layer:
                        layer = "SENTENCING"
                    else:
                        layer = "FACT"  # 默认为事实层

                # 创建辩论点
                debate_point = DebatePoint(
                    topic=item["topic"],
                    layer=layer,
                    importance=float(item.get("importance", 0.5))
                )

                # 添加各方观点
                opinions = item.get("opinions", {})
                if isinstance(opinions, dict):
                    for agent_id, opinion in opinions.items():
                        debate_point.add_opinion(agent_id, opinion)
                elif isinstance(opinions, list):
                    # 处理观点可能是列表的情况
                    for i, opinion in enumerate(opinions):
                        if isinstance(opinion, dict) and "agent" in opinion and "opinion" in opinion:
                            debate_point.add_opinion(opinion["agent"], opinion["opinion"])
                        else:
                            debate_point.add_opinion(f"agent{i+1}", str(opinion))

                # 设置分歧程度
                if "divergence_score" in item:
                    try:
                        debate_point.divergence_score = float(item["divergence_score"])
                    except (ValueError, TypeError):
                        # 如果分歧程度不是有效数字，重新计算
                        debate_point.calculate_divergence()
                else:
                    debate_point.calculate_divergence()

                debate_points.append(debate_point)
            except Exception as e:
                logger.error(f"创建辩论点失败: {e}")
                continue

        # 如果没有成功解析任何辩论点，尝试从文本中提取
        if not debate_points and response:
            logger.warning("未能从JSON解析辩论点，尝试从文本中提取")

            # 分割文本为段落
            paragraphs = response.split('\n\n')

            # 查找可能包含辩论点的段落
            for paragraph in paragraphs:
                if any(keyword in paragraph for keyword in ["分歧", "辩论", "争议", "不同意见"]):
                    # 提取可能的主题
                    topic_match = re.search(r'(?:关于|对于|针对)\s*"?([^"]+)"?\s*(?:的|有)', paragraph)
                    if topic_match:
                        topic = topic_match.group(1)

                        # 确定层次
                        if any(keyword in paragraph for keyword in ["事实", "证据", "发生"]):
                            layer = "FACT"
                        elif any(keyword in paragraph for keyword in ["法律", "罪名", "构成"]):
                            layer = "LAW"
                        elif any(keyword in paragraph for keyword in ["量刑", "刑期", "处罚"]):
                            layer = "SENTENCING"
                        else:
                            layer = "FACT"  # 默认为事实层

                        # 创建辩论点
                        debate_point = DebatePoint(
                            topic=topic,
                            layer=layer,
                            importance=0.5  # 默认中等重要性
                        )

                        debate_points.append(debate_point)

        # 确保至少有一个辩论点
        if not debate_points:
            logger.warning("未能解析任何辩论点，创建默认辩论点")
            debate_point = DebatePoint(
                topic="案件主要争议点",
                layer="FACT",
                importance=0.8
            )
            debate_points.append(debate_point)

        return debate_points

    def _extract_opinion_on_topic(self, response: str, topic: str) -> str:
        """从智能体回复中提取对特定辩论点的观点"""
        # 首先尝试使用规则和模式匹配提取观点
        # 1. 检查是否有明确的段落或句子提到该主题

        # 构建可能的主题表述变体
        topic_variants = [
            topic,
            topic.replace("是否", ""),
            topic.replace("的", ""),
            topic.replace("问题", ""),
            topic.replace("情况", "")
        ]

        # 尝试找到包含主题的段落
        paragraphs = response.split('\n\n')
        relevant_paragraphs = []

        for paragraph in paragraphs:
            if any(variant in paragraph for variant in topic_variants):
                relevant_paragraphs.append(paragraph)

        # 如果找到相关段落，直接返回
        if relevant_paragraphs:
            # 如果有多个相关段落，选择最长的一个
            return max(relevant_paragraphs, key=len)

        # 2. 如果没有找到明确的段落，尝试使用关键词上下文提取
        sentences = re.split(r'[。！？；.!?;]', response)
        relevant_sentences = []

        for sentence in sentences:
            if any(variant in sentence for variant in topic_variants):
                relevant_sentences.append(sentence)

        if relevant_sentences:
            # 合并相关句子
            return "。".join(relevant_sentences) + "。"

        # 3. 如果规则方法都失败了，使用LLM提取
        # 构建更精确的提示词，让LLM提取观点
        extract_prompt = "请从以下法律分析文本中精确提取关于\"" + topic + "\"的观点和论述。\n\n"
        extract_prompt += """要求：
1. 只提取与\"""" + topic + """\"直接相关的内容
2. 保留原文的法律术语和表述
3. 如果文本中有多处提及该主题，请综合所有相关内容
4. 如果文本中没有明确提到该主题，请回复"未提及"
5. 不要添加任何不在原文中的内容或解释

文本：
""" + response + """

请直接输出提取的观点，不要添加任何前缀、标题或解释。"""

        prompt = extract_prompt

        # 获取LLM回复
        extracted_opinion = self.get_completion(prompt, role="legal opinion extractor specialized in identifying specific arguments")

        if "未提及" in extracted_opinion:
            return ""

        return extracted_opinion

    def conduct_debate_round(self, debate_point: DebatePoint, previous_rounds: List[Dict] = None) -> Dict:
        """
        针对特定辩论点进行一轮辩论

        Args:
            debate_point: 要辩论的争议点
            previous_rounds: 之前的辩论轮次结果

        Returns:
            辩论结果，包含各方观点和达成的共识
        """
        # 构建辩论提示词
        prompt = self._build_debate_round_prompt(debate_point, previous_rounds)

        # 获取LLM回复
        response = self.get_completion(
            prompt,
            role="senior legal debate moderator specialized in facilitating structured debates and consensus building"
        )

        # 解析辩论结果
        debate_result = self._parse_debate_result(response, debate_point)

        # 更新辩论历史
        self.debate_history.append({
            "debate_point": debate_point.to_dict(),
            "result": debate_result
        })

        return debate_result

    def _build_debate_round_prompt(self, debate_point: DebatePoint, previous_rounds: List[Dict] = None) -> str:
        """构建辩论轮次的提示词"""
        # 获取当前层次信息
        layer_info = DEBATE_LAYERS[debate_point.layer]

        # 获取当前轮次
        current_round = len(previous_rounds) + 1 if previous_rounds else 1

        # 构建各方观点的文本
        opinions_text = ""
        for agent_id, opinion in debate_point.opinions.items():
            agent_role = next((a["role"] for a in self.agents if a["id"] == agent_id), "专家")
            opinions_text += f"【{agent_role}({agent_id})的观点】\n{opinion}\n\n"

        # 构建之前轮次的辩论历史
        history_text = ""
        previous_focus = None

        if previous_rounds:
            for i, round_result in enumerate(previous_rounds):
                history_text += f"### 第{i+1}轮辩论\n"
                history_text += f"交叉质询：\n{round_result.get('cross_examination', '无')}\n\n"

                # 添加各方回应
                responses = round_result.get('responses', {})
                if isinstance(responses, dict):
                    for agent_id, response in responses.items():
                        agent_role = next((a["role"] for a in self.agents if a["id"] == agent_id), "专家")
                        history_text += f"【{agent_role}({agent_id})的回应】\n{response}\n\n"
                else:
                    history_text += f"各方回应：\n{responses}\n\n"

                # 添加阶段性共识
                consensus = round_result.get('interim_consensus', '无')
                history_text += f"阶段性共识：\n{consensus}\n\n"

                # 记录最近一轮的焦点
                if i == len(previous_rounds) - 1:
                    previous_focus = round_result.get('next_round_focus', '')

                # 添加剩余分歧
                if 'remaining_disagreements' in round_result:
                    history_text += f"剩余分歧：\n{round_result['remaining_disagreements']}\n\n"

                # 添加共识程度
                if 'consensus_score' in round_result:
                    history_text += f"共识程度：{round_result['consensus_score']:.2f}\n\n"

        # 根据当前轮次和层次调整辩论策略
        debate_strategy = ""
        if current_round == 1:
            # 第一轮：明确分歧点
            debate_strategy = "这是第一轮辩论，请重点关注明确各方观点的具体分歧，并尝试找出共同点。"
        elif current_round == 2:
            # 第二轮：深入分析
            debate_strategy = "这是第二轮辩论，请深入分析上一轮辩论中发现的关键分歧点，并尝试缩小分歧。"
        elif current_round >= 3:
            # 第三轮及以后：寻求共识
            debate_strategy = f"这已经是第{current_round}轮辩论，请重点关注达成最终共识，尽量解决剩余的关键分歧。"

        # 根据层次添加特定指导
        if debate_point.layer == "FACT":
            debate_strategy += "\n作为事实层辩论，请特别关注证据的可靠性、完整性和关联性，以及事实认定的准确性。"
        elif debate_point.layer == "LAW":
            debate_strategy += "\n作为法律适用层辩论，请特别关注法律条款的解释、适用条件和构成要件的满足情况。"
        elif debate_point.layer == "SENTENCING":
            debate_strategy += "\n作为量刑层辩论，请特别关注量刑情节的认定、基准刑的确定和量刑调整的合理性。"

        # 如果有上一轮的焦点，添加到策略中
        if previous_focus:
            debate_strategy += f"\n\n根据上一轮辩论结果，本轮应重点关注：{previous_focus}"

        debate_round_prompt = "作为一名资深法律辩论主持人，请就以下辩论点组织第" + str(current_round) + "轮结构化辩论，目标是通过交叉质询和反驳，促进各方达成共识或缩小分歧。\n\n"
        debate_round_prompt += "辩论层次：" + layer_info['name'] + "（" + layer_info['description'] + "）\n"
        debate_round_prompt += "辩论主题：" + debate_point.topic + "\n"
        debate_round_prompt += f"重要性：{debate_point.importance:.2f}（满分1.0）\n"
        debate_round_prompt += f"分歧程度：{debate_point.divergence_score:.2f}（满分1.0）\n\n"
        debate_round_prompt += "各方初始观点：\n" + opinions_text + "\n"

        if history_text:
            debate_round_prompt += "之前的辩论历史：\n" + history_text + "\n"

        debate_round_prompt += "辩论策略指导：\n" + debate_strategy + "\n\n"
        debate_round_prompt += """请按照以下结构组织本轮辩论：

1. 交叉质询环节：
   - 针对各方观点中的关键分歧点提出尖锐问题
   - 质疑观点中的事实错误、法律误用或逻辑谬误
   - 要求各方澄清模糊表述或提供更多支持证据
   - 特别关注上一轮辩论中未解决的问题

2. 各方回应环节：
   - 各方对质询的详细回应
   - 对其他方观点的有理有据的反驳
   - 对自身观点的修正或补充
   - 提供更多支持自己观点的法律依据或事实证据

3. 共识形成环节：
   - 总结各方观点中的共同点
   - 分析分歧是否缩小，量化分歧程度的变化
   - 尝试形成各方都能接受的共识表述
   - 评估共识程度（0-1，1表示完全共识）
   - 如果无法达成完全共识，明确剩余分歧的具体内容和原因

请以JSON格式输出辩论结果，格式如下：
```json
{
  "cross_examination": "交叉质询的详细内容",
  "responses": {
    "agent1": "该方对质询的回应",
    "agent2": "该方对质询的回应"
  },
  "interim_consensus": "本轮形成的阶段性共识",
  "consensus_score": 0.7,
  "remaining_disagreements": "仍然存在的分歧",
  "next_round_focus": "下一轮应关注的焦点（如果需要继续辩论）",
  "consensus_progress": "相比上一轮的共识进展描述"
}
```

请确保辩论过程专业、客观、公正，不偏向任何一方，严格基于法律规定和案件事实。辩论应当深入、有实质性，避免表面化和形式化。"""

        prompt = debate_round_prompt

        return prompt

    def _parse_debate_result(self, response: str, debate_point: DebatePoint) -> Dict:
        """从LLM回复中解析辩论结果"""
        # 提取JSON部分
        json_match = re.search(r'```json\s*([\s\S]*?)\s*```', response)
        if json_match:
            json_str = json_match.group(1)
            try:
                result = json.loads(json_str)
                return result
            except json.JSONDecodeError as e:
                logger.error(f"JSON解析失败: {e}")
                # 尝试修复常见的JSON错误
                try:
                    # 修复尾部逗号
                    fixed_json = re.sub(r',\s*}', '}', json_str)
                    # 修复没有引号的键
                    fixed_json = re.sub(r'(\w+):', r'"\1":', fixed_json)
                    # 修复单引号
                    fixed_json = fixed_json.replace("'", '"')
                    result = json.loads(fixed_json)
                    return result
                except:
                    logger.error("JSON修复失败，使用正则表达式提取")

        # 如果无法解析JSON，尝试使用正则表达式提取关键信息
        result = {}

        # 提取交叉质询
        cross_exam_match = re.search(r'交叉质询[环节]*[：:]\s*([\s\S]*?)(?=各方回应[环节]*[：:]|共识形成[环节]*[：:]|$)', response)
        if cross_exam_match:
            result["cross_examination"] = cross_exam_match.group(1).strip()

        # 提取各方回应
        responses = {}
        for agent in self.agents:
            agent_id = agent["id"]
            agent_role = agent["role"]
            pattern = fr'{agent_role}[（(]?{agent_id}[）)]?的回应[：:]\s*([\s\S]*?)(?=\n\n|\n[^的]|$)'
            response_match = re.search(pattern, response)
            if response_match:
                responses[agent_id] = response_match.group(1).strip()
        result["responses"] = responses

        # 提取共识
        consensus_match = re.search(r'(共识|一致意见|阶段性共识)[：:]\s*([\s\S]*?)(?=\n\n|\n[^的]|分歧|$)', response)
        if consensus_match:
            result["interim_consensus"] = consensus_match.group(2).strip()

        # 提取共识程度
        score_match = re.search(r'共识程度[：:]?\s*([0-9.]+)', response)
        if score_match:
            try:
                result["consensus_score"] = float(score_match.group(1))
            except:
                result["consensus_score"] = 0.0

        # 提取剩余分歧
        disagree_match = re.search(r'(剩余分歧|仍然存在的分歧)[：:]\s*([\s\S]*?)(?=\n\n|\n[^的]|下一轮|$)', response)
        if disagree_match:
            result["remaining_disagreements"] = disagree_match.group(2).strip()

        # 提取下一轮焦点
        focus_match = re.search(r'(下一轮[应该]*关注|下一轮焦点)[：:]\s*([\s\S]*?)(?=\n\n|\n[^的]|$)', response)
        if focus_match:
            result["next_round_focus"] = focus_match.group(2).strip()

        # 提取共识进展
        progress_match = re.search(r'(共识进展|相比上一轮)[：:]\s*([\s\S]*?)(?=\n\n|\n[^的]|$)', response)
        if progress_match:
            result["consensus_progress"] = progress_match.group(2).strip()

        # 确保结果中包含必要的字段
        if "consensus_score" not in result:
            result["consensus_score"] = 0.0

        if "interim_consensus" not in result and "cross_examination" in result:
            # 如果有交叉质询但没有提取到共识，尝试从整个回复中提取
            consensus_prompt = "从以下辩论内容中提取关于\"" + debate_point.topic + "\"的共识结论。如果没有明确的共识，请回复\"未达成共识\"。\n\n"
            consensus_prompt += "辩论内容：\n" + response + "\n\n"
            consensus_prompt += "请直接输出共识内容，不要添加任何前缀或解释。"

            try:
                extracted_consensus = self.get_completion(consensus_prompt, role="consensus extractor")
                if "未达成共识" not in extracted_consensus:
                    result["interim_consensus"] = extracted_consensus
                    # 估计共识程度
                    result["consensus_score"] = 0.3  # 默认较低的共识程度
            except:
                logger.error("提取共识失败")
                result["interim_consensus"] = "未能提取共识"

        return result

    def debate_layer(self, layer: str, fact: str, accusation: str, agent_responses: Dict[str, str]) -> Dict:
        """
        针对特定层次进行辩论

        Args:
            layer: 辩论层次 (FACT, LAW, SENTENCING)
            fact: 案件事实
            accusation: 指控罪名
            agent_responses: 各智能体的初始分析结果

        Returns:
            该层次的辩论结果，包含达成的共识
        """
        # 获取层次信息
        layer_info = DEBATE_LAYERS[layer]

        # 检查依赖层次是否已完成
        for dep_layer in layer_info.get("dependencies", []):
            if not self.layer_results.get(dep_layer, {}).get("completed", False):
                logger.warning(f"层次 {layer} 的依赖层次 {dep_layer} 尚未完成辩论，无法进行当前层次辩论")
                self.layer_results[layer]["status"] = "dependency_not_met"
                self.layer_results[layer]["message"] = f"依赖层次 {DEBATE_LAYERS[dep_layer]['name']} 尚未完成辩论"
                return self.layer_results[layer]

            # 检查依赖层次是否达到了足够的共识
            dep_consensus_items = self.layer_results[dep_layer].get("consensus", {}).items()
            if not dep_consensus_items:
                logger.warning(f"层次 {layer} 的依赖层次 {dep_layer} 未达成任何共识，无法进行当前层次辩论")
                self.layer_results[layer]["status"] = "dependency_no_consensus"
                self.layer_results[layer]["message"] = f"依赖层次 {DEBATE_LAYERS[dep_layer]['name']} 未达成任何共识"
                return self.layer_results[layer]

            # 检查依赖层次的关键输出是否存在
            if "key_outputs" in DEBATE_LAYERS[dep_layer] and "key_outputs" not in self.layer_results[dep_layer]:
                logger.warning(f"层次 {layer} 的依赖层次 {dep_layer} 缺少关键输出，无法进行当前层次辩论")
                self.layer_results[layer]["status"] = "dependency_missing_key_outputs"
                self.layer_results[layer]["message"] = f"依赖层次 {DEBATE_LAYERS[dep_layer]['name']} 缺少关键输出"
                return self.layer_results[layer]

        # 获取该层次的辩论点
        layer_debate_points = [p for p in self.debate_points if p.layer == layer]

        if not layer_debate_points:
            logger.warning(f"层次 {layer} 没有检测到辩论点")

            # 如果没有辩论点，尝试自动生成
            if self._should_generate_debate_points(layer):
                logger.info(f"尝试为层次 {layer} 自动生成辩论点")
                generated_points = self._generate_debate_points_for_layer(layer, fact, accusation, agent_responses)
                if generated_points:
                    layer_debate_points = generated_points
                    # 将生成的辩论点添加到总辩论点列表中
                    self.debate_points.extend(generated_points)
                    logger.info(f"成功为层次 {layer} 生成 {len(generated_points)} 个辩论点")
                else:
                    logger.warning(f"无法为层次 {layer} 生成辩论点")
                    self.layer_results[layer]["completed"] = True
                    self.layer_results[layer]["status"] = "no_debate_points"
                    self.layer_results[layer]["message"] = f"未检测到辩论点，且无法自动生成"
                    return self.layer_results[layer]
            else:
                self.layer_results[layer]["completed"] = True
                self.layer_results[layer]["status"] = "no_debate_points"
                self.layer_results[layer]["message"] = f"未检测到辩论点"
                return self.layer_results[layer]

        # 初始化层次状态
        self.layer_results[layer]["status"] = "in_progress"
        self.layer_results[layer]["message"] = f"开始 {layer_info['name']} 辩论"

        # 根据案件复杂度和辩论点重要性调整辩论轮次
        for debate_point in layer_debate_points:
            # 计算该辩论点的最大辩论轮次
            importance_factor = debate_point.importance
            divergence_factor = debate_point.divergence_score
            complexity_factor = self.case_complexity

            # 获取该层次的特性

            # 计算层次权重 - 事实层和法律适用层更重要
            layer_weight = 1.0
            if layer == "FACT":
                layer_weight = 1.2  # 事实层权重最高
            elif layer == "LAW":
                layer_weight = 1.1  # 法律适用层权重次之
            elif layer == "SENTENCING":
                layer_weight = 0.9  # 量刑层权重较低

            # 计算辩论点特征分数
            # 使用非线性函数增强重要性和分歧程度的影响
            importance_score = importance_factor ** 0.8  # 减小极端值的影响
            divergence_score = divergence_factor ** 0.7  # 减小极端值的影响

            # 计算辩论需求度 - 综合考虑多个因素
            debate_necessity = (
                0.35 * importance_score +  # 重要性
                0.35 * divergence_score +  # 分歧程度
                0.15 * complexity_factor +  # 案件复杂度
                0.15 * (1.0 - (len(debate_point.opinions) / len(self.agents) if self.agents else 1.0))  # 观点覆盖率
            ) * layer_weight  # 应用层次权重

            # 动态计算最大辩论轮次
            # 使用对数函数使轮次增长更加平滑
            rounds_range = layer_info["max_rounds"] - layer_info["min_rounds"]
            max_rounds = max(
                layer_info["min_rounds"],
                min(
                    layer_info["max_rounds"],
                    int(layer_info["min_rounds"] + rounds_range * (
                        1.0 - math.exp(-2.5 * debate_necessity)  # 指数函数使增长更加敏感
                    ))
                )
            )

            # 记录计算过程
            logger.debug(f"辩论点 '{debate_point.topic}' 的辩论轮次计算:")
            logger.debug(f"  - 重要性: {importance_factor:.2f}, 分歧程度: {divergence_factor:.2f}")
            logger.debug(f"  - 案件复杂度: {complexity_factor:.2f}, 层次权重: {layer_weight:.2f}")
            logger.debug(f"  - 辩论需求度: {debate_necessity:.2f}, 最大轮次: {max_rounds}")

            logger.info(f"辩论点 '{debate_point.topic}' 将进行最多 {max_rounds} 轮辩论")

            # 进行多轮辩论
            previous_rounds = []
            final_consensus = None
            final_consensus_score = 0.0

            for round_num in range(max_rounds):
                logger.info(f"开始第 {round_num + 1} 轮辩论，主题: '{debate_point.topic}'")

                # 进行一轮辩论
                round_result = self.conduct_debate_round(debate_point, previous_rounds)
                previous_rounds.append(round_result)

                # 获取本轮共识和共识程度
                consensus = round_result.get("interim_consensus", "")
                consensus_score = round_result.get("consensus_score", 0.0)

                # 更新最终共识
                if consensus and consensus_score > final_consensus_score:
                    final_consensus = consensus
                    final_consensus_score = consensus_score

                # 计算自适应收敛阈值
                # 随着轮次增加，适当降低收敛阈值，避免无限辩论
                base_threshold = layer_info["convergence_threshold"]
                rounds_factor = min(1.0, (round_num + 1) / max_rounds)  # 轮次因子，随轮次增加而增加
                importance_factor = debate_point.importance  # 重要性因子

                # 自适应收敛阈值计算
                # 1. 重要性高的辩论点需要更高的共识度
                # 2. 轮次越多，收敛阈值适当降低
                # 3. 不同层次有不同的基础阈值
                adaptive_threshold = base_threshold * (
                    1.0 - (0.15 * rounds_factor * (1.0 - importance_factor))
                )

                # 记录自适应阈值计算过程
                logger.debug(f"辩论点 '{debate_point.topic}' 第 {round_num + 1} 轮的收敛阈值计算:")
                logger.debug(f"  - 基础阈值: {base_threshold:.2f}, 轮次因子: {rounds_factor:.2f}")
                logger.debug(f"  - 重要性因子: {importance_factor:.2f}, 自适应阈值: {adaptive_threshold:.2f}")
                logger.debug(f"  - 当前共识程度: {consensus_score:.2f}")

                # 检查是否达到收敛条件
                if consensus_score >= adaptive_threshold:
                    logger.info(f"辩论点 '{debate_point.topic}' 在第 {round_num + 1} 轮达到收敛条件，共识程度: {consensus_score:.2f}, 阈值: {adaptive_threshold:.2f}")
                    break

                # 检查辩论进展情况
                if round_num > 0 and previous_rounds:
                    # 计算共识进展
                    prev_consensus_score = previous_rounds[-1].get("consensus_score", 0.0)
                    consensus_progress = consensus_score - prev_consensus_score

                    # 如果连续两轮共识进展很小，考虑提前结束辩论
                    if round_num > 1 and consensus_progress < 0.05:
                        prev_progress = previous_rounds[-1].get("consensus_score", 0.0) - previous_rounds[-2].get("consensus_score", 0.0)
                        if prev_progress < 0.05:
                            logger.info(f"辩论点 '{debate_point.topic}' 在第 {round_num + 1} 轮检测到共识进展停滞，提前结束辩论")
                            logger.info(f"  - 前一轮进展: {prev_progress:.2f}, 当前轮进展: {consensus_progress:.2f}")
                            break

                # 检查是否还有继续辩论的必要
                if not round_result.get("next_round_focus"):
                    logger.info(f"辩论点 '{debate_point.topic}' 在第 {round_num + 1} 轮结束辩论，无需继续（无下一轮焦点）")
                    break

                # 检查是否达到最大轮次
                if round_num == max_rounds - 1:
                    logger.info(f"辩论点 '{debate_point.topic}' 达到最大轮次 {max_rounds}，结束辩论")
                    break

            # 设置辩论点的最终共识
            debate_point.set_consensus(final_consensus, final_consensus_score)

            # 将共识添加到层次结果中
            self.layer_results[layer]["consensus"][debate_point.topic] = {
                "content": final_consensus,
                "score": final_consensus_score
            }

        # 标记该层次辩论完成
        self.layer_results[layer]["completed"] = True

        # 生成层次总结
        self.layer_results[layer]["summary"] = self._generate_layer_summary(layer, fact, accusation)

        return self.layer_results[layer]

    def _generate_layer_summary(self, layer: str, fact: str, accusation: str) -> str:
        """生成层次辩论的总结"""
        # 获取该层次的所有共识
        consensus_items = self.layer_results[layer]["consensus"].items()
        if not consensus_items:
            return "本层次未达成任何共识。"

        # 构建共识文本
        consensus_text = ""
        for topic, consensus_info in consensus_items:
            consensus_text += f"关于\"{topic}\"的共识（共识程度：{consensus_info['score']:.2f}）：\n{consensus_info['content']}\n\n"

        # 获取层次信息
        layer_info = DEBATE_LAYERS[layer]

        # 获取依赖层次的总结
        dependencies_text = ""
        for dep_layer in layer_info.get("dependencies", []):
            if dep_layer in self.layer_results and "summary" in self.layer_results[dep_layer]:
                dep_layer_info = DEBATE_LAYERS[dep_layer]
                dependencies_text += f"【{dep_layer_info['name']}总结】\n{self.layer_results[dep_layer]['summary']}\n\n"

        # 获取层间依赖关系
        layer_dependencies_text = ""
        for dep_key, dep_info in LAYER_DEPENDENCIES.items():
            if dep_key.startswith(f"{layer}_to_") or dep_key.endswith(f"_to_{layer}"):
                layer_dependencies_text += f"【{dep_info['description']}】\n"
                layer_dependencies_text += f"关键信息: {', '.join(dep_info['key_information'])}\n"
                layer_dependencies_text += f"影响: {dep_info['impact']}\n\n"

        # 构建关键输出要求
        key_outputs_text = ""
        if "key_outputs" in layer_info:
            key_outputs_text = "需要明确输出以下关键信息：\n"
            for output in layer_info["key_outputs"]:
                key_outputs_text += f"- {output}\n"

        # 构建提示词
        layer_summary_prompt = "作为一名资深法律专家，请根据以下辩论共识和相关层次信息，生成" + layer_info['name'] + "的综合总结。\n\n"
        layer_summary_prompt += "案件事实概述：\n" + fact[:500] + "...\n\n"
        layer_summary_prompt += "指控罪名：" + accusation + "\n\n"
        layer_summary_prompt += layer_info['name'] + "辩论共识：\n" + consensus_text + "\n"

        if dependencies_text:
            layer_summary_prompt += "前序层次总结：\n" + dependencies_text + "\n"

        if layer_dependencies_text:
            layer_summary_prompt += "层次间关键信息传递：\n" + layer_dependencies_text + "\n"

        layer_summary_prompt += key_outputs_text + "\n"

        layer_summary_prompt += "请生成一份全面、客观、专业的" + layer_info['name'] + "总结，要求：\n"
        layer_summary_prompt += "1. 整合所有达成的共识，形成连贯的分析\n"
        layer_summary_prompt += "2. 清晰呈现该层次的关键结论，特别是" + ', '.join(layer_info.get('key_outputs', ['关键结论'])) + "\n"
        layer_summary_prompt += """3. 保持法律专业性和准确性，引用相关法律条款
4. 确保与前序层次的结论保持一致性和逻辑连贯性
5. 为下一层次的辩论奠定基础，提供必要的信息支持
6. 如果存在未能达成共识的重要问题，请明确指出

总结应当结构清晰，逻辑严密，论证充分，语言精炼。请直接输出总结内容，不要添加额外的前缀或说明。"""

        prompt = layer_summary_prompt

        # 获取LLM回复
        summary = self.get_completion(
            prompt,
            role=f"senior legal expert specialized in {layer_info['name']} with expertise in integrating multi-level legal analysis"
        )

        # 保存层次总结到结果中
        self.layer_results[layer]["summary"] = summary

        # 提取并保存关键输出
        if "key_outputs" in layer_info:
            key_outputs_result = {}

            # 为每个关键输出构建提取提示词
            for output in layer_info["key_outputs"]:
                extract_prompt = "从以下" + layer_info['name'] + "总结中，精确提取关于\"" + output + "\"的具体结论。\n\n"
                extract_prompt += "总结内容：\n" + summary + "\n\n"
                extract_prompt += "请直接输出提取的结论，不要添加任何前缀或解释。如果没有找到相关结论，请回复\"未找到相关结论\"。"

                # 提取关键输出
                extracted_output = self.get_completion(
                    extract_prompt,
                    role="legal information extractor specialized in identifying specific conclusions"
                )

                if "未找到" not in extracted_output:
                    key_outputs_result[output] = extracted_output

            # 保存关键输出到结果中
            self.layer_results[layer]["key_outputs"] = key_outputs_result

        return summary

    def _should_generate_debate_points(self, layer: str) -> bool:
        """
        判断是否应该为指定层次自动生成辩论点

        Args:
            layer: 辩论层次 (FACT, LAW, SENTENCING)

        Returns:
            是否应该生成辩论点
        """
        # 获取层次信息
        layer_info = DEBATE_LAYERS[layer]

        # 检查是否有依赖层次
        if not layer_info.get("dependencies", []):
            # 如果是事实层（没有依赖），通常不自动生成辩论点
            return False

        # 检查所有依赖层次是否已完成且有共识
        for dep_layer in layer_info.get("dependencies", []):
            if not self.layer_results.get(dep_layer, {}).get("completed", False):
                return False

            # 检查依赖层次是否有共识
            dep_consensus_items = self.layer_results[dep_layer].get("consensus", {}).items()
            if not dep_consensus_items:
                return False

        # 如果所有依赖层次都已完成且有共识，则可以尝试自动生成辩论点
        return True

    def _generate_debate_points_for_layer(self, layer: str, fact: str, accusation: str, agent_responses: Dict[str, str]) -> List[DebatePoint]:
        """
        为指定层次自动生成辩论点

        Args:
            layer: 辩论层次 (FACT, LAW, SENTENCING)
            fact: 案件事实
            accusation: 指控罪名
            agent_responses: 各智能体的初始分析结果

        Returns:
            生成的辩论点列表
        """
        # 获取层次信息
        layer_info = DEBATE_LAYERS[layer]

        # 获取依赖层次的总结和关键输出
        dependencies_text = ""
        key_outputs_text = ""

        for dep_layer in layer_info.get("dependencies", []):
            if dep_layer in self.layer_results and "summary" in self.layer_results[dep_layer]:
                dep_layer_info = DEBATE_LAYERS[dep_layer]
                dependencies_text += f"【{dep_layer_info['name']}总结】\n{self.layer_results[dep_layer]['summary']}\n\n"

                # 添加关键输出
                if "key_outputs" in self.layer_results[dep_layer]:
                    key_outputs_text += f"【{dep_layer_info['name']}关键输出】\n"
                    for output_name, output_content in self.layer_results[dep_layer]["key_outputs"].items():
                        key_outputs_text += f"{output_name}: {output_content}\n"
                    key_outputs_text += "\n"

        # 构建提示词，根据层次特点生成辩论点
        debate_points_prompt = "作为一名资深法律辩论点分析专家，请根据以下信息，为" + layer_info['name'] + "自动生成关键辩论点。\n\n"
        debate_points_prompt += "案件事实概述：\n" + fact[:500] + "...\n\n"
        debate_points_prompt += "指控罪名：" + accusation + "\n\n"

        if dependencies_text:
            debate_points_prompt += "前序层次总结：\n" + dependencies_text + "\n"

        if key_outputs_text:
            debate_points_prompt += "前序层次关键输出：\n" + key_outputs_text + "\n"

        debate_points_prompt += layer_info['name'] + "需要关注的关键方面：\n"
        debate_points_prompt += ', '.join(layer_info.get('key_aspects', [])) + "\n\n"

        debate_points_prompt += "请根据" + layer_info['name'] + "的特点和前序层次的结论，生成2-3个关键辩论点。辩论点应当：\n"
        debate_points_prompt += "1. 聚焦于" + layer_info['name'] + "的核心问题\n"
        debate_points_prompt += "2. 与前序层次的结论保持一致性\n"
        debate_points_prompt += "3. 对案件最终判决有重要影响\n"
        debate_points_prompt += "4. 具有一定的争议性，值得辩论\n\n"

        debate_points_prompt += "请以JSON格式输出，格式如下：\n"
        debate_points_prompt += "```json\n"
        debate_points_prompt += "[\n"
        debate_points_prompt += "  {\n"
        debate_points_prompt += "    \"topic\": \"辩论点主题\",\n"
        debate_points_prompt += "    \"layer\": \"" + layer + "\",\n"
        debate_points_prompt += "    \"importance\": 0.8,\n"
        debate_points_prompt += "    \"description\": \"对该辩论点的详细描述\"\n"
        debate_points_prompt += "  },\n"
        debate_points_prompt += "  ...\n"
        debate_points_prompt += "]\n"
        debate_points_prompt += "```\n\n"

        debate_points_prompt += "请确保生成的辩论点专业、客观、全面，能够推动" + layer_info['name'] + "的深入讨论。"

        prompt = debate_points_prompt

        # 获取LLM回复
        response = self.get_completion(
            prompt,
            role=f"debate points generator specialized in {layer_info['name']} issues"
        )

        # 解析回复，提取辩论点
        debate_points = []

        # 提取JSON部分
        json_match = re.search(r'```json\s*([\s\S]*?)\s*```', response)
        if json_match:
            json_str = json_match.group(1)
            try:
                data = json.loads(json_str)

                # 创建辩论点对象
                for item in data:
                    if "topic" in item and "layer" in item:
                        debate_point = DebatePoint(
                            topic=item["topic"],
                            layer=layer,  # 确保使用正确的层次
                            importance=float(item.get("importance", 0.7))  # 默认较高重要性
                        )

                        # 为每个智能体添加初始观点
                        for agent_id, response in agent_responses.items():
                            # 提取该智能体对该辩论点的观点
                            opinion = self._extract_opinion_on_topic(response, debate_point.topic)
                            if opinion:
                                debate_point.add_opinion(agent_id, opinion)
                            else:
                                # 如果无法提取观点，生成一个基于辩论点描述的初始观点
                                generated_opinion = self._generate_initial_opinion(
                                    agent_id,
                                    debate_point.topic,
                                    item.get("description", ""),
                                    layer
                                )
                                debate_point.add_opinion(agent_id, generated_opinion)

                        # 计算分歧程度
                        debate_point.calculate_divergence()

                        debate_points.append(debate_point)
            except:
                logger.error(f"解析自动生成的辩论点失败")

        return debate_points

    def _generate_initial_opinion(self, agent_id: str, topic: str, description: str, layer: str) -> str:
        """
        为智能体生成关于辩论点的初始观点

        Args:
            agent_id: 智能体ID
            topic: 辩论点主题
            description: 辩论点描述
            layer: 辩论层次

        Returns:
            生成的初始观点
        """
        # 获取智能体角色
        agent_role = next((a["role"] for a in self.agents if a["id"] == agent_id), "专家")

        # 获取层次信息
        layer_info = DEBATE_LAYERS[layer]

        # 构建提示词
        prompt = f"""作为一名{agent_role}，请针对以下{layer_info['name']}辩论点生成一个初始观点。

辩论点：{topic}
辩论点描述：{description}

请从{agent_role}的专业角度，对该辩论点提出一个合理的观点。观点应当：
1. 符合{agent_role}的专业视角
2. 具有法律专业性和逻辑性
3. 长度适中（100-200字）
4. 可能与其他专家存在一定分歧

请直接输出观点内容，不要添加任何前缀或说明。"""

        # 获取LLM回复
        opinion = self.get_completion(
            prompt,
            role=f"{agent_role} specialized in criminal law"
        )

        return opinion

    def _update_debate_metrics(self):
        """更新辩论指标"""
        # 重置指标
        self.debate_metrics = {
            "total_rounds": 0,
            "total_debate_points": len(self.debate_points),
            "avg_consensus_score": 0.0,
            "avg_rounds_per_point": 0.0,
            "layer_metrics": {
                "FACT": {"points": 0, "rounds": 0, "avg_consensus": 0.0},
                "LAW": {"points": 0, "rounds": 0, "avg_consensus": 0.0},
                "SENTENCING": {"points": 0, "rounds": 0, "avg_consensus": 0.0}
            },
            "debate_point_metrics": {},
            "convergence_rate": 0.0,
            "efficiency_score": 0.0
        }

        # 统计各层次和辩论点指标
        total_consensus_score = 0.0
        converged_points = 0
        total_rounds = 0

        for point in self.debate_points:
            # 获取该辩论点的辩论历史
            point_history = [h for h in self.debate_history if h["debate_point"]["topic"] == point.topic]
            rounds_count = len(point_history)

            # 更新总轮次
            total_rounds += rounds_count

            # 更新层次指标
            layer = point.layer
            self.debate_metrics["layer_metrics"][layer]["points"] += 1
            self.debate_metrics["layer_metrics"][layer]["rounds"] += rounds_count

            # 更新共识分数
            if point.consensus_score > 0:
                total_consensus_score += point.consensus_score
                self.debate_metrics["layer_metrics"][layer]["avg_consensus"] += point.consensus_score

                # 检查是否达到收敛
                layer_info = DEBATE_LAYERS[layer]
                if point.consensus_score >= layer_info["convergence_threshold"]:
                    converged_points += 1

            # 记录辩论点指标
            self.debate_metrics["debate_point_metrics"][point.topic] = {
                "importance": point.importance,
                "divergence": point.divergence_score,
                "consensus_score": point.consensus_score,
                "rounds": rounds_count,
                "layer": layer,
                "efficiency": point.consensus_score / max(1, rounds_count)  # 每轮平均共识增长
            }

        # 计算平均值
        if self.debate_points:
            self.debate_metrics["avg_consensus_score"] = total_consensus_score / len(self.debate_points)
            self.debate_metrics["avg_rounds_per_point"] = total_rounds / len(self.debate_points)
            self.debate_metrics["convergence_rate"] = converged_points / len(self.debate_points)

            # 计算各层平均共识
            for layer in ["FACT", "LAW", "SENTENCING"]:
                layer_points = self.debate_metrics["layer_metrics"][layer]["points"]
                if layer_points > 0:
                    self.debate_metrics["layer_metrics"][layer]["avg_consensus"] /= layer_points

        # 计算辩论效率分数
        # 效率 = 平均共识分数 / 平均轮次 * 收敛率
        if self.debate_metrics["avg_rounds_per_point"] > 0:
            self.debate_metrics["efficiency_score"] = (
                self.debate_metrics["avg_consensus_score"] /
                self.debate_metrics["avg_rounds_per_point"] *
                (0.5 + 0.5 * self.debate_metrics["convergence_rate"])  # 收敛率影响效率，但不是唯一因素
            )

        # 更新各层结果中的指标
        for layer in ["FACT", "LAW", "SENTENCING"]:
            self.layer_results[layer]["metrics"] = self.debate_metrics["layer_metrics"][layer]

        return self.debate_metrics

    def conduct_full_debate(self, fact: str, accusation: str, agent_responses: Dict[str, str]) -> Dict:
        """
        进行完整的三层辩论

        Args:
            fact: 案件事实
            accusation: 指控罪名
            agent_responses: 各智能体的初始分析结果

        Returns:
            完整的辩论结果，包含各层次的共识和最终结论
        """
        # 初始化结果
        result = {
            "status": "in_progress",
            "message": "开始辩论",
            "debate_points": [],
            "layer_results": {},
            "final_conclusion": {},
            "metrics": {},
            "debate_duration": 0
        }

        # 检测辩论点
        self.detect_debate_points(fact, accusation, agent_responses)

        if not self.debate_points:
            logger.warning("未检测到任何辩论点")
            return {
                "status": "failed",
                "message": "未检测到任何辩论点",
                "results": self.layer_results
            }

        # 记录开始时间
        start_time = time.time()

        # 按层次顺序进行辩论
        layer_status = {}
        for layer in ["FACT", "LAW", "SENTENCING"]:
            logger.info(f"开始 {DEBATE_LAYERS[layer]['name']} 辩论")
            layer_result = self.debate_layer(layer, fact, accusation, agent_responses)
            layer_status[layer] = layer_result.get("status", "unknown")

            # 检查是否需要继续下一层辩论
            if not self.layer_results[layer].get("completed", False):
                logger.warning(f"{DEBATE_LAYERS[layer]['name']} 辩论未完成，中止后续辩论")
                result["status"] = "partial_success"
                result["message"] = f"{DEBATE_LAYERS[layer]['name']} 辩论未完成，后续层次辩论已中止"
                break

        # 记录结束时间
        end_time = time.time()
        debate_duration = end_time - start_time
        result["debate_duration"] = debate_duration

        # 更新辩论指标
        self._update_debate_metrics()
        result["metrics"] = self.debate_metrics

        # 生成最终结论
        final_conclusion = self._generate_final_conclusion(fact, accusation)
        result["final_conclusion"] = final_conclusion

        # 如果所有层次都完成了辩论，则标记为成功
        if all(self.layer_results[layer].get("completed", False) for layer in ["FACT", "LAW", "SENTENCING"]):
            result["status"] = "success"
            result["message"] = "所有层次辩论已完成"

        # 添加辩论点和层次结果
        result["debate_points"] = [p.to_dict() for p in self.debate_points]
        result["layer_results"] = self.layer_results
        result["layer_status"] = layer_status

        # 添加结构化判决结果
        if final_conclusion.get("crime_established") is not None:
            result["crime_established"] = final_conclusion["crime_established"]
        if final_conclusion.get("crime_name"):
            result["crime_name"] = final_conclusion["crime_name"]
        if final_conclusion.get("imprisonment") is not None:
            result["imprisonment"] = final_conclusion["imprisonment"]

        # 记录辩论总结
        logger.info(f"辩论完成，总耗时: {debate_duration:.2f}秒")
        logger.info(f"辩论点数量: {self.debate_metrics['total_debate_points']}, 总轮次: {self.debate_metrics['total_rounds']}")
        logger.info(f"平均共识程度: {self.debate_metrics['avg_consensus_score']:.2f}, 收敛率: {self.debate_metrics['convergence_rate']:.2f}")
        logger.info(f"辩论效率分数: {self.debate_metrics['efficiency_score']:.2f}")

        # 记录判决结果
        if final_conclusion.get("crime_established") is not None:
            logger.info(f"罪名认定: {'构成' if final_conclusion['crime_established'] else '不构成'} {final_conclusion.get('crime_name', accusation)}")
        if final_conclusion.get("imprisonment") is not None:
            logger.info(f"量刑结果: {final_conclusion['imprisonment']} 个月")

        return result

    def _generate_final_conclusion(self, fact: str, accusation: str) -> Dict:
        """
        生成最终结论

        Args:
            fact: 案件事实
            accusation: 指控罪名

        Returns:
            最终结论，包含判决内容和结构化结果
        """
        # 检查是否完成了所有层次的辩论
        incomplete_layers = [DEBATE_LAYERS[layer]["name"] for layer, result in self.layer_results.items()
                            if not result.get("completed", False)]

        if incomplete_layers:
            return {
                "status": "incomplete",
                "message": f"以下层次辩论未完成，无法生成完整最终结论：{', '.join(incomplete_layers)}",
                "content": "辩论未完全完成，无法生成最终结论。",
                "crime_established": None,
                "imprisonment": None,
                "reasoning": None
            }

        # 构建各层次总结的文本
        summaries_text = ""
        for layer, layer_info in DEBATE_LAYERS.items():
            if layer in self.layer_results and "summary" in self.layer_results[layer]:
                summaries_text += f"【{layer_info['name']}总结】\n{self.layer_results[layer]['summary']}\n\n"

        # 构建各层次关键输出的文本
        key_outputs_text = ""
        for layer, layer_info in DEBATE_LAYERS.items():
            if layer in self.layer_results and "key_outputs" in self.layer_results[layer]:
                key_outputs_text += "【" + layer_info['name'] + "关键输出】\n"
                for output_name, output_content in self.layer_results[layer]["key_outputs"].items():
                    key_outputs_text += output_name + ": " + output_content + "\n"
                key_outputs_text += "\n"

        # 构建辩论指标的文本
        metrics_text = ""
        if self.debate_metrics:
            metrics_text += "辩论点数量: " + str(self.debate_metrics.get('total_debate_points', 0)) + "\n"
            metrics_text += "总辩论轮次: " + str(self.debate_metrics.get('total_rounds', 0)) + "\n"
            metrics_text += "平均共识程度: " + "{:.2f}".format(self.debate_metrics.get('avg_consensus_score', 0)) + "\n"
            metrics_text += "收敛率: " + "{:.2f}".format(self.debate_metrics.get('convergence_rate', 0)) + "\n"
            metrics_text += "辩论效率分数: " + "{:.2f}".format(self.debate_metrics.get('efficiency_score', 0)) + "\n\n"

        # 构建提示词
        final_judgment_prompt = "作为一名最高人民法院资深法官，请根据以下三个层次的辩论结果，对案件做出最终判决。\n\n"
        final_judgment_prompt += "案件事实概述：\n" + fact[:500] + "...\n\n"
        final_judgment_prompt += "指控罪名：" + accusation + "\n\n"
        final_judgment_prompt += "辩论总结：\n" + summaries_text + "\n"

        if key_outputs_text:
            final_judgment_prompt += "辩论关键输出：\n" + key_outputs_text + "\n"

        if metrics_text:
            final_judgment_prompt += "辩论指标：\n" + metrics_text + "\n"

        final_judgment_prompt += "请给出最终判决，包括：\n"
        final_judgment_prompt += "1. 罪名认定：被告人是否构成" + accusation + "，并说明理由\n"
        final_judgment_prompt += "2. 量刑决定：具体的刑期（以月为单位），并说明理由\n\n"

        final_judgment_prompt += """判决应当：
- 基于事实层的共识认定案件事实
- 基于法律适用层的共识进行罪名认定
- 基于量刑层的共识进行量刑决定
- 保持法律专业性和准确性
- 逻辑严密，论证充分
- 引用相关法律条款和司法解释

请以下列JSON格式输出判决结果：
```json
{
  "crime_established": true/false,  // 是否构成犯罪
  "crime_name": "犯罪名称",  // 认定的罪名
  "imprisonment": 36,  // 判处的刑期（月）
  "reasoning": "判决理由的详细说明"
}
```

在JSON之后，请提供完整的判决书文本，包含详细的事实认定、法律分析和量刑说明。"""

        prompt = final_judgment_prompt

        # 获取LLM回复
        response = self.get_completion(
            prompt,
            role="senior judge with 30 years of experience in criminal law and expertise in integrating multi-level legal analysis"
        )

        # 解析回复，提取结构化结果
        result = {
            "status": "success",
            "content": response,
            "crime_established": None,
            "imprisonment": None,
            "reasoning": None
        }

        # 提取JSON部分
        json_match = re.search(r'```json\s*([\s\S]*?)\s*```', response)
        if json_match:
            json_str = json_match.group(1)
            try:
                data = json.loads(json_str)
                result["crime_established"] = data.get("crime_established")
                result["crime_name"] = data.get("crime_name")
                result["imprisonment"] = data.get("imprisonment")
                result["reasoning"] = data.get("reasoning")
            except:
                logger.error("解析判决结果JSON失败")

                # 尝试使用正则表达式提取关键信息
                crime_match = re.search(r'罪名认定[：:]\s*(.+?)(?=\n|$)', response)
                if crime_match:
                    crime_text = crime_match.group(1)
                    result["crime_established"] = "构成" in crime_text and "不构成" not in crime_text

                imprisonment_match = re.search(r'量刑[：:]\s*(\d+)个?月', response)
                if imprisonment_match:
                    try:
                        result["imprisonment"] = int(imprisonment_match.group(1))
                    except:
                        pass
        else:
            # 如果没有JSON部分，尝试直接从文本中提取关键信息
            crime_match = re.search(r'罪名认定[：:]\s*(.+?)(?=\n|$)', response)
            if crime_match:
                crime_text = crime_match.group(1)
                result["crime_established"] = "构成" in crime_text and "不构成" not in crime_text

            imprisonment_match = re.search(r'量刑[：:]\s*(\d+)个?月', response)
            if imprisonment_match:
                try:
                    result["imprisonment"] = int(imprisonment_match.group(1))
                except:
                    pass

        return result
