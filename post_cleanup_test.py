#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代码清理后的系统功能验证测试

验证代码清理和优化后系统的核心功能是否正常工作，包括：
1. 核心模块导入测试
2. 智能体初始化测试
3. 基本功能运行测试
4. 多智能体协作机制测试

使用方法：
python post_cleanup_test.py
"""

import sys
import logging
from typing import Dict, List

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_core_imports():
    """测试核心模块导入"""
    logger.info("测试核心模块导入...")
    
    try:
        # 测试核心系统导入
        from judicial_ie_coordinator import JudicialIECoordinator
        from extraction_types import ExtractionTaskType, InformationType, ExtractionResult
        
        # 测试智能体导入
        from extraction_agents.entity_extractor import EntityExtractionAgent
        from extraction_agents.base_agent import BaseExtractionAgent
        
        logger.info("✅ 核心模块导入成功")
        return True
        
    except ImportError as e:
        logger.error(f"❌ 核心模块导入失败: {e}")
        return False

def test_agent_initialization():
    """测试智能体初始化"""
    logger.info("测试智能体初始化...")
    
    try:
        from extraction_agents.entity_extractor import EntityExtractionAgent
        
        # 初始化实体抽取智能体
        agent = EntityExtractionAgent()
        
        # 检查基本属性
        assert hasattr(agent, 'agent_id')
        assert hasattr(agent, 'specialization')
        assert hasattr(agent, 'performance_stats')
        assert hasattr(agent, 'extract')
        
        logger.info(f"✅ 智能体初始化成功，ID: {agent.agent_id}")
        logger.info(f"   专业领域数量: {len(agent.specialization)}")
        return True
        
    except Exception as e:
        logger.error(f"❌ 智能体初始化失败: {e}")
        return False

def test_coordinator_initialization():
    """测试协调器初始化"""
    logger.info("测试协调器初始化...")
    
    try:
        from judicial_ie_coordinator import JudicialIECoordinator
        
        # 初始化协调器（禁用协作以简化测试）
        coordinator = JudicialIECoordinator(
            enable_parallel=False,
            enable_cache=False,
            enable_collaboration=False
        )
        
        # 检查基本属性
        assert hasattr(coordinator, 'agents')
        assert hasattr(coordinator, 'config')
        assert hasattr(coordinator, 'legal_knowledge')
        
        logger.info("✅ 协调器初始化成功")
        logger.info(f"   已加载智能体: {list(coordinator.agents.keys())}")
        return True
        
    except Exception as e:
        logger.error(f"❌ 协调器初始化失败: {e}")
        return False

def test_basic_extraction():
    """测试基本信息抽取功能"""
    logger.info("测试基本信息抽取功能...")
    
    try:
        from judicial_ie_coordinator import JudicialIECoordinator
        from extraction_types import ExtractionTaskType
        
        # 初始化协调器
        coordinator = JudicialIECoordinator(
            enable_parallel=False,
            enable_cache=False,
            enable_collaboration=False
        )
        
        # 测试文本
        test_text = "被告人张某于2023年3月在北京市朝阳区盗窃他人财物。"
        
        # 执行信息抽取
        result = coordinator.extract_information(
            test_text, 
            ExtractionTaskType.ENTITY_EXTRACTION
        )
        
        # 检查结果
        assert isinstance(result, dict)
        assert 'status' in result
        
        logger.info(f"✅ 基本信息抽取测试成功")
        logger.info(f"   抽取状态: {result.get('status', 'unknown')}")
        
        if result.get('results'):
            logger.info(f"   抽取结果数量: {len(result['results'])}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 基本信息抽取测试失败: {e}")
        return False

def test_collaboration_components():
    """测试协作组件"""
    logger.info("测试协作组件...")
    
    try:
        from judicial_ie_coordinator import MessageBus, SharedKnowledgeBase, AgentDependencyManager
        
        # 测试消息总线
        message_bus = MessageBus(max_workers=2)
        assert hasattr(message_bus, 'publish')
        assert hasattr(message_bus, 'subscribe')
        
        # 测试共享知识库
        knowledge_base = SharedKnowledgeBase()
        assert hasattr(knowledge_base, 'store_knowledge')
        assert hasattr(knowledge_base, 'search_knowledge')
        
        # 测试依赖关系管理器
        dependency_manager = AgentDependencyManager()
        assert hasattr(dependency_manager, 'add_dependency')
        assert hasattr(dependency_manager, 'get_execution_order')
        
        # 关闭消息总线
        message_bus.shutdown()
        
        logger.info("✅ 协作组件测试成功")
        return True
        
    except Exception as e:
        logger.error(f"❌ 协作组件测试失败: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    logger.info("开始代码清理后的系统功能验证")
    logger.info("=" * 50)
    
    tests = [
        ("核心模块导入", test_core_imports),
        ("智能体初始化", test_agent_initialization),
        ("协调器初始化", test_coordinator_initialization),
        ("基本信息抽取", test_basic_extraction),
        ("协作组件", test_collaboration_components)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 执行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"测试 {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果总结
    logger.info("\n" + "=" * 50)
    logger.info("📋 测试结果总结")
    logger.info("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name:15} : {status}")
        if result:
            passed += 1
    
    logger.info(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        logger.info("🎉 所有测试通过！代码清理后系统功能正常！")
        return True
    else:
        logger.warning("⚠️  部分测试失败，需要进一步检查")
        return False

def main():
    """主函数"""
    print("司法信息提取系统 - 代码清理后功能验证")
    print("=" * 60)
    
    success = run_all_tests()
    
    if success:
        print("\n✅ 系统验证完成：所有核心功能正常工作")
        print("📝 系统已准备好进行下一步的开发和优化")
    else:
        print("\n❌ 系统验证发现问题，请检查错误日志")
        print("🔧 建议修复问题后重新运行测试")

if __name__ == "__main__":
    main()
