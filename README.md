# 多智能体司法决策系统

本项目实现了一个基于多智能体协作的司法决策系统，包含反事实证据分析机制和自适应多层次辩论框架。

## 项目结构

```
.
├── judicial_cola.py              # 核心代码
├── counterfactual_evidence_analyzer.py  # 反事实证据分析模块
├── adaptive_debate_framework.py  # 自适应多层次辩论框架
├── utils.py                      # 工具函数
├── legal_knowledge_base.json     # 法律知识库
├── legal_role_map.json           # 法律角色映射
├── test_judicial_system.py       # 测试脚本
├── small/                        # 小型数据集
│   └── test_small.jsonl          # 测试数据
└── big/                          # 大型数据集
```

## 核心创新点

### 1. 反事实证据分析机制

通过模拟"如果某证据不存在或不同"的情况，评估各证据对最终结论的影响权重。

- 评估各证据的重要性
- 分析证据组合效应
- 执行敏感性分析
- 识别关键证据

### 2. 自适应多层次辩论框架

将法律推理分为事实层、法律适用层和量刑层三个层次进行结构化辩论。

- 自动检测争议点
- 动态控制辩论深度
- 实现层间信息传递
- 形成最终共识

## 使用方法

### 安装依赖

```bash
pip install openai tqdm jsonlines
```

### 运行测试

```bash
python test_judicial_system.py --case_indices 0
```

参数说明：
- `--input_file`: 输入文件路径，默认为 'small/test_small.jsonl'
- `--output_file`: 输出文件路径，默认为 'judicial_system_results.json'
- `--case_indices`: 要处理的案例索引，用逗号分隔，例如 "0,1,2"，使用 "all" 处理所有案例
- `--model`: 使用的模型名称，默认为 'gpt-3.5-turbo-16k'

## 系统流程

1. **反事实证据分析**：评估各证据对最终结论的影响
2. **线索分析**：分析案件中的关键线索
3. **法律专家分析**：分析案件的法律适用
4. **辩护律师分析**：提供辩护观点
5. **检察官分析**：提供控诉观点
6. **量刑专家分析**：提供量刑建议
7. **辩论分析**：协调各方观点，解决分歧
8. **法官判决**：根据各方分析做出最终判决

## 注意事项

- 需要设置OpenAI API密钥
- 处理大型数据集可能需要较长时间
- 建议先使用小型数据集进行测试
