"""
司法信息抽取系统测试验证模块

用于验证多智能体司法信息抽取系统的核心功能，包括：
1. 实体抽取准确性测试
2. 证据权重引导效果测试
3. 冲突解决机制测试
4. 系统集成测试

支持多种评估指标和详细的性能分析。
提供完整的测试框架和性能评估体系。
"""

import json
import logging
import time
from typing import Dict, List, Tuple
from dataclasses import dataclass

# 导入系统组件
from judicial_ie_coordinator import JudicialIECoordinator, ExtractionTaskType

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class TestResult:
    """测试结果数据结构"""
    case_id: str
    test_type: str
    precision: float
    recall: float
    f1_score: float
    execution_time: float
    extracted_entities: Dict
    expected_entities: Dict
    errors: List[str]

class JudicialIEValidator:
    """司法信息抽取系统验证器"""

    def __init__(self):
        # 初始化系统
        self.ie_coordinator = JudicialIECoordinator()

        # 加载测试数据
        self.test_cases = self._load_test_cases()

        # 测试结果存储
        self.test_results = []

        logger.info("司法信息抽取验证器初始化完成")

    def _load_test_cases(self) -> List[Dict]:
        """加载测试案例"""
        try:
            with open('test_data/test_cases.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
            test_cases = data.get('test_cases', [])
            logger.info(f"加载了 {len(test_cases)} 个测试案例")
            return test_cases
        except Exception as e:
            logger.error(f"加载测试案例失败: {e}")
            return []

    def run_basic_extraction_test(self) -> List[TestResult]:
        """运行基础信息抽取测试"""
        logger.info("开始基础信息抽取测试")

        basic_test_results = []

        for test_case in self.test_cases:
            case_id = test_case['case_id']
            text = test_case['text']
            expected = test_case['expected_results']

            logger.info(f"测试案例 {case_id}")

            try:
                # 执行信息抽取
                start_time = time.time()
                extraction_result = self.ie_coordinator.extract_information(
                    text, ExtractionTaskType.ENTITY_EXTRACTION
                )
                execution_time = time.time() - start_time

                # 解析抽取结果
                extracted_entities = self._parse_extraction_results(extraction_result)

                # 计算评估指标
                precision, recall, f1_score = self._calculate_metrics(extracted_entities, expected)

                # 创建测试结果
                test_result = TestResult(
                    case_id=case_id,
                    test_type="basic_extraction",
                    precision=precision,
                    recall=recall,
                    f1_score=f1_score,
                    execution_time=execution_time,
                    extracted_entities=extracted_entities,
                    expected_entities=expected,
                    errors=[]
                )

                basic_test_results.append(test_result)

                logger.info(f"案例 {case_id} 测试完成 - P: {precision:.3f}, R: {recall:.3f}, F1: {f1_score:.3f}")

            except Exception as e:
                logger.error(f"案例 {case_id} 测试失败: {e}")
                error_result = TestResult(
                    case_id=case_id,
                    test_type="basic_extraction",
                    precision=0.0,
                    recall=0.0,
                    f1_score=0.0,
                    execution_time=0.0,
                    extracted_entities={},
                    expected_entities=expected,
                    errors=[str(e)]
                )
                basic_test_results.append(error_result)

        self.test_results.extend(basic_test_results)
        return basic_test_results

    def run_evidence_guided_test(self) -> List[TestResult]:
        """运行证据权重引导抽取测试"""
        logger.info("开始证据权重引导抽取测试")

        guided_test_results = []

        for test_case in self.test_cases:
            case_id = test_case['case_id']
            text = test_case['text']
            expected = test_case['expected_results']

            logger.info(f"测试证据权重引导 - 案例 {case_id}")

            try:
                # 执行综合信息抽取（包含证据权重引导）
                start_time = time.time()
                extraction_result = self.ie_coordinator.extract_information(
                    text, ExtractionTaskType.COMPREHENSIVE_EXTRACTION
                )
                execution_time = time.time() - start_time

                # 解析抽取结果
                extracted_entities = self._parse_extraction_results(extraction_result)

                # 计算评估指标
                precision, recall, f1_score = self._calculate_metrics(extracted_entities, expected)

                # 创建测试结果
                test_result = TestResult(
                    case_id=case_id,
                    test_type="evidence_guided",
                    precision=precision,
                    recall=recall,
                    f1_score=f1_score,
                    execution_time=execution_time,
                    extracted_entities=extracted_entities,
                    expected_entities=expected,
                    errors=[]
                )

                guided_test_results.append(test_result)

                logger.info(f"证据引导案例 {case_id} 测试完成 - P: {precision:.3f}, R: {recall:.3f}, F1: {f1_score:.3f}")

            except Exception as e:
                logger.error(f"证据引导案例 {case_id} 测试失败: {e}")
                error_result = TestResult(
                    case_id=case_id,
                    test_type="evidence_guided",
                    precision=0.0,
                    recall=0.0,
                    f1_score=0.0,
                    execution_time=0.0,
                    extracted_entities={},
                    expected_entities=expected,
                    errors=[str(e)]
                )
                guided_test_results.append(error_result)

        self.test_results.extend(guided_test_results)
        return guided_test_results

    def _parse_extraction_results(self, extraction_result: Dict) -> Dict:
        """解析抽取结果为标准格式"""
        extracted_entities = {
            "defendants": [],
            "victims": [],
            "witnesses": [],
            "crime_time": [],
            "trial_time": [],
            "crime_location": [],
            "charges": [],
            "legal_articles": [],
            "sentence": [],
            "case_facts": []
        }

        if extraction_result.get('status') == 'success':
            results = extraction_result.get('results', {})
            detailed_results = results.get('detailed_results', [])

            for result in detailed_results:
                info_type = result.get('info_type', '')
                content = result.get('content', '')

                if info_type == 'defendant' and content:
                    extracted_entities["defendants"].append(content)
                elif info_type == 'victim' and content:
                    extracted_entities["victims"].append(content)
                elif info_type == 'witness' and content:
                    extracted_entities["witnesses"].append(content)
                elif info_type == 'crime_time' and content:
                    extracted_entities["crime_time"].append(content)
                elif info_type == 'trial_time' and content:
                    extracted_entities["trial_time"].append(content)
                elif info_type == 'crime_location' and content:
                    extracted_entities["crime_location"].append(content)
                elif info_type == 'charges' and content:
                    extracted_entities["charges"].append(content)
                elif info_type == 'legal_articles' and content:
                    extracted_entities["legal_articles"].append(content)
                elif info_type == 'verdict' and content:
                    extracted_entities["sentence"].append(content)
                elif info_type == 'case_facts' and content:
                    extracted_entities["case_facts"].append(content)

        return extracted_entities

    def _calculate_metrics(self, extracted: Dict, expected: Dict) -> Tuple[float, float, float]:
        """计算精确率、召回率和F1分数"""
        total_precision = 0.0
        total_recall = 0.0
        valid_types = 0

        for entity_type in expected.keys():
            if entity_type in extracted:
                extracted_set = set(extracted[entity_type])
                expected_set = set(expected[entity_type])

                if len(expected_set) > 0:  # 只计算有标准答案的类型
                    # 计算精确率
                    if len(extracted_set) > 0:
                        precision = len(extracted_set & expected_set) / len(extracted_set)
                    else:
                        precision = 0.0

                    # 计算召回率
                    recall = len(extracted_set & expected_set) / len(expected_set)

                    total_precision += precision
                    total_recall += recall
                    valid_types += 1

        # 计算平均精确率和召回率
        avg_precision = total_precision / valid_types if valid_types > 0 else 0.0
        avg_recall = total_recall / valid_types if valid_types > 0 else 0.0

        # 计算F1分数
        if avg_precision + avg_recall > 0:
            f1_score = 2 * avg_precision * avg_recall / (avg_precision + avg_recall)
        else:
            f1_score = 0.0

        return avg_precision, avg_recall, f1_score

    def generate_test_report(self) -> Dict:
        """生成测试报告"""
        if not self.test_results:
            return {"error": "没有测试结果"}

        # 按测试类型分组统计
        basic_results = [r for r in self.test_results if r.test_type == "basic_extraction"]
        guided_results = [r for r in self.test_results if r.test_type == "evidence_guided"]

        report = {
            "test_summary": {
                "total_cases": len(self.test_cases),
                "basic_extraction_tests": len(basic_results),
                "evidence_guided_tests": len(guided_results)
            },
            "basic_extraction_performance": self._calculate_average_performance(basic_results),
            "evidence_guided_performance": self._calculate_average_performance(guided_results),
            "detailed_results": [
                {
                    "case_id": r.case_id,
                    "test_type": r.test_type,
                    "precision": r.precision,
                    "recall": r.recall,
                    "f1_score": r.f1_score,
                    "execution_time": r.execution_time
                }
                for r in self.test_results
            ]
        }

        return report

    def _calculate_average_performance(self, results: List[TestResult]) -> Dict:
        """计算平均性能指标"""
        if not results:
            return {"avg_precision": 0.0, "avg_recall": 0.0, "avg_f1": 0.0, "avg_time": 0.0}

        avg_precision = sum(r.precision for r in results) / len(results)
        avg_recall = sum(r.recall for r in results) / len(results)
        avg_f1 = sum(r.f1_score for r in results) / len(results)
        avg_time = sum(r.execution_time for r in results) / len(results)

        return {
            "avg_precision": avg_precision,
            "avg_recall": avg_recall,
            "avg_f1": avg_f1,
            "avg_time": avg_time
        }

    def run_comprehensive_test(self) -> Dict:
        """运行综合测试"""
        logger.info("开始综合测试")

        # 运行基础抽取测试
        basic_results = self.run_basic_extraction_test()

        # 运行证据权重引导测试
        guided_results = self.run_evidence_guided_test()

        # 生成测试报告
        report = self.generate_test_report()

        logger.info("综合测试完成")
        return report

def main():
    """主测试函数"""
    print("=" * 80)
    print("司法信息抽取系统测试验证")
    print("=" * 80)

    # 创建验证器
    validator = JudicialIEValidator()

    # 运行综合测试
    test_report = validator.run_comprehensive_test()

    # 输出测试报告
    print("\n测试报告:")
    print("-" * 60)
    print(f"总测试案例数: {test_report['test_summary']['total_cases']}")
    print(f"基础抽取测试: {test_report['test_summary']['basic_extraction_tests']}")
    print(f"证据引导测试: {test_report['test_summary']['evidence_guided_tests']}")

    print("\n基础抽取性能:")
    basic_perf = test_report['basic_extraction_performance']
    print(f"  平均精确率: {basic_perf['avg_precision']:.3f}")
    print(f"  平均召回率: {basic_perf['avg_recall']:.3f}")
    print(f"  平均F1分数: {basic_perf['avg_f1']:.3f}")
    print(f"  平均执行时间: {basic_perf['avg_time']:.3f}秒")

    print("\n证据引导抽取性能:")
    guided_perf = test_report['evidence_guided_performance']
    print(f"  平均精确率: {guided_perf['avg_precision']:.3f}")
    print(f"  平均召回率: {guided_perf['avg_recall']:.3f}")
    print(f"  平均F1分数: {guided_perf['avg_f1']:.3f}")
    print(f"  平均执行时间: {guided_perf['avg_time']:.3f}秒")

    # 保存详细报告
    with open('test_results.json', 'w', encoding='utf-8') as f:
        json.dump(test_report, f, ensure_ascii=False, indent=2)

    print("\n详细测试报告已保存到 test_results.json")
    print("=" * 80)

if __name__ == "__main__":
    main()
