"""
司法信息抽取协调器 - 主入口模块

基于现有司法决策系统的技术优势，实现多智能体协作的司法信息抽取。
直接复用evidence_weight_analyzer和adaptive_debate_framework的核心功能。

核心设计原则：
1. 最大化复用现有系统组件
2. 保持与现有系统的兼容性
3. 模块化设计便于独立开发和测试
4. 渐进式集成确保系统稳定性

主要功能：
- 多智能体协作信息抽取
- 证据权重引导抽取策略
- 智能冲突解决机制
- 质量评估和结果整合
"""

import json
import logging
import time
import asyncio
import threading
import queue
import concurrent.futures
from typing import Dict, List, Optional, Any, Tuple, Callable, Awaitable
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict, deque
import uuid

# 直接复用现有系统的核心组件
from evidence_weight_analyzer import EvidenceWeightAnalyzer, Evidence
from adaptive_debate_framework import AdaptiveDebateFramework
from judicial_cola import get_completion

# 导入模块化的协作组件
from core.message_bus import MessageBus, AgentMessage, MessageType
from core.shared_knowledge import SharedKnowledgeBase
from core.agent_dependency import AgentDependencyManager
from core.collaborative_reasoning import CollaborativeReasoningEngine

# 导入共享类型定义
from extraction_types import ExtractionResult, ExtractionTaskType, InformationType

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入性能优化组件
try:
    from parallel_extraction_framework import ParallelExtractionCoordinator, IntelligentScheduler
    from llm_cache_system import LLMResponseCache, CachedLLMWrapper
    OPTIMIZATION_AVAILABLE = True
except ImportError as e:
    logger.warning(f"性能优化组件导入失败: {e}")
    OPTIMIZATION_AVAILABLE = False

# 类型定义已移至 extraction_types.py

# ==================== 多智能体协作机制核心组件 ====================

class MessageType(Enum):
    """消息类型枚举"""
    KNOWLEDGE_SHARE = "knowledge_share"
    COLLABORATION_REQUEST = "collaboration_request"
    EVIDENCE_UPDATE = "evidence_update"
    CONSENSUS_PROPOSAL = "consensus_proposal"
    CONFLICT_NOTIFICATION = "conflict_notification"
    TASK_COORDINATION = "task_coordination"

@dataclass
class AgentMessage:
    """智能体间消息"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    sender_id: str = ""
    receiver_id: str = ""  # 空字符串表示广播
    message_type: MessageType = MessageType.KNOWLEDGE_SHARE
    content: Dict[str, Any] = field(default_factory=dict)
    timestamp: float = field(default_factory=time.time)
    priority: int = 1  # 1-5，5为最高优先级
    requires_response: bool = False
    correlation_id: Optional[str] = None

    def __lt__(self, other):
        """为优先级队列提供比较方法"""
        if not isinstance(other, AgentMessage):
            return NotImplemented
        # 优先级高的消息排在前面（数值越大优先级越高）
        if self.priority != other.priority:
            return self.priority > other.priority
        # 优先级相同时，按时间戳排序（早的在前）
        return self.timestamp < other.timestamp

class MessageBus:
    """智能体消息总线系统 - 真正的异步实现"""

    def __init__(self, max_workers: int = 4):
        self.subscribers: Dict[str, List[Callable]] = defaultdict(list)
        self.message_queue: queue.PriorityQueue = queue.PriorityQueue()
        self.message_history: List[AgentMessage] = []
        self.active_agents: Dict[str, Dict] = {}
        self.collaboration_sessions: Dict[str, Dict] = {}
        self._lock = threading.Lock()

        # 异步处理组件
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=max_workers)
        self.processing_thread = None
        self.is_running = False
        self.processed_count = 0
        self.failed_count = 0

        # 启动异步消息处理线程
        self._start_async_processing()

        logger.info(f"异步消息总线系统初始化完成，工作线程数: {max_workers}")

    def register_agent(self, agent_id: str, agent_info: Dict):
        """注册智能体"""
        with self._lock:
            self.active_agents[agent_id] = {
                "info": agent_info,
                "last_active": time.time(),
                "message_count": 0,
                "collaboration_count": 0
            }
        logger.info(f"智能体 {agent_id} 已注册到消息总线")

    def subscribe(self, agent_id: str, message_type: MessageType, callback: Callable):
        """订阅消息类型"""
        subscription_key = f"{agent_id}:{message_type.value}"
        self.subscribers[subscription_key].append(callback)
        logger.debug(f"智能体 {agent_id} 订阅了消息类型 {message_type.value}")

    def _start_async_processing(self):
        """启动异步消息处理线程"""
        self.is_running = True
        self.processing_thread = threading.Thread(target=self._async_message_processor, daemon=True)
        self.processing_thread.start()
        logger.info("异步消息处理线程已启动")

    def _async_message_processor(self):
        """异步消息处理器 - 在独立线程中运行"""
        while self.is_running:
            try:
                # 从优先级队列获取消息（阻塞等待）
                message = self.message_queue.get(timeout=1.0)

                # 提交到线程池异步处理
                future = self.executor.submit(self._process_message_async, message)

                # 可选：等待处理完成或设置超时
                try:
                    future.result(timeout=5.0)  # 5秒超时
                    self.processed_count += 1
                except concurrent.futures.TimeoutError:
                    logger.warning(f"消息处理超时: {message.id}")
                    self.failed_count += 1
                except Exception as e:
                    logger.error(f"消息处理失败: {e}")
                    self.failed_count += 1

                # 标记任务完成
                self.message_queue.task_done()

            except queue.Empty:
                # 超时，继续循环
                continue
            except Exception as e:
                logger.error(f"异步消息处理器错误: {e}")

    def publish(self, message: AgentMessage) -> bool:
        """发布消息 - 异步处理"""
        try:
            with self._lock:
                # 添加到消息历史
                self.message_history.append(message)

                # 更新发送者活跃状态
                if message.sender_id in self.active_agents:
                    self.active_agents[message.sender_id]["last_active"] = time.time()
                    self.active_agents[message.sender_id]["message_count"] += 1

            # 添加到优先级队列进行异步处理
            # 直接使用消息对象，利用其__lt__方法进行排序
            self.message_queue.put(message)

            logger.debug(f"消息已加入异步处理队列: {message.id}, 优先级: {message.priority}")
            return True

        except Exception as e:
            logger.error(f"消息发布失败: {e}")
            return False

    def _process_message_async(self, message: AgentMessage):
        """异步处理消息 - 在线程池中执行"""
        try:
            # 确定接收者
            with self._lock:
                if message.receiver_id:
                    # 点对点消息
                    receivers = [message.receiver_id] if message.receiver_id in self.active_agents else []
                else:
                    # 广播消息
                    receivers = list(self.active_agents.keys())
                    if message.sender_id in receivers:
                        receivers.remove(message.sender_id)  # 不发送给自己

            # 分发消息
            for receiver_id in receivers:
                subscription_key = f"{receiver_id}:{message.message_type.value}"
                callbacks = self.subscribers.get(subscription_key, [])

                for callback in callbacks:
                    try:
                        # 在线程池中执行回调
                        callback(message)
                        logger.debug(f"消息已投递给 {receiver_id}: {message.id}")
                    except Exception as e:
                        logger.error(f"消息处理回调失败 {receiver_id}: {e}")

            logger.debug(f"消息处理完成: {message.id}")

        except Exception as e:
            logger.error(f"异步消息处理失败: {e}")
            raise

    def _process_message(self, message: AgentMessage):
        """同步处理消息 - 保持向后兼容"""
        # 为了保持测试兼容性，保留同步版本
        self._process_message_async(message)

    def shutdown(self):
        """关闭消息总线"""
        logger.info("正在关闭异步消息总线...")
        self.is_running = False

        # 等待队列中的消息处理完成
        try:
            self.message_queue.join()  # 等待所有任务完成
        except:
            pass

        # 关闭线程池
        self.executor.shutdown(wait=True)

        # 等待处理线程结束
        if self.processing_thread and self.processing_thread.is_alive():
            self.processing_thread.join(timeout=2.0)

        logger.info(f"消息总线已关闭，处理统计: 成功 {self.processed_count}, 失败 {self.failed_count}")

    def get_processing_stats(self) -> Dict:
        """获取处理统计信息"""
        return {
            "processed_count": self.processed_count,
            "failed_count": self.failed_count,
            "queue_size": self.message_queue.qsize(),
            "active_agents": len(self.active_agents),
            "is_running": self.is_running
        }

    def get_collaboration_history(self, agent_id: str) -> List[AgentMessage]:
        """获取智能体的协作历史"""
        return [msg for msg in self.message_history
                if msg.sender_id == agent_id or msg.receiver_id == agent_id]

    def create_collaboration_session(self, session_id: str, participants: List[str],
                                   topic: str) -> Dict:
        """创建协作会话"""
        session = {
            "id": session_id,
            "participants": participants,
            "topic": topic,
            "created_at": time.time(),
            "messages": [],
            "status": "active"
        }
        self.collaboration_sessions[session_id] = session

        # 通知参与者
        for participant in participants:
            notification = AgentMessage(
                sender_id="system",
                receiver_id=participant,
                message_type=MessageType.TASK_COORDINATION,
                content={
                    "action": "collaboration_session_created",
                    "session_id": session_id,
                    "topic": topic,
                    "participants": participants
                }
            )
            self.publish(notification)

        logger.info(f"创建协作会话 {session_id}，参与者: {participants}")
        return session

class SharedKnowledgeBase:
    """智能体共享知识库系统"""

    def __init__(self):
        self.knowledge_store: Dict[str, Dict] = {}
        self.version_history: Dict[str, List[Dict]] = defaultdict(list)
        self.access_log: List[Dict] = []
        self.knowledge_dependencies: Dict[str, List[str]] = defaultdict(list)
        self._lock = threading.Lock()

        logger.info("共享知识库系统初始化完成")

    def store_knowledge(self, knowledge_id: str, content: Dict,
                       contributor_id: str, knowledge_type: str = "general") -> bool:
        """存储知识"""
        try:
            with self._lock:
                # 创建知识条目
                knowledge_entry = {
                    "id": knowledge_id,
                    "content": content,
                    "type": knowledge_type,
                    "contributor": contributor_id,
                    "created_at": time.time(),
                    "version": len(self.version_history[knowledge_id]) + 1,
                    "access_count": 0,
                    "confidence": content.get("confidence", 0.8),
                    "tags": content.get("tags", [])
                }

                # 存储到主知识库
                self.knowledge_store[knowledge_id] = knowledge_entry

                # 记录版本历史
                self.version_history[knowledge_id].append(knowledge_entry.copy())

                # 记录访问日志
                self.access_log.append({
                    "action": "store",
                    "knowledge_id": knowledge_id,
                    "agent_id": contributor_id,
                    "timestamp": time.time()
                })

            logger.info(f"知识 {knowledge_id} 已存储，贡献者: {contributor_id}")
            return True

        except Exception as e:
            logger.error(f"知识存储失败: {e}")
            return False

    def retrieve_knowledge(self, knowledge_id: str, requester_id: str) -> Optional[Dict]:
        """检索知识"""
        try:
            with self._lock:
                if knowledge_id in self.knowledge_store:
                    knowledge = self.knowledge_store[knowledge_id].copy()
                    knowledge["access_count"] += 1

                    # 记录访问日志
                    self.access_log.append({
                        "action": "retrieve",
                        "knowledge_id": knowledge_id,
                        "agent_id": requester_id,
                        "timestamp": time.time()
                    })

                    return knowledge
                else:
                    return None

        except Exception as e:
            logger.error(f"知识检索失败: {e}")
            return None

    def update_knowledge(self, knowledge_id: str, updates: Dict,
                        updater_id: str) -> bool:
        """更新知识"""
        try:
            with self._lock:
                if knowledge_id in self.knowledge_store:
                    # 备份当前版本
                    current = self.knowledge_store[knowledge_id].copy()
                    self.version_history[knowledge_id].append(current)

                    # 应用更新
                    self.knowledge_store[knowledge_id]["content"].update(updates)
                    self.knowledge_store[knowledge_id]["version"] += 1
                    self.knowledge_store[knowledge_id]["last_updated"] = time.time()
                    self.knowledge_store[knowledge_id]["last_updater"] = updater_id

                    # 记录访问日志
                    self.access_log.append({
                        "action": "update",
                        "knowledge_id": knowledge_id,
                        "agent_id": updater_id,
                        "timestamp": time.time()
                    })

                    return True
                else:
                    return False

        except Exception as e:
            logger.error(f"知识更新失败: {e}")
            return False

    def search_knowledge(self, query: Dict, requester_id: str) -> List[Dict]:
        """智能知识搜索 - 基于语义相似度"""
        results = []

        try:
            with self._lock:
                for knowledge_id, knowledge in self.knowledge_store.items():
                    # 计算多维度匹配分数
                    match_components = self._calculate_match_components(query, knowledge)

                    # 动态权重计算（基于查询类型和知识特征）
                    weights = self._calculate_dynamic_weights(query, knowledge)

                    # 加权总分
                    match_score = sum(weights[component] * score
                                    for component, score in match_components.items())

                    # 置信度阈值检查
                    confidence_threshold = query.get("min_confidence", 0.5)
                    min_match_threshold = query.get("min_match_score", 0.2)

                    if (knowledge["confidence"] >= confidence_threshold and
                        match_score >= min_match_threshold):
                        result = knowledge.copy()
                        result["match_score"] = match_score
                        result["match_components"] = match_components
                        result["match_weights"] = weights
                        results.append(result)

            # 按匹配分数排序
            results.sort(key=lambda x: x["match_score"], reverse=True)

            # 记录搜索日志
            self.access_log.append({
                "action": "search",
                "query": query,
                "agent_id": requester_id,
                "results_count": len(results),
                "avg_match_score": sum(r["match_score"] for r in results) / len(results) if results else 0.0,
                "timestamp": time.time()
            })

            return results

        except Exception as e:
            logger.error(f"知识搜索失败: {e}")
            return []

    def _calculate_match_components(self, query: Dict, knowledge: Dict) -> Dict[str, float]:
        """计算各个匹配组件的分数"""
        components = {}

        # 1. 类型匹配
        if "type" in query:
            components["type_match"] = 1.0 if query["type"] == knowledge["type"] else 0.0
        else:
            components["type_match"] = 0.5  # 中性分数

        # 2. 标签相似度
        if "tags" in query and query["tags"]:
            query_tags = set(query["tags"])
            knowledge_tags = set(knowledge.get("tags", []))
            if query_tags and knowledge_tags:
                # Jaccard相似度
                intersection = len(query_tags & knowledge_tags)
                union = len(query_tags | knowledge_tags)
                components["tag_similarity"] = intersection / union if union > 0 else 0.0
            else:
                components["tag_similarity"] = 0.0
        else:
            components["tag_similarity"] = 0.5

        # 3. 关键词语义匹配
        if "keywords" in query and query["keywords"]:
            components["keyword_match"] = self._calculate_keyword_similarity(
                query["keywords"], str(knowledge["content"])
            )
        else:
            components["keyword_match"] = 0.5

        # 4. 内容语义相似度
        if "content" in query:
            components["content_similarity"] = self._calculate_content_similarity(
                str(query["content"]), str(knowledge["content"])
            )
        else:
            components["content_similarity"] = 0.5

        # 5. 时间相关性
        components["temporal_relevance"] = self._calculate_temporal_relevance(
            query.get("time_preference", "recent"), knowledge
        )

        # 6. 置信度匹配
        query_confidence = query.get("preferred_confidence", 0.8)
        knowledge_confidence = knowledge.get("confidence", 0.8)
        components["confidence_match"] = 1.0 - abs(query_confidence - knowledge_confidence)

        return components

    def _calculate_dynamic_weights(self, query: Dict, knowledge: Dict) -> Dict[str, float]:
        """动态计算权重 - 基于查询类型和知识特征"""
        # 基础权重
        base_weights = {
            "type_match": 0.2,
            "tag_similarity": 0.25,
            "keyword_match": 0.25,
            "content_similarity": 0.15,
            "temporal_relevance": 0.1,
            "confidence_match": 0.05
        }

        # 根据查询类型调整权重
        query_type = query.get("type", "general")
        if query_type.endswith("_extraction"):
            # 抽取类查询更重视类型和标签匹配
            base_weights["type_match"] *= 1.5
            base_weights["tag_similarity"] *= 1.3
        elif query_type == "reasoning_step":
            # 推理类查询更重视内容相似度
            base_weights["content_similarity"] *= 2.0
            base_weights["keyword_match"] *= 1.2

        # 根据知识特征调整权重
        knowledge_age_days = (time.time() - knowledge.get("created_at", time.time())) / 86400
        if knowledge_age_days < 1:
            # 新知识提高时间相关性权重
            base_weights["temporal_relevance"] *= 1.5

        # 归一化权重
        total_weight = sum(base_weights.values())
        normalized_weights = {k: v / total_weight for k, v in base_weights.items()}

        return normalized_weights

    def _calculate_keyword_similarity(self, query_keywords: List[str], content: str) -> float:
        """计算关键词相似度"""
        if not query_keywords:
            return 0.0

        content_lower = content.lower()
        matched_count = 0
        partial_match_score = 0.0

        for keyword in query_keywords:
            keyword_lower = keyword.lower()
            if keyword_lower in content_lower:
                matched_count += 1
            else:
                # 部分匹配：检查是否包含关键词的子串
                for word in content_lower.split():
                    if keyword_lower in word or word in keyword_lower:
                        partial_match_score += 0.5
                        break

        # 完全匹配分数 + 部分匹配分数
        total_score = matched_count + partial_match_score
        return min(1.0, total_score / len(query_keywords))

    def _calculate_content_similarity(self, query_content: str, knowledge_content: str) -> float:
        """计算内容相似度 - 简化的语义相似度"""
        if not query_content or not knowledge_content:
            return 0.0

        # 转换为词集合
        query_words = set(query_content.lower().split())
        knowledge_words = set(knowledge_content.lower().split())

        if not query_words or not knowledge_words:
            return 0.0

        # Jaccard相似度
        intersection = len(query_words & knowledge_words)
        union = len(query_words | knowledge_words)

        return intersection / union if union > 0 else 0.0

    def _calculate_temporal_relevance(self, time_preference: str, knowledge: Dict) -> float:
        """计算时间相关性"""
        knowledge_age_days = (time.time() - knowledge.get("created_at", time.time())) / 86400

        if time_preference == "recent":
            # 偏好最近的知识
            if knowledge_age_days < 1:
                return 1.0
            elif knowledge_age_days < 7:
                return 0.8
            elif knowledge_age_days < 30:
                return 0.6
            else:
                return 0.4
        elif time_preference == "historical":
            # 偏好历史知识
            if knowledge_age_days > 30:
                return 1.0
            elif knowledge_age_days > 7:
                return 0.8
            else:
                return 0.6
        else:
            # 中性时间偏好
            return 0.7

    def add_dependency(self, knowledge_id: str, depends_on: str):
        """添加知识依赖关系"""
        self.knowledge_dependencies[knowledge_id].append(depends_on)

    def get_knowledge_stats(self) -> Dict:
        """获取知识库统计信息"""
        return {
            "total_knowledge_items": len(self.knowledge_store),
            "total_versions": sum(len(versions) for versions in self.version_history.values()),
            "total_accesses": len(self.access_log),
            "knowledge_types": list(set(k["type"] for k in self.knowledge_store.values())),
            "most_accessed": max(self.knowledge_store.items(),
                               key=lambda x: x[1]["access_count"], default=(None, {"access_count": 0}))[0]
        }

class AgentDependencyManager:
    """智能体依赖关系管理器"""

    def __init__(self):
        self.dependencies: Dict[str, List[str]] = defaultdict(list)
        self.dependency_weights: Dict[str, Dict[str, float]] = defaultdict(dict)
        self.execution_order: List[str] = []
        self.collaboration_patterns: Dict[str, List[Dict]] = defaultdict(list)

        logger.info("智能体依赖关系管理器初始化完成")

    def add_dependency(self, dependent: str, dependency: str, weight: float = 1.0,
                      dependency_type: str = "sequential"):
        """添加依赖关系"""
        self.dependencies[dependent].append(dependency)
        self.dependency_weights[dependent][dependency] = weight

        # 记录协作模式
        self.collaboration_patterns[dependent].append({
            "dependency": dependency,
            "type": dependency_type,
            "weight": weight,
            "created_at": time.time()
        })

        logger.info(f"添加依赖关系: {dependent} 依赖于 {dependency} (权重: {weight})")

    def get_execution_order(self, agents: List[str]) -> List[str]:
        """获取智能体执行顺序（拓扑排序）"""
        # 简化的拓扑排序实现
        in_degree = {agent: 0 for agent in agents}

        # 计算入度
        for agent in agents:
            for dep in self.dependencies.get(agent, []):
                if dep in in_degree:
                    in_degree[agent] += 1

        # 拓扑排序
        queue = [agent for agent in agents if in_degree[agent] == 0]
        result = []

        while queue:
            current = queue.pop(0)
            result.append(current)

            # 更新依赖于当前智能体的其他智能体
            for agent in agents:
                if current in self.dependencies.get(agent, []):
                    in_degree[agent] -= 1
                    if in_degree[agent] == 0:
                        queue.append(agent)

        self.execution_order = result
        return result

    def check_dependency_satisfaction(self, agent: str, completed_agents: List[str]) -> bool:
        """检查依赖关系是否满足"""
        dependencies = self.dependencies.get(agent, [])
        return all(dep in completed_agents for dep in dependencies)

    def get_collaboration_recommendations(self, agent: str, context: Dict) -> List[Dict]:
        """获取协作建议"""
        recommendations = []

        # 基于历史协作模式
        patterns = self.collaboration_patterns.get(agent, [])
        for pattern in patterns:
            if pattern["weight"] > 0.7:  # 高权重依赖
                recommendations.append({
                    "type": "high_priority_collaboration",
                    "target_agent": pattern["dependency"],
                    "reason": f"历史协作权重高 ({pattern['weight']:.2f})",
                    "priority": pattern["weight"]
                })

        return recommendations

class CollaborativeReasoningEngine:
    """协作推理增强引擎"""

    def __init__(self, message_bus: MessageBus, knowledge_base: SharedKnowledgeBase):
        self.message_bus = message_bus
        self.knowledge_base = knowledge_base
        self.reasoning_sessions: Dict[str, Dict] = {}
        self.inference_cache: Dict[str, Dict] = {}

        logger.info("协作推理增强引擎初始化完成")

    def start_collaborative_reasoning(self, session_id: str, participants: List[str],
                                    problem: Dict, context: Dict) -> Dict:
        """启动协作推理会话"""
        session = {
            "id": session_id,
            "participants": participants,
            "problem": problem,
            "context": context,
            "start_time": time.time(),
            "reasoning_steps": [],
            "shared_insights": [],
            "consensus_level": 0.0,
            "status": "active"
        }

        self.reasoning_sessions[session_id] = session

        # 通知参与者
        for participant in participants:
            message = AgentMessage(
                sender_id="reasoning_engine",
                receiver_id=participant,
                message_type=MessageType.COLLABORATION_REQUEST,
                content={
                    "action": "join_reasoning_session",
                    "session_id": session_id,
                    "problem": problem,
                    "context": context
                },
                requires_response=True
            )
            self.message_bus.publish(message)

        logger.info(f"启动协作推理会话 {session_id}，参与者: {participants}")
        return session

    def add_reasoning_step(self, session_id: str, agent_id: str,
                          reasoning: Dict) -> bool:
        """添加推理步骤"""
        if session_id not in self.reasoning_sessions:
            return False

        step = {
            "agent_id": agent_id,
            "reasoning": reasoning,
            "timestamp": time.time(),
            "confidence": reasoning.get("confidence", 0.8),
            "evidence": reasoning.get("evidence", [])
        }

        self.reasoning_sessions[session_id]["reasoning_steps"].append(step)

        # 分享推理结果给其他参与者
        session = self.reasoning_sessions[session_id]
        for participant in session["participants"]:
            if participant != agent_id:
                message = AgentMessage(
                    sender_id=agent_id,
                    receiver_id=participant,
                    message_type=MessageType.KNOWLEDGE_SHARE,
                    content={
                        "action": "reasoning_step_shared",
                        "session_id": session_id,
                        "reasoning": reasoning
                    }
                )
                self.message_bus.publish(message)

        # 存储到共享知识库
        knowledge_id = f"reasoning_{session_id}_{agent_id}_{len(session['reasoning_steps'])}"
        self.knowledge_base.store_knowledge(
            knowledge_id=knowledge_id,
            content={
                "reasoning": reasoning,
                "session_id": session_id,
                "confidence": step["confidence"],
                "tags": ["reasoning", "collaborative", session_id]
            },
            contributor_id=agent_id,
            knowledge_type="reasoning_step"
        )

        return True

    def synthesize_collaborative_insights(self, session_id: str) -> Dict:
        """综合协作洞察"""
        if session_id not in self.reasoning_sessions:
            return {}

        session = self.reasoning_sessions[session_id]
        reasoning_steps = session["reasoning_steps"]

        if not reasoning_steps:
            return {"insights": [], "consensus_level": 0.0}

        # 分析推理步骤的一致性
        insights = []
        consensus_scores = []

        # 按主题聚类推理步骤
        topic_groups = defaultdict(list)
        for step in reasoning_steps:
            topic = step["reasoning"].get("topic", "general")
            topic_groups[topic].append(step)

        # 为每个主题生成洞察
        for topic, steps in topic_groups.items():
            if len(steps) > 1:
                # 计算一致性
                confidences = [step["confidence"] for step in steps]
                avg_confidence = sum(confidences) / len(confidences)

                # 提取共同观点
                common_elements = self._extract_common_elements(steps)

                insight = {
                    "topic": topic,
                    "participant_count": len(set(step["agent_id"] for step in steps)),
                    "average_confidence": avg_confidence,
                    "common_elements": common_elements,
                    "consensus_score": self._calculate_consensus_score(steps)
                }

                insights.append(insight)
                consensus_scores.append(insight["consensus_score"])

        # 计算整体共识水平
        overall_consensus = sum(consensus_scores) / len(consensus_scores) if consensus_scores else 0.0

        session["shared_insights"] = insights
        session["consensus_level"] = overall_consensus

        return {
            "insights": insights,
            "consensus_level": overall_consensus,
            "total_reasoning_steps": len(reasoning_steps),
            "active_participants": len(set(step["agent_id"] for step in reasoning_steps))
        }

    def _extract_common_elements(self, steps: List[Dict]) -> List[str]:
        """提取共同元素"""
        # 简化实现：提取共同关键词
        all_keywords = []
        for step in steps:
            reasoning_text = str(step["reasoning"])
            # 这里可以使用更复杂的NLP技术
            keywords = reasoning_text.split()
            all_keywords.extend(keywords)

        # 统计词频
        from collections import Counter
        word_counts = Counter(all_keywords)

        # 返回出现频率高的词
        common_threshold = len(steps) * 0.6  # 60%的步骤中都出现
        common_elements = [word for word, count in word_counts.items()
                          if count >= common_threshold and len(word) > 2]

        return common_elements[:10]  # 返回前10个

    def _calculate_consensus_score(self, steps: List[Dict]) -> float:
        """计算共识分数 - 真正的内容相似性计算"""
        if len(steps) < 2:
            return 1.0

        # 1. 基于置信度的一致性
        confidences = [step["confidence"] for step in steps]
        mean_confidence = sum(confidences) / len(confidences)
        confidence_variance = sum((c - mean_confidence)**2 for c in confidences) / len(confidences)
        confidence_consensus = max(0.0, 1.0 - confidence_variance)

        # 2. 基于内容的真正相似性计算
        content_similarity = self._calculate_content_consensus(steps)

        # 3. 基于推理逻辑的一致性
        reasoning_consistency = self._calculate_reasoning_consistency(steps)

        # 4. 基于证据支持的一致性
        evidence_consistency = self._calculate_evidence_consistency(steps)

        # 动态权重计算
        weights = self._calculate_consensus_weights(steps)

        # 加权总分
        total_consensus = (
            weights["confidence"] * confidence_consensus +
            weights["content"] * content_similarity +
            weights["reasoning"] * reasoning_consistency +
            weights["evidence"] * evidence_consistency
        )

        return min(1.0, max(0.0, total_consensus))

    def _calculate_content_consensus(self, steps: List[Dict]) -> float:
        """计算内容共识度"""
        if len(steps) < 2:
            return 1.0

        # 提取所有推理内容
        contents = []
        for step in steps:
            reasoning = step.get("reasoning", {})
            content = str(reasoning.get("conclusion", "")) + " " + str(reasoning.get("position", ""))
            contents.append(content.strip())

        # 计算两两相似度
        similarities = []
        for i in range(len(contents)):
            for j in range(i + 1, len(contents)):
                sim = self._calculate_text_similarity(contents[i], contents[j])
                similarities.append(sim)

        return sum(similarities) / len(similarities) if similarities else 0.0

    def _calculate_reasoning_consistency(self, steps: List[Dict]) -> float:
        """计算推理逻辑一致性"""
        if len(steps) < 2:
            return 1.0

        # 提取推理关键词
        reasoning_keywords = []
        for step in steps:
            reasoning = step.get("reasoning", {})
            keywords = self._extract_reasoning_keywords(reasoning)
            reasoning_keywords.append(keywords)

        # 计算关键词重叠度
        if not reasoning_keywords:
            return 0.0

        # 计算所有步骤的关键词交集
        common_keywords = set(reasoning_keywords[0])
        for keywords in reasoning_keywords[1:]:
            common_keywords &= set(keywords)

        # 计算平均关键词数量
        avg_keywords = sum(len(keywords) for keywords in reasoning_keywords) / len(reasoning_keywords)

        if avg_keywords == 0:
            return 0.0

        return len(common_keywords) / avg_keywords

    def _calculate_evidence_consistency(self, steps: List[Dict]) -> float:
        """计算证据支持一致性"""
        if len(steps) < 2:
            return 1.0

        # 提取证据信息
        evidence_sets = []
        for step in steps:
            evidence = step.get("evidence", [])
            if isinstance(evidence, list):
                evidence_sets.append(set(str(e) for e in evidence))
            else:
                evidence_sets.append(set())

        if not evidence_sets or all(len(es) == 0 for es in evidence_sets):
            return 0.5  # 中性分数，没有证据信息

        # 计算证据重叠度
        overlaps = []
        for i in range(len(evidence_sets)):
            for j in range(i + 1, len(evidence_sets)):
                if len(evidence_sets[i]) == 0 and len(evidence_sets[j]) == 0:
                    overlaps.append(1.0)
                elif len(evidence_sets[i]) == 0 or len(evidence_sets[j]) == 0:
                    overlaps.append(0.0)
                else:
                    intersection = len(evidence_sets[i] & evidence_sets[j])
                    union = len(evidence_sets[i] | evidence_sets[j])
                    overlaps.append(intersection / union if union > 0 else 0.0)

        return sum(overlaps) / len(overlaps) if overlaps else 0.0

    def _calculate_consensus_weights(self, steps: List[Dict]) -> Dict[str, float]:
        """动态计算共识权重"""
        # 基础权重
        base_weights = {
            "confidence": 0.3,
            "content": 0.4,
            "reasoning": 0.2,
            "evidence": 0.1
        }

        # 根据步骤特征调整权重
        has_rich_content = any(
            len(str(step.get("reasoning", {}))) > 50 for step in steps
        )
        has_evidence = any(
            len(step.get("evidence", [])) > 0 for step in steps
        )

        if has_rich_content:
            base_weights["content"] *= 1.2
            base_weights["reasoning"] *= 1.1

        if has_evidence:
            base_weights["evidence"] *= 2.0

        # 归一化权重
        total_weight = sum(base_weights.values())
        return {k: v / total_weight for k, v in base_weights.items()}

    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """计算文本相似度"""
        if not text1 or not text2:
            return 0.0

        # 转换为词集合
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())

        if not words1 or not words2:
            return 0.0

        # Jaccard相似度
        intersection = len(words1 & words2)
        union = len(words1 | words2)

        return intersection / union if union > 0 else 0.0

    def _extract_reasoning_keywords(self, reasoning: Dict) -> List[str]:
        """提取推理关键词"""
        keywords = []

        # 从推理内容中提取关键词
        for key in ["conclusion", "position", "justification", "topic"]:
            if key in reasoning:
                text = str(reasoning[key])
                # 简单的关键词提取：长度大于2的词
                words = [word for word in text.split() if len(word) > 2]
                keywords.extend(words)

        return list(set(keywords))  # 去重

class EvidenceGuidedExtractor:
    """证据权重引导的信息抽取器 - 核心创新技术"""

    def __init__(self):
        # 直接复用现有的证据权重分析器
        self.evidence_analyzer = EvidenceWeightAnalyzer(get_completion)

    def guide_extraction_focus(self, text: str, preliminary_results: List[ExtractionResult]) -> Dict[str, float]:
        """
        基于证据权重分析指导信息抽取的重点区域

        Args:
            text: 原始司法文本
            preliminary_results: 初步抽取结果

        Returns:
            文本区域的重要性权重映射
        """
        logger.info("开始证据权重引导的抽取策略分析")

        # 将抽取结果转换为Evidence对象
        evidence_list = self._convert_to_evidence_objects(preliminary_results)

        if not evidence_list:
            logger.warning("没有可用的证据进行权重分析")
            return {}

        try:
            # 使用现有的证据权重分析技术
            weight_analysis = self.evidence_analyzer.analyze_evidence_weights(
                fact=text,
                evidence_list=evidence_list
            )

            # 基于权重分析生成抽取指导
            extraction_guidance = self._generate_extraction_guidance(weight_analysis, text)

            logger.info(f"生成了 {len(extraction_guidance)} 个重点抽取区域")
            return extraction_guidance

        except Exception as e:
            logger.error(f"证据权重分析失败: {e}")
            return {}

    def _convert_to_evidence_objects(self, extraction_results: List[ExtractionResult]) -> List[Evidence]:
        """将抽取结果转换为Evidence对象"""
        evidence_list = []

        for i, result in enumerate(extraction_results):
            # 将所有抽取结果都转换为证据对象，用于权重分析
            # 不同类型的信息都可以作为证据进行分析
            evidence_type = self._map_info_type_to_evidence_type(result.info_type)

            evidence = Evidence(
                id=f"extracted_evidence_{i}_{result.info_type.value}",
                content=result.content,
                type=evidence_type,
                source=f"extraction_agent_{result.agent_id}"
            )
            # 设置证据属性
            evidence.weight = result.evidence_weight
            evidence.reliability = result.confidence
            evidence.relevance = 0.8  # 默认相关性
            evidence.consistency = 0.8  # 默认一致性
            evidence_list.append(evidence)

        return evidence_list

    def _map_info_type_to_evidence_type(self, info_type: InformationType) -> str:
        """将信息类型映射为证据类型"""
        from common_types import get_evidence_type
        return get_evidence_type(info_type)

    def _generate_extraction_guidance(self, weight_analysis: Dict, text: str) -> Dict[str, float]:
        """基于权重分析生成抽取指导"""
        guidance = {}

        try:
            # 获取关键证据信息
            key_evidence = weight_analysis.get("key_evidence", [])
            evidence_weights = weight_analysis.get("evidence_weights", {})

            # 为关键证据相关的文本区域分配高权重
            for evidence_id in key_evidence:
                if evidence_id in evidence_weights:
                    weight = evidence_weights[evidence_id]
                    # 在文本中查找证据相关内容的位置
                    evidence_positions = self._find_evidence_positions_in_text(evidence_id, text)
                    for pos in evidence_positions:
                        region_key = f"text_region_{pos[0]}_{pos[1]}"
                        guidance[region_key] = weight

            # 基于证据链完整性调整整体抽取策略
            chain_completeness = weight_analysis.get("chain_completeness", 0.0)
            if chain_completeness < 0.6:
                # 证据链不完整时，需要更仔细地抽取
                guidance["global_attention_boost"] = 0.2

            # 基于证据类型分布调整策略
            evidence_type_distribution = self._analyze_evidence_type_distribution(weight_analysis)
            guidance.update(evidence_type_distribution)

            logger.info(f"生成抽取指导，包含 {len(guidance)} 个指导项")
            return guidance

        except Exception as e:
            logger.error(f"生成抽取指导失败: {e}")
            return {"global_attention_boost": 0.1}  # 默认轻微提升

    def _find_evidence_positions_in_text(self, evidence_id: str, text: str) -> List[Tuple[int, int]]:
        """在文本中查找证据相关内容的位置"""
        positions = []

        # 简化实现：基于关键词匹配
        # 实际应用中可以使用更复杂的语义匹配
        keywords = evidence_id.split("_")
        for keyword in keywords:
            if len(keyword) > 2:  # 忽略过短的关键词
                import re
                matches = re.finditer(re.escape(keyword), text)
                for match in matches:
                    positions.append((match.start(), match.end()))

        return positions

    def _analyze_evidence_type_distribution(self, weight_analysis: Dict) -> Dict[str, float]:
        """分析证据类型分布，生成针对性指导"""
        type_guidance = {}

        evidence_weights = weight_analysis.get("evidence_weights", {})

        # 统计不同类型证据的权重分布
        physical_evidence_weight = 0.0
        witness_evidence_weight = 0.0
        document_evidence_weight = 0.0

        for evidence_id, weight in evidence_weights.items():
            if "物证" in evidence_id or "现场" in evidence_id:
                physical_evidence_weight = max(physical_evidence_weight, weight)
            elif "证人" in evidence_id or "证言" in evidence_id:
                witness_evidence_weight = max(witness_evidence_weight, weight)
            elif "文件" in evidence_id or "记录" in evidence_id:
                document_evidence_weight = max(document_evidence_weight, weight)

        # 基于证据类型权重生成指导
        if physical_evidence_weight > 0.8:
            type_guidance["focus_on_physical_evidence"] = 0.3
        if witness_evidence_weight > 0.7:
            type_guidance["focus_on_witness_statements"] = 0.2
        if document_evidence_weight > 0.7:
            type_guidance["focus_on_documents"] = 0.2

        return type_guidance

class ExtractionConflictResolver:
    """抽取冲突解决器 - 基于现有辩论框架的创新应用"""

    def __init__(self):
        # 直接复用现有的自适应辩论框架
        self.debate_framework = AdaptiveDebateFramework(get_completion)

    def resolve_extraction_conflicts(self, conflicting_results: List[List[ExtractionResult]]) -> List[ExtractionResult]:
        """
        使用辩论机制解决抽取冲突

        Args:
            conflicting_results: 冲突的抽取结果列表

        Returns:
            解决冲突后的最终抽取结果
        """
        logger.info("开始使用辩论机制解决抽取冲突")

        resolved_results = []

        for conflict_group in conflicting_results:
            if len(conflict_group) <= 1:
                # 没有冲突，直接添加
                resolved_results.extend(conflict_group)
                continue

            # 构建辩论参与者
            debate_participants = self._prepare_debate_participants(conflict_group)

            # 执行辩论解决
            resolution = self._conduct_extraction_debate(conflict_group, debate_participants)

            # 应用解决方案
            resolved_result = self._apply_debate_resolution(conflict_group, resolution)
            if resolved_result:
                resolved_results.append(resolved_result)

        logger.info(f"冲突解决完成，最终结果数量: {len(resolved_results)}")
        return resolved_results



    def _prepare_debate_participants(self, conflict_group: List[ExtractionResult]) -> Dict:
        """准备辩论参与者"""
        participants = {}

        for i, result in enumerate(conflict_group):
            participant_id = f"agent_{result.agent_id}_{i}"
            participants[participant_id] = {
                "position": result.content,
                "confidence": result.confidence,
                "evidence_weight": result.evidence_weight,
                "supporting_context": result.context,
                "agent_role": result.agent_id
            }

        return participants

    def _conduct_extraction_debate(self, conflict_group: List[ExtractionResult], participants: Dict) -> Dict:
        """执行抽取辩论"""
        try:
            # 使用现有辩论框架的简化版本
            # 这里可以调用adaptive_debate_framework的相关方法

            # 简化实现：基于证据权重和置信度选择最佳结果
            best_result = max(conflict_group,
                            key=lambda x: x.evidence_weight * x.confidence)

            resolution = {
                "winner": best_result.agent_id,
                "final_content": best_result.content,
                "confidence": best_result.confidence * 0.9,  # 辩论后略微降低置信度
                "evidence_weight": best_result.evidence_weight,
                "reasoning": f"基于证据权重({best_result.evidence_weight:.2f})和置信度({best_result.confidence:.2f})选择"
            }

            return resolution

        except Exception as e:
            logger.error(f"辩论执行失败: {e}")
            # 降级处理：选择置信度最高的结果
            best_result = max(conflict_group, key=lambda x: x.confidence)
            return {
                "winner": best_result.agent_id,
                "final_content": best_result.content,
                "confidence": best_result.confidence * 0.8,
                "evidence_weight": best_result.evidence_weight,
                "reasoning": "辩论失败，基于置信度选择"
            }



    def _apply_debate_resolution(self, conflict_group: List[ExtractionResult], resolution: Dict) -> Optional[ExtractionResult]:
        """应用辩论解决方案"""
        winner_agent = resolution["winner"]

        # 找到获胜的抽取结果
        for result in conflict_group:
            if result.agent_id == winner_agent:
                # 更新置信度和证据权重
                resolved_result = ExtractionResult(
                    info_type=result.info_type,
                    content=resolution["final_content"],
                    confidence=resolution["confidence"],
                    evidence_weight=resolution["evidence_weight"],
                    source_span=result.source_span,
                    context=result.context,
                    agent_id=result.agent_id,
                    metadata={
                        "resolution_method": "debate",
                        "reasoning": resolution["reasoning"],
                        "original_confidence": result.confidence
                    }
                )
                return resolved_result

        return None

class JudicialIECoordinator:
    """司法信息抽取协调器 - 主协调类（多智能体协作增强版本）"""

    def __init__(self, enable_parallel=True, enable_cache=True, cache_size=1000,
                 enable_collaboration=True):
        # 加载配置和知识库
        self.config = self._load_config()
        self.legal_knowledge = self._load_legal_knowledge()

        # 性能优化配置
        self.enable_parallel = enable_parallel
        self.enable_cache = enable_cache
        self.enable_collaboration = enable_collaboration

        # 初始化多智能体协作系统
        if self.enable_collaboration:
            self.message_bus = MessageBus()
            self.shared_knowledge = SharedKnowledgeBase()
            self.dependency_manager = AgentDependencyManager()
            self.reasoning_engine = CollaborativeReasoningEngine(
                self.message_bus, self.shared_knowledge
            )
            logger.info("多智能体协作系统已启用")
        else:
            self.message_bus = None
            self.shared_knowledge = None
            self.dependency_manager = None
            self.reasoning_engine = None

        # 初始化缓存系统
        if self.enable_cache:
            self.cache_system = LLMResponseCache(max_size=cache_size, cache_file="judicial_ie_cache.pkl")
            self.cached_get_completion = CachedLLMWrapper(get_completion, self.cache_system)
            logger.info("LLM缓存系统已启用")
        else:
            self.cache_system = None
            self.cached_get_completion = None

        # 初始化智能体
        self.agents = {}
        self._initialize_agents()

        # 设置智能体依赖关系
        if self.enable_collaboration:
            self._setup_agent_dependencies()

        # 初始化性能优化组件
        if self.enable_parallel and OPTIMIZATION_AVAILABLE:
            self.parallel_coordinator = ParallelExtractionCoordinator(self.agents, max_workers=4)
            logger.info("并行处理系统已启用")
        else:
            self.parallel_coordinator = None
            if self.enable_parallel:
                logger.warning("并行处理请求但优化组件不可用，降级到串行处理")

        # 初始化核心组件
        self.evidence_guided_extractor = EvidenceGuidedExtractor()
        self.conflict_resolver = ExtractionConflictResolver()

        # 性能监控（增强版）
        self.extraction_metrics = {
            "total_extractions": 0,
            "conflicts_resolved": 0,
            "evidence_guided_improvements": 0,
            "parallel_extractions": 0,
            "cache_hits": 0,
            "time_saved": 0.0,
            "collaboration_sessions": 0,
            "knowledge_sharing_events": 0,
            "consensus_achieved": 0
        }

        logger.info("司法信息抽取协调器初始化完成（多智能体协作增强版本）")

    def _initialize_agents(self):
        """初始化专业抽取智能体"""
        try:
            # 导入智能体
            from extraction_agents.entity_extractor import EntityExtractionAgent
            from extraction_agents.fact_extractor import FactExtractionAgent
            from extraction_agents.legal_element_extractor import LegalElementAgent
            from extraction_agents.sentence_extractor import SentenceExtractionAgent

            # 初始化智能体 - 现在都是基于LLM的真正智能体
            self.agents = {
                "entity": EntityExtractionAgent(self.legal_knowledge),    # 基于LLM的智能实体抽取
                "fact": FactExtractionAgent(self.legal_knowledge),        # 基于LLM的智能事实抽取
                "legal": LegalElementAgent(self.legal_knowledge),         # 基于LLM的智能法律要素抽取
                "sentence": SentenceExtractionAgent(self.legal_knowledge) # 基于LLM的智能判决抽取
            }
            logger.info("智能体初始化完成，所有专业智能体已就绪")
            logger.info(f"已加载智能体: {list(self.agents.keys())}")
        except Exception as e:
            logger.error(f"智能体初始化失败: {e}")
            # 降级处理：使用占位符
            self.agents = {
                "entity": None,
                "fact": None,
                "legal": None,
                "sentence": None
            }

    def _setup_agent_dependencies(self):
        """设置智能体依赖关系"""
        if not self.enable_collaboration or not self.dependency_manager:
            return

        # 注册智能体到消息总线
        for agent_id, agent in self.agents.items():
            if agent is not None:
                agent_info = {
                    "type": agent_id,
                    "specialization": getattr(agent, 'specialization', []),
                    "capabilities": ["extraction", "analysis", "reasoning"]
                }
                self.message_bus.register_agent(agent_id, agent_info)

        # 设置依赖关系
        # 实体抽取是基础，其他智能体可能依赖其结果
        self.dependency_manager.add_dependency("fact", "entity", weight=0.7,
                                             dependency_type="informational")
        self.dependency_manager.add_dependency("legal", "entity", weight=0.6,
                                             dependency_type="informational")
        self.dependency_manager.add_dependency("legal", "fact", weight=0.8,
                                             dependency_type="sequential")
        self.dependency_manager.add_dependency("sentence", "legal", weight=0.9,
                                             dependency_type="sequential")

        logger.info("智能体依赖关系设置完成")

    def _enable_agent_collaboration(self, text: str, task_type: ExtractionTaskType) -> str:
        """启用智能体协作模式"""
        if not self.enable_collaboration:
            return None

        # 创建协作会话
        session_id = f"extraction_{int(time.time())}_{hash(text) % 10000}"
        participants = [agent_id for agent_id, agent in self.agents.items() if agent is not None]

        # 启动协作推理会话
        problem = {
            "type": "information_extraction",
            "task_type": task_type.value,
            "text_length": len(text),
            "complexity": self._assess_text_complexity(text)
        }

        context = {
            "legal_knowledge": self.legal_knowledge,
            "extraction_config": self.config
        }

        session = self.reasoning_engine.start_collaborative_reasoning(
            session_id, participants, problem, context
        )

        self.extraction_metrics["collaboration_sessions"] += 1
        logger.info(f"启动协作会话 {session_id}，参与智能体: {participants}")

        return session_id

    def _assess_text_complexity(self, text: str) -> float:
        """评估文本复杂度"""
        # 简化的复杂度评估
        length_factor = min(1.0, len(text) / 1000)

        # 法律术语密度
        legal_terms = ["法条", "刑法", "判决", "有期徒刑", "证据", "被告人", "犯罪"]
        legal_density = sum(1 for term in legal_terms if term in text) / len(legal_terms)

        return 0.6 * length_factor + 0.4 * legal_density

    def extract_information(self, text: str, task_type: ExtractionTaskType = ExtractionTaskType.COMPREHENSIVE_EXTRACTION) -> Dict:
        """
        主要的信息抽取接口

        Args:
            text: 待抽取的司法文本
            task_type: 抽取任务类型

        Returns:
            抽取结果字典
        """
        logger.info(f"开始司法信息抽取，任务类型: {task_type.value}")
        start_time = time.time()

        try:
            # 启用协作模式（如果可用）
            collaboration_session_id = None
            if self.enable_collaboration:
                collaboration_session_id = self._enable_agent_collaboration(text, task_type)

            # 第一阶段：协作式初步抽取
            preliminary_results = self._collaborative_preliminary_extraction(
                text, task_type, collaboration_session_id
            )

            # 第二阶段：证据权重引导的精细化抽取
            guided_results = self._evidence_guided_refinement(text, preliminary_results)

            # 第三阶段：协作式冲突检测和解决
            final_results = self._collaborative_conflict_resolution(
                guided_results, collaboration_session_id
            )

            # 第四阶段：结果整合和质量评估
            integrated_results = self._integrate_and_assess_results(final_results, text)

            # 第五阶段：协作洞察综合（如果启用协作）
            if collaboration_session_id and self.reasoning_engine:
                collaboration_insights = self.reasoning_engine.synthesize_collaborative_insights(
                    collaboration_session_id
                )
                integrated_results["collaboration_insights"] = collaboration_insights

                # 更新协作指标
                if collaboration_insights.get("consensus_level", 0) > 0.7:
                    self.extraction_metrics["consensus_achieved"] += 1

            # 更新性能指标
            self.extraction_metrics["total_extractions"] += len(final_results)

            extraction_time = time.time() - start_time
            logger.info(f"信息抽取完成，耗时: {extraction_time:.2f}秒")

            return {
                "status": "success",
                "results": integrated_results,
                "collaboration_session_id": collaboration_session_id,
                "metrics": {
                    "extraction_time": extraction_time,
                    "total_results": len(final_results),
                    "performance_metrics": self.extraction_metrics
                }
            }

        except Exception as e:
            logger.error(f"信息抽取失败: {e}")
            return {
                "status": "error",
                "error": str(e),
                "results": []
            }

    def _load_config(self) -> Dict:
        """加载配置"""
        try:
            with open('config.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.warning("配置文件未找到，使用默认配置")
            return {
                "extraction_confidence_threshold": 0.6,
                "evidence_weight_threshold": 0.5,
                "max_extraction_rounds": 3
            }

    def _load_legal_knowledge(self) -> Dict:
        """加载法律知识库"""
        try:
            with open('legal_knowledge_base.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.warning("法律知识库文件未找到，使用默认知识库")
            return {}

    def _preliminary_extraction(self, text: str, task_type: ExtractionTaskType) -> List[ExtractionResult]:
        """初步信息抽取 - 性能优化版本（支持并行处理）"""
        logger.info("执行初步信息抽取")
        start_time = time.time()

        # 优先使用并行处理
        if self.enable_parallel and self.parallel_coordinator:
            logger.info("使用并行处理模式")
            preliminary_results = self.parallel_coordinator.parallel_extract(text, task_type)
            self.extraction_metrics["parallel_extractions"] += 1

            # 获取并行处理性能统计
            parallel_stats = self.parallel_coordinator.get_performance_report()
            self.extraction_metrics["time_saved"] += parallel_stats.get("time_saved", 0.0)

        else:
            # 降级到串行处理
            logger.info("使用串行处理模式")
            preliminary_results = self._serial_extraction(text, task_type)

        extraction_time = time.time() - start_time
        logger.info(f"初步抽取完成，总计获得 {len(preliminary_results)} 个结果，耗时 {extraction_time:.2f}秒")
        return preliminary_results

    def _serial_extraction(self, text: str, task_type: ExtractionTaskType) -> List[ExtractionResult]:
        """串行抽取（备用方法）"""
        preliminary_results = []

        # 性能优化：对于短文本，只运行核心智能体
        if len(text) < 100:
            logger.info("短文本检测，使用精简抽取模式")
            # 只运行实体和事实抽取
            if self.agents.get("entity") is not None:
                entity_results = self.agents["entity"].extract(text, task_type)
                preliminary_results.extend(entity_results)
                logger.info(f"实体抽取完成，获得 {len(entity_results)} 个结果")

            if self.agents.get("fact") is not None:
                fact_results = self.agents["fact"].extract(text, task_type)
                preliminary_results.extend(fact_results)
                logger.info(f"事实抽取完成，获得 {len(fact_results)} 个结果")
        else:
            # 完整抽取模式
            # 实体抽取
            if task_type in [ExtractionTaskType.ENTITY_EXTRACTION, ExtractionTaskType.COMPREHENSIVE_EXTRACTION]:
                if self.agents.get("entity") is not None:
                    entity_results = self.agents["entity"].extract(text, task_type)
                    preliminary_results.extend(entity_results)
                    logger.info(f"实体抽取完成，获得 {len(entity_results)} 个结果")

            # 事实抽取
            if task_type in [ExtractionTaskType.FACT_EXTRACTION, ExtractionTaskType.COMPREHENSIVE_EXTRACTION]:
                if self.agents.get("fact") is not None:
                    fact_results = self.agents["fact"].extract(text, task_type)
                    preliminary_results.extend(fact_results)
                    logger.info(f"事实抽取完成，获得 {len(fact_results)} 个结果")

            # 法律要素抽取
            if task_type in [ExtractionTaskType.LEGAL_ELEMENT_EXTRACTION, ExtractionTaskType.COMPREHENSIVE_EXTRACTION]:
                if self.agents.get("legal") is not None:
                    legal_results = self.agents["legal"].extract(text, task_type)
                    preliminary_results.extend(legal_results)
                    logger.info(f"法律要素抽取完成，获得 {len(legal_results)} 个结果")

            # 判决抽取
            if task_type in [ExtractionTaskType.SENTENCE_EXTRACTION, ExtractionTaskType.COMPREHENSIVE_EXTRACTION]:
                if self.agents.get("sentence") is not None:
                    sentence_results = self.agents["sentence"].extract(text, task_type)
                    preliminary_results.extend(sentence_results)
                    logger.info(f"判决抽取完成，获得 {len(sentence_results)} 个结果")

        return preliminary_results

    def _collaborative_preliminary_extraction(self, text: str, task_type: ExtractionTaskType,
                                            collaboration_session_id: Optional[str]) -> List[ExtractionResult]:
        """协作式初步信息抽取"""
        if not self.enable_collaboration or not collaboration_session_id:
            # 降级到标准抽取
            return self._preliminary_extraction(text, task_type)

        logger.info("执行协作式初步信息抽取")
        start_time = time.time()

        # 获取智能体执行顺序
        agent_names = [name for name, agent in self.agents.items() if agent is not None]
        execution_order = self.dependency_manager.get_execution_order(agent_names)

        preliminary_results = []
        completed_agents = []
        shared_context = {}

        for agent_name in execution_order:
            agent = self.agents.get(agent_name)
            if agent is None:
                continue

            # 检查依赖关系
            if not self.dependency_manager.check_dependency_satisfaction(agent_name, completed_agents):
                logger.warning(f"智能体 {agent_name} 的依赖关系未满足，跳过")
                continue

            logger.info(f"执行智能体 {agent_name} 的协作抽取")

            # 从共享知识库获取相关信息
            relevant_knowledge = self._get_relevant_knowledge_for_agent(
                agent_name, text, shared_context
            )

            # 执行抽取
            agent_results = agent.extract(text, task_type)

            # 分享抽取结果到知识库
            if agent_results:
                self._share_extraction_results(
                    agent_name, agent_results, collaboration_session_id
                )

                # 更新共享上下文
                shared_context[agent_name] = {
                    "results": agent_results,
                    "timestamp": time.time(),
                    "result_count": len(agent_results)
                }

            preliminary_results.extend(agent_results)
            completed_agents.append(agent_name)

            # 添加推理步骤到协作会话
            if self.reasoning_engine:
                reasoning = {
                    "agent": agent_name,
                    "topic": f"{agent_name}_extraction",
                    "results_summary": f"抽取了 {len(agent_results)} 个结果",
                    "confidence": sum(r.confidence for r in agent_results) / len(agent_results) if agent_results else 0.0,
                    "evidence": [r.content for r in agent_results[:3]]  # 前3个结果作为证据
                }
                self.reasoning_engine.add_reasoning_step(
                    collaboration_session_id, agent_name, reasoning
                )

        extraction_time = time.time() - start_time
        logger.info(f"协作式初步抽取完成，总计获得 {len(preliminary_results)} 个结果，耗时 {extraction_time:.2f}秒")

        return preliminary_results

    def _evidence_guided_refinement(self, text: str, preliminary_results: List[ExtractionResult]) -> List[ExtractionResult]:
        """证据权重引导的精细化抽取 - 性能优化版本"""
        logger.info("执行证据权重引导的精细化抽取")

        if not preliminary_results:
            logger.info("没有初步抽取结果，跳过证据权重引导")
            return preliminary_results

        # 性能优化：对于小规模抽取结果，使用简化的权重调整
        if len(preliminary_results) <= 15:  # 提高阈值，更多情况使用简化版本
            logger.info("使用简化的证据权重调整（性能优化）")
            return self._simplified_evidence_refinement(preliminary_results)

        try:
            # 获取抽取指导
            extraction_guidance = self.evidence_guided_extractor.guide_extraction_focus(text, preliminary_results)

            if not extraction_guidance:
                logger.info("未获得有效的抽取指导，使用原始结果")
                return preliminary_results

            # 基于证据权重调整抽取结果的权重
            refined_results = []
            for result in preliminary_results:
                # 检查该结果是否在重点关注区域
                enhanced_weight = self._calculate_enhanced_evidence_weight(result, extraction_guidance)

                # 创建增强的抽取结果
                refined_result = ExtractionResult(
                    info_type=result.info_type,
                    content=result.content,
                    confidence=result.confidence,
                    evidence_weight=enhanced_weight,
                    source_span=result.source_span,
                    context=result.context,
                    agent_id=result.agent_id,
                    metadata={
                        **(result.metadata or {}),
                        "evidence_guided": True,
                        "original_weight": result.evidence_weight,
                        "enhancement_factor": enhanced_weight / result.evidence_weight if result.evidence_weight > 0 else 1.0
                    }
                )
                refined_results.append(refined_result)

            # 更新性能指标
            self.extraction_metrics["evidence_guided_improvements"] += len(refined_results)

            logger.info(f"证据权重引导完成，处理了 {len(refined_results)} 个结果")
            return refined_results

        except Exception as e:
            logger.error(f"证据权重引导过程出错: {e}")
            return preliminary_results

    def _simplified_evidence_refinement(self, preliminary_results: List[ExtractionResult]) -> List[ExtractionResult]:
        """简化的证据权重调整 - 用于性能优化"""
        refined_results = []

        for result in preliminary_results:
            # 简单的权重提升策略
            enhanced_weight = min(1.0, result.evidence_weight * 1.1)  # 轻微提升权重

            refined_result = ExtractionResult(
                info_type=result.info_type,
                content=result.content,
                confidence=result.confidence,
                evidence_weight=enhanced_weight,
                source_span=result.source_span,
                context=result.context,
                agent_id=result.agent_id,
                metadata={
                    **(result.metadata or {}),
                    "evidence_guided": True,
                    "simplified_refinement": True,
                    "original_weight": result.evidence_weight
                }
            )
            refined_results.append(refined_result)

        # 更新性能指标
        self.extraction_metrics["evidence_guided_improvements"] += len(refined_results)

        logger.info(f"简化证据权重调整完成，处理了 {len(refined_results)} 个结果")
        return refined_results

    def _calculate_enhanced_evidence_weight(self, result: ExtractionResult, guidance: Dict[str, float]) -> float:
        """基于抽取指导计算增强的证据权重"""
        base_weight = result.evidence_weight

        # 检查是否有全局注意力提升
        global_boost = guidance.get("global_attention_boost", 0.0)

        # 检查是否在重点证据区域 - 优化匹配逻辑
        evidence_boost = 0.0
        result_content_lower = result.content.lower()

        for evidence_id, weight in guidance.items():
            if evidence_id != "global_attention_boost":
                # 改进匹配逻辑：使用更精确的关键词匹配
                evidence_keywords = evidence_id.replace("text_region_", "").split("_")
                for keyword in evidence_keywords:
                    if len(keyword) > 2 and keyword.lower() in result_content_lower:
                        evidence_boost = max(evidence_boost, weight * 0.2)  # 最多提升20%
                        break

        # 计算最终权重
        enhanced_weight = base_weight + global_boost + evidence_boost

        # 确保权重在合理范围内
        return min(1.0, max(0.0, enhanced_weight))

    def _resolve_conflicts(self, results: List[ExtractionResult]) -> List[ExtractionResult]:
        """解决抽取冲突"""
        logger.info("检测和解决抽取冲突")

        # 检测冲突
        conflicts = self._detect_conflicts(results)

        if not conflicts:
            return results

        # 解决冲突
        resolved_results = self.conflict_resolver.resolve_extraction_conflicts(conflicts)
        self.extraction_metrics["conflicts_resolved"] += len(conflicts)

        return resolved_results

    def _detect_conflicts(self, results: List[ExtractionResult]) -> List[List[ExtractionResult]]:
        """检测抽取结果中的冲突"""
        conflicts = []

        # 按信息类型分组
        type_groups = {}
        for result in results:
            if result.info_type not in type_groups:
                type_groups[result.info_type] = []
            type_groups[result.info_type].append(result)

        # 检测每个类型内的冲突
        for info_type, group_results in type_groups.items():
            if len(group_results) > 1:
                # 检查内容是否冲突
                contents = set(result.content for result in group_results)
                if len(contents) > 1:
                    conflicts.append(group_results)

        return conflicts

    def _get_relevant_knowledge_for_agent(self, agent_name: str, text: str,
                                        shared_context: Dict) -> List[Dict]:
        """为智能体获取相关知识"""
        if not self.shared_knowledge:
            return []

        # 构建搜索查询
        query = {
            "type": f"{agent_name}_extraction",
            "keywords": text.split()[:10],  # 前10个词作为关键词
            "min_confidence": 0.6
        }

        # 搜索相关知识
        relevant_knowledge = self.shared_knowledge.search_knowledge(query, agent_name)

        return relevant_knowledge[:5]  # 返回前5个最相关的知识

    def _share_extraction_results(self, agent_name: str, results: List[ExtractionResult],
                                session_id: str):
        """分享抽取结果到共享知识库"""
        if not self.shared_knowledge:
            return

        for i, result in enumerate(results):
            knowledge_id = f"{session_id}_{agent_name}_result_{i}"
            content = {
                "result": result.to_dict(),
                "agent": agent_name,
                "session": session_id,
                "confidence": result.confidence,
                "tags": [agent_name, "extraction_result", result.info_type.value]
            }

            self.shared_knowledge.store_knowledge(
                knowledge_id, content, agent_name, f"{agent_name}_extraction"
            )

        # 发送知识分享消息
        if self.message_bus:
            message = AgentMessage(
                sender_id=agent_name,
                message_type=MessageType.KNOWLEDGE_SHARE,
                content={
                    "action": "extraction_results_shared",
                    "session_id": session_id,
                    "result_count": len(results),
                    "summary": f"{agent_name} 分享了 {len(results)} 个抽取结果"
                }
            )
            self.message_bus.publish(message)
            self.extraction_metrics["knowledge_sharing_events"] += 1

    def _collaborative_conflict_resolution(self, results: List[ExtractionResult],
                                         collaboration_session_id: Optional[str]) -> List[ExtractionResult]:
        """协作式冲突检测和解决"""
        if not self.enable_collaboration or not collaboration_session_id:
            # 降级到标准冲突解决
            return self._resolve_conflicts(results)

        logger.info("执行协作式冲突检测和解决")

        # 检测冲突
        conflicts = self._detect_conflicts(results)

        if not conflicts:
            return results

        # 使用协作机制解决冲突
        resolved_results = []

        for conflict_group in conflicts:
            if len(conflict_group) <= 1:
                resolved_results.extend(conflict_group)
                continue

            # 启动协作冲突解决会话
            conflict_session_id = f"{collaboration_session_id}_conflict_{len(resolved_results)}"

            # 收集冲突智能体
            conflict_agents = list(set(result.agent_id for result in conflict_group))

            # 创建冲突解决协作会话
            if self.reasoning_engine:
                conflict_problem = {
                    "type": "conflict_resolution",
                    "conflict_type": conflict_group[0].info_type.value,
                    "conflicting_results": [r.to_dict() for r in conflict_group]
                }

                conflict_context = {
                    "parent_session": collaboration_session_id,
                    "conflict_agents": conflict_agents
                }

                self.reasoning_engine.start_collaborative_reasoning(
                    conflict_session_id, conflict_agents, conflict_problem, conflict_context
                )

                # 让每个智能体提供冲突解决推理
                for agent_id in conflict_agents:
                    agent_results = [r for r in conflict_group if r.agent_id == agent_id]
                    reasoning = {
                        "agent": agent_id,
                        "topic": "conflict_resolution",
                        "position": f"支持结果: {agent_results[0].content if agent_results else '无'}",
                        "confidence": agent_results[0].confidence if agent_results else 0.0,
                        "justification": f"基于 {agent_id} 的专业分析"
                    }
                    self.reasoning_engine.add_reasoning_step(
                        conflict_session_id, agent_id, reasoning
                    )

                # 综合协作洞察
                insights = self.reasoning_engine.synthesize_collaborative_insights(conflict_session_id)

                # 基于协作洞察选择最佳结果
                if insights.get("consensus_level", 0) > 0.6:
                    # 有较好共识，选择置信度最高的结果
                    best_result = max(conflict_group, key=lambda x: x.confidence)
                    resolved_results.append(best_result)
                else:
                    # 共识不足，使用传统方法
                    traditional_resolution = self.conflict_resolver.resolve_extraction_conflicts([conflict_group])
                    resolved_results.extend(traditional_resolution)
            else:
                # 降级到传统冲突解决
                traditional_resolution = self.conflict_resolver.resolve_extraction_conflicts([conflict_group])
                resolved_results.extend(traditional_resolution)

        self.extraction_metrics["conflicts_resolved"] += len(conflicts)
        logger.info(f"协作式冲突解决完成，处理了 {len(conflicts)} 个冲突")

        return resolved_results

    def _integrate_and_assess_results(self, results: List[ExtractionResult], text: str) -> Dict:
        """整合和评估最终结果"""
        logger.info("整合和评估最终结果")

        # 按类型统计结果
        type_counts = {}
        total_confidence = 0.0
        total_evidence_weight = 0.0

        for result in results:
            info_type = result.info_type.value
            type_counts[info_type] = type_counts.get(info_type, 0) + 1
            total_confidence += result.confidence
            total_evidence_weight += result.evidence_weight

        # 计算平均指标
        avg_confidence = total_confidence / len(results) if results else 0.0
        avg_evidence_weight = total_evidence_weight / len(results) if results else 0.0

        # 获取缓存统计
        cache_stats = {}
        if self.enable_cache and self.cache_system:
            cache_stats = self.cache_system.get_stats()
            self.extraction_metrics["cache_hits"] = cache_stats.get("cache_hits", 0)

        # 获取并行处理统计
        parallel_stats = {}
        if self.enable_parallel and self.parallel_coordinator:
            parallel_stats = self.parallel_coordinator.get_performance_report()

        return {
            "extraction_summary": type_counts,
            "detailed_results": [result.to_dict() for result in results],
            "quality_metrics": {
                "total_extractions": len(results),
                "average_confidence": avg_confidence,
                "average_evidence_weight": avg_evidence_weight
            },
            "innovation_metrics": {
                "evidence_weight_utilization": avg_evidence_weight,
                "conflict_resolution_rate": self.extraction_metrics["conflicts_resolved"] / max(1, self.extraction_metrics["total_extractions"])
            },
            "performance_metrics": {
                "cache_stats": cache_stats,
                "parallel_stats": parallel_stats,
                "extraction_metrics": self.extraction_metrics
            }
        }

    def get_comprehensive_performance_report(self) -> Dict[str, Any]:
        """获取综合性能报告"""
        report = {
            "system_info": {
                "parallel_enabled": self.enable_parallel,
                "cache_enabled": self.enable_cache,
                "agents_loaded": list(self.agents.keys())
            },
            "extraction_metrics": self.extraction_metrics.copy()
        }

        # 缓存性能
        if self.enable_cache and self.cache_system:
            report["cache_performance"] = self.cache_system.get_stats()

        # 并行处理性能
        if self.enable_parallel and self.parallel_coordinator:
            report["parallel_performance"] = self.parallel_coordinator.get_performance_report()

        return report

    def save_cache(self):
        """保存缓存到文件"""
        if self.enable_cache and self.cache_system:
            self.cache_system.save_cache()
            logger.info("缓存已保存")

    def cleanup(self):
        """清理资源"""
        if self.enable_parallel and self.parallel_coordinator:
            self.parallel_coordinator.cleanup()

        if self.enable_cache and self.cache_system:
            self.cache_system.save_cache()
