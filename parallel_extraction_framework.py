"""
并行抽取框架 - 多智能体并行处理机制
目标：将执行时间从4.98秒优化到<3秒

核心特性：
1. 智能体异步并行调用
2. 动态负载均衡
3. 智能调度策略
4. 错误恢复机制
"""

import asyncio
import time
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum

# 导入共享类型定义
from extraction_types import ExtractionResult, ExtractionTaskType, InformationType

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ProcessingMode(Enum):
    """处理模式"""
    MINIMAL = "minimal"      # 极简模式：只运行实体抽取
    SIMPLIFIED = "simplified"  # 精简模式：实体+事实抽取
    STANDARD = "standard"    # 标准模式：实体+事实+法律要素
    COMPREHENSIVE = "comprehensive"  # 完整模式：所有智能体

@dataclass
class AgentTask:
    """智能体任务"""
    agent_name: str
    text: str
    task_type: ExtractionTaskType
    priority: int = 1
    estimated_time: float = 3.0

class IntelligentScheduler:
    """智能调度器"""

    def __init__(self):
        # 智能体性能统计
        self.agent_performance = {
            "entity": {"avg_time": 2.1, "success_rate": 0.98, "load": 0},
            "fact": {"avg_time": 3.2, "success_rate": 0.95, "load": 0},
            "legal": {"avg_time": 3.4, "success_rate": 0.92, "load": 0},
            "sentence": {"avg_time": 2.8, "success_rate": 0.90, "load": 0}
        }

        # 文本复杂度阈值
        self.complexity_thresholds = {
            "length_simple": 50,
            "length_medium": 100,
            "length_complex": 500
        }

    def analyze_text_complexity(self, text: str) -> Dict[str, Any]:
        """分析文本复杂度"""
        length = len(text)

        # 基础复杂度评分
        complexity_score = 0.0

        # 长度因子
        if length < self.complexity_thresholds["length_simple"]:
            length_factor = 0.2
        elif length < self.complexity_thresholds["length_medium"]:
            length_factor = 0.5
        elif length < self.complexity_thresholds["length_complex"]:
            length_factor = 0.8
        else:
            length_factor = 1.0

        # 内容复杂度因子
        content_indicators = {
            "法条": 0.3, "第.*条": 0.3, "刑法": 0.2,
            "判决": 0.2, "有期徒刑": 0.2, "罚金": 0.1,
            "证据": 0.2, "证人": 0.1, "物证": 0.2,
            "被告人": 0.1, "被害人": 0.1, "犯罪": 0.2
        }

        content_factor = 0.0
        for indicator, weight in content_indicators.items():
            if indicator in text:
                content_factor += weight

        content_factor = min(1.0, content_factor)

        # 综合复杂度
        complexity_score = 0.6 * length_factor + 0.4 * content_factor

        return {
            "length": length,
            "complexity_score": complexity_score,
            "length_factor": length_factor,
            "content_factor": content_factor
        }

    def determine_processing_mode(self, text: str, task_type: ExtractionTaskType) -> ProcessingMode:
        """确定处理模式"""
        analysis = self.analyze_text_complexity(text)
        length = analysis["length"]
        complexity = analysis["complexity_score"]

        # 强制完整模式的情况
        if task_type == ExtractionTaskType.COMPREHENSIVE_EXTRACTION and complexity > 0.7:
            return ProcessingMode.COMPREHENSIVE

        # 基于文本特征的智能选择
        if length < 30:
            return ProcessingMode.MINIMAL
        elif length < 80:
            return ProcessingMode.SIMPLIFIED
        elif complexity < 0.5:
            return ProcessingMode.STANDARD
        else:
            return ProcessingMode.COMPREHENSIVE

    def get_agent_schedule(self, mode: ProcessingMode) -> List[str]:
        """获取智能体调度方案"""
        schedules = {
            ProcessingMode.MINIMAL: ["entity"],
            ProcessingMode.SIMPLIFIED: ["entity", "fact"],
            ProcessingMode.STANDARD: ["entity", "fact", "legal"],
            ProcessingMode.COMPREHENSIVE: ["entity", "fact", "legal", "sentence"]
        }
        return schedules.get(mode, ["entity", "fact"])

    def estimate_execution_time(self, agents: List[str], parallel: bool = True) -> float:
        """估算执行时间"""
        if not agents:
            return 0.0

        if parallel:
            # 并行执行：取最长时间
            max_time = max(self.agent_performance[agent]["avg_time"] for agent in agents)
            # 考虑并行开销
            parallel_overhead = 0.3
            return max_time + parallel_overhead
        else:
            # 串行执行：累加时间
            return sum(self.agent_performance[agent]["avg_time"] for agent in agents)

class ParallelExtractionCoordinator:
    """并行抽取协调器"""

    def __init__(self, agents: Dict, max_workers: int = 4):
        self.agents = agents
        self.scheduler = IntelligentScheduler()
        self.executor = ThreadPoolExecutor(max_workers=max_workers)

        # 性能监控
        self.performance_stats = {
            "total_extractions": 0,
            "parallel_extractions": 0,
            "time_saved": 0.0,
            "cache_hits": 0,
            "errors": 0
        }

    def extract_with_agent(self, agent_name: str, text: str, task_type: ExtractionTaskType) -> Tuple[str, List[ExtractionResult]]:
        """单个智能体抽取"""
        try:
            start_time = time.time()
            agent = self.agents.get(agent_name)

            if agent is None:
                logger.warning(f"智能体 {agent_name} 不可用")
                return agent_name, []

            results = agent.extract(text, task_type)
            execution_time = time.time() - start_time

            # 更新性能统计
            self.scheduler.agent_performance[agent_name]["avg_time"] = (
                self.scheduler.agent_performance[agent_name]["avg_time"] * 0.8 +
                execution_time * 0.2
            )

            logger.info(f"智能体 {agent_name} 完成抽取，耗时 {execution_time:.2f}秒，结果数量 {len(results)}")
            return agent_name, results

        except Exception as e:
            logger.error(f"智能体 {agent_name} 抽取失败: {e}")
            self.performance_stats["errors"] += 1
            return agent_name, []

    def parallel_extract(self, text: str, task_type: ExtractionTaskType) -> List[ExtractionResult]:
        """并行抽取主方法"""
        logger.info(f"开始并行抽取，文本长度: {len(text)} 字符")
        start_time = time.time()

        # 智能调度
        processing_mode = self.scheduler.determine_processing_mode(text, task_type)
        scheduled_agents = self.scheduler.get_agent_schedule(processing_mode)

        logger.info(f"选择处理模式: {processing_mode.value}, 调度智能体: {scheduled_agents}")

        # 估算时间
        estimated_time = self.scheduler.estimate_execution_time(scheduled_agents, parallel=True)
        logger.info(f"预估执行时间: {estimated_time:.2f}秒")

        # 并行执行
        all_results = []

        if len(scheduled_agents) == 1:
            # 单智能体，直接执行
            agent_name, results = self.extract_with_agent(scheduled_agents[0], text, task_type)
            all_results.extend(results)
        else:
            # 多智能体并行执行
            future_to_agent = {}

            for agent_name in scheduled_agents:
                future = self.executor.submit(self.extract_with_agent, agent_name, text, task_type)
                future_to_agent[future] = agent_name

            # 收集结果
            for future in as_completed(future_to_agent):
                agent_name = future_to_agent[future]
                try:
                    _, results = future.result(timeout=30)  # 30秒超时
                    all_results.extend(results)
                except Exception as e:
                    logger.error(f"智能体 {agent_name} 执行异常: {e}")
                    self.performance_stats["errors"] += 1

        # 性能统计
        total_time = time.time() - start_time
        self.performance_stats["total_extractions"] += 1
        self.performance_stats["parallel_extractions"] += 1

        # 计算时间节省
        serial_time = self.scheduler.estimate_execution_time(scheduled_agents, parallel=False)
        time_saved = max(0, serial_time - total_time)
        self.performance_stats["time_saved"] += time_saved

        logger.info(f"并行抽取完成，总耗时: {total_time:.2f}秒，节省时间: {time_saved:.2f}秒")
        logger.info(f"抽取结果总数: {len(all_results)}")

        return all_results

    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        stats = self.performance_stats.copy()

        if stats["total_extractions"] > 0:
            stats["average_time_saved"] = stats["time_saved"] / stats["total_extractions"]
            stats["error_rate"] = stats["errors"] / stats["total_extractions"]
        else:
            stats["average_time_saved"] = 0.0
            stats["error_rate"] = 0.0

        # 添加智能体性能统计
        stats["agent_performance"] = self.scheduler.agent_performance.copy()

        return stats

    def cleanup(self):
        """清理资源"""
        self.executor.shutdown(wait=True)

# 使用示例和测试函数
def test_parallel_extraction():
    """测试并行抽取功能"""
    print("🚀 测试并行抽取框架")

    # 这里需要实际的智能体实例
    # 在实际使用中，从 JudicialIECoordinator 获取
    agents = {
        "entity": None,  # 实际使用时替换为真实智能体
        "fact": None,
        "legal": None,
        "sentence": None
    }

    coordinator = ParallelExtractionCoordinator(agents)

    test_cases = [
        "被告人张某持木棒击打李某头部。",  # 短文本
        "被告人王某于2023年3月15日晚上8时许，在北京市朝阳区某小区内，因琐事与被害人李某发生争执，后持木棒击打李某头部，致李某轻伤二级。经鉴定，李某的伤情构成轻伤二级。",  # 中等文本
    ]

    for i, text in enumerate(test_cases, 1):
        print(f"\n--- 测试案例 {i} ---")
        print(f"文本: {text}")
        print(f"长度: {len(text)} 字符")

        # 分析复杂度
        analysis = coordinator.scheduler.analyze_text_complexity(text)
        print(f"复杂度分析: {analysis}")

        # 确定处理模式
        mode = coordinator.scheduler.determine_processing_mode(text, ExtractionTaskType.COMPREHENSIVE_EXTRACTION)
        print(f"处理模式: {mode.value}")

        # 获取调度方案
        agents = coordinator.scheduler.get_agent_schedule(mode)
        print(f"调度智能体: {agents}")

        # 估算时间
        estimated_time = coordinator.scheduler.estimate_execution_time(agents, parallel=True)
        print(f"预估时间: {estimated_time:.2f}秒")

    # 性能报告
    report = coordinator.get_performance_report()
    print(f"\n📊 性能报告: {report}")

    coordinator.cleanup()

if __name__ == "__main__":
    test_parallel_extraction()
