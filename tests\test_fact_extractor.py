"""
测试事实抽取智能体

验证FactExtractionAgent的功能：
1. 基本事实抽取能力
2. 因果关系分析能力
3. 情节要素识别能力
4. 事实完整性验证机制
5. 与系统架构的一致性
"""

import logging
import json
from typing import Dict, List

# 导入事实抽取智能体
from extraction_agents.fact_extractor import FactExtractionAgent, ExtractionTaskType, InformationType

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FactExtractorTester:
    """事实抽取智能体测试器"""
    
    def __init__(self):
        self.test_cases = self._load_test_cases()
        
    def _load_test_cases(self) -> List[Dict]:
        """加载测试案例"""
        return [
            {
                "id": "complex_case_1",
                "text": """被告人张某于2023年3月15日晚上8时许，在北京市朝阳区某小区内，因琐事与被害人李某发生争执。张某情绪激动，用拳头击打李某面部，致李某鼻骨骨折。经法医鉴定，李某的伤情构成轻伤二级。案发后，张某主动投案自首，如实供述了犯罪事实，并积极赔偿被害人医疗费用5000元。张某系初犯，平时表现良好，认罪态度诚恳。但张某在案发前曾因类似纠纷被公安机关警告过一次。""",
                "expected_facts": {
                    "basic_facts": ["张某用拳头击打李某面部", "致李某鼻骨骨折"],
                    "mitigating_factors": ["主动投案自首", "如实供述犯罪事实", "积极赔偿医疗费用", "初犯", "认罪态度诚恳"],
                    "aggravating_factors": ["曾因类似纠纷被警告"],
                    "damages": ["李某鼻骨骨折，轻伤二级", "医疗费用5000元"]
                }
            },
            {
                "id": "theft_case_1",
                "text": """被告人王某某于2022年12月1日凌晨2时许，趁夜深人静之时，撬开上海市浦东新区某商场后门锁，进入商场内盗窃。王某某先后窃取了三部手机、两台笔记本电脑和现金2000元，总价值约15000元。案发后，王某某潜逃外地，直至2023年1月被公安机关抓获。王某某到案后拒不认罪，且系累犯，曾因盗窃罪被判处有期徒刑一年。""",
                "expected_facts": {
                    "basic_facts": ["撬开商场后门锁进入", "窃取手机、电脑和现金"],
                    "aggravating_factors": ["拒不认罪", "累犯", "曾因盗窃罪被判刑"],
                    "damages": ["财物损失总价值约15000元"]
                }
            },
            {
                "id": "simple_case_1",
                "text": """被告人刘某在广州市天河区体育西路某银行门口抢劫被害人周某的手机一部。案发后，被害人周某立即报警。""",
                "expected_facts": {
                    "basic_facts": ["刘某抢劫周某手机"],
                    "damages": ["手机一部"]
                }
            }
        ]
    
    def test_basic_fact_extraction(self):
        """测试基本事实抽取功能"""
        print("\n" + "="*60)
        print("🔍 测试1: 基本事实抽取功能验证")
        print("="*60)
        
        try:
            agent = FactExtractionAgent()
            
            for i, test_case in enumerate(self.test_cases, 1):
                print(f"\n📋 测试案例 {i}: {test_case['id']}")
                print(f"📄 文本长度: {len(test_case['text'])} 字符")
                
                # 执行事实抽取
                results = agent.extract(test_case['text'], ExtractionTaskType.FACT_EXTRACTION)
                
                print(f"🎯 抽取结果数量: {len(results)}")
                
                # 按类别统计结果
                fact_categories = {}
                for result in results:
                    category = result.metadata.get("fact_category", "unknown")
                    fact_categories[category] = fact_categories.get(category, 0) + 1
                
                print(f"📊 事实类别分布: {fact_categories}")
                
                # 显示部分抽取结果
                for j, result in enumerate(results[:5], 1):
                    category = result.metadata.get("fact_category", "unknown")
                    print(f"  {j}. [{category}] {result.content[:80]}... (置信度: {result.confidence:.2f}, 权重: {result.evidence_weight:.2f})")
                
                if len(results) > 5:
                    print(f"  ... 还有 {len(results) - 5} 个结果")
                
                # 检查API调用统计
                stats = agent.get_performance_stats()
                print(f"📊 API调用次数: {stats.get('api_calls', 0)}")
                print(f"📊 完整性分数: {stats.get('average_completeness_score', 0):.2f}")
                
                if results:
                    print(f"✅ 案例 {i} 事实抽取成功")
                else:
                    print(f"⚠️ 案例 {i} 未抽取到事实")
            
            return True
            
        except Exception as e:
            print(f"❌ 基本事实抽取测试失败: {e}")
            return False
    
    def test_fact_completeness_validation(self):
        """测试事实完整性验证机制"""
        print("\n" + "="*60)
        print("🧠 测试2: 事实完整性验证机制")
        print("="*60)
        
        try:
            agent = FactExtractionAgent()
            
            # 使用复杂案例测试完整性验证
            complex_case = self.test_cases[0]  # 选择最复杂的案例
            
            print(f"📋 测试案例: {complex_case['id']}")
            print(f"📄 文本: {complex_case['text'][:100]}...")
            
            # 执行抽取
            results = agent.extract(complex_case['text'], ExtractionTaskType.FACT_EXTRACTION)
            
            # 分析完整性
            fact_categories = {}
            for result in results:
                category = result.metadata.get("fact_category", "unknown")
                fact_categories[category] = fact_categories.get(category, 0) + 1
            
            print(f"📊 抽取的事实类别: {list(fact_categories.keys())}")
            
            # 检查是否包含期望的事实类别
            expected_categories = ["basic_fact", "mitigating_factor", "aggravating_factor", "damage"]
            found_categories = list(fact_categories.keys())
            
            coverage = len(set(expected_categories) & set(found_categories)) / len(expected_categories)
            print(f"📊 事实类别覆盖率: {coverage:.2f}")
            
            # 检查完整性分数
            stats = agent.get_performance_stats()
            completeness_score = stats.get('average_completeness_score', 0)
            print(f"📊 完整性分数: {completeness_score:.2f}")
            
            if completeness_score >= 0.7:
                print("✅ 事实完整性验证通过")
            else:
                print("⚠️ 事实完整性需要改进")
            
            return True
            
        except Exception as e:
            print(f"❌ 事实完整性验证测试失败: {e}")
            return False
    
    def test_integration_with_coordinator(self):
        """测试与协调器的集成"""
        print("\n" + "="*60)
        print("🏗️ 测试3: 与协调器集成验证")
        print("="*60)
        
        try:
            # 测试与协调器的集成
            from judicial_ie_coordinator import JudicialIECoordinator
            
            coordinator = JudicialIECoordinator()
            print("✅ 协调器初始化成功")
            
            # 检查事实抽取智能体是否正确加载
            if coordinator.agents.get("fact") is not None:
                print("✅ 事实抽取智能体已正确集成到协调器")
                
                # 测试通过协调器进行事实抽取
                test_text = self.test_cases[0]["text"]
                result = coordinator.extract_information(test_text, ExtractionTaskType.FACT_EXTRACTION)
                
                if result.get('status') == 'success':
                    results = result.get('results', {})
                    detailed_results = results.get('detailed_results', [])
                    
                    # 统计事实抽取结果
                    fact_results = [r for r in detailed_results if r.get('info_type') == 'case_facts']
                    print(f"✅ 通过协调器抽取到 {len(fact_results)} 个事实")
                    
                    # 检查系统性能指标
                    metrics = result.get('metrics', {})
                    performance = metrics.get('performance_metrics', {})
                    print(f"✅ 系统性能指标正常: {len(performance)} 个指标")
                    
                    return True
                else:
                    print(f"❌ 协调器抽取失败: {result.get('error', 'unknown error')}")
                    return False
            else:
                print("❌ 事实抽取智能体未正确集成到协调器")
                return False
            
        except Exception as e:
            print(f"❌ 协调器集成测试失败: {e}")
            return False
    
    def test_llm_api_consistency(self):
        """测试LLM API调用一致性"""
        print("\n" + "="*60)
        print("🔧 测试4: LLM API调用一致性验证")
        print("="*60)
        
        try:
            agent = FactExtractionAgent()
            
            # 检查是否正确导入get_completion函数
            from extraction_agents.fact_extractor import get_completion
            print("✅ 成功导入get_completion函数，确认API依赖")
            
            # 检查智能体类型和方法
            print(f"✅ 智能体ID: {agent.agent_id}")
            print(f"✅ 智能体类型: {type(agent).__name__}")
            
            # 执行一次抽取，验证API调用
            test_text = self.test_cases[2]["text"]  # 使用简单案例
            results = agent.extract(test_text, ExtractionTaskType.FACT_EXTRACTION)
            
            # 检查API调用统计
            stats = agent.get_performance_stats()
            api_calls = stats.get('api_calls', 0)
            
            if api_calls > 0:
                print(f"✅ API调用正常，共调用 {api_calls} 次")
                print(f"✅ 抽取方法: {stats.get('extraction_method', 'unknown')}")
                print(f"✅ 智能体类型: {stats.get('agent_type', 'unknown')}")
                return True
            else:
                print("❌ 未检测到API调用")
                return False
            
        except Exception as e:
            print(f"❌ LLM API一致性测试失败: {e}")
            return False
    
    def run_comprehensive_test(self):
        """运行综合测试"""
        print("🚀 开始事实抽取智能体综合测试")
        print("="*80)
        
        test_results = []
        
        # 执行所有测试
        tests = [
            ("基本事实抽取功能", self.test_basic_fact_extraction),
            ("事实完整性验证", self.test_fact_completeness_validation),
            ("协调器集成", self.test_integration_with_coordinator),
            ("LLM API一致性", self.test_llm_api_consistency)
        ]
        
        for test_name, test_func in tests:
            try:
                result = test_func()
                test_results.append((test_name, result))
            except Exception as e:
                print(f"❌ {test_name}测试异常: {e}")
                test_results.append((test_name, False))
        
        # 输出测试总结
        print("\n" + "="*80)
        print("📊 测试总结")
        print("="*80)
        
        passed_tests = sum(1 for _, result in test_results if result)
        total_tests = len(test_results)
        
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{status} {test_name}")
        
        print(f"\n🎯 总体结果: {passed_tests}/{total_tests} 测试通过")
        
        if passed_tests == total_tests:
            print("🎉 所有测试通过！FactExtractionAgent已成功实现为基于LLM的智能体")
            print("✅ 符合多智能体系统架构要求")
            print("✅ 具备专业的事实抽取能力")
            print("✅ 与现有系统完美集成")
        else:
            print("⚠️ 部分测试未通过，需要进一步优化")
        
        return passed_tests == total_tests

def main():
    """主函数"""
    tester = FactExtractorTester()
    success = tester.run_comprehensive_test()
    
    if success:
        print("\n🎊 FactExtractionAgent开发成功！")
        print("现在我们有了两个专业的LLM驱动智能体：")
        print("  1. EntityExtractionAgent - 实体抽取专家")
        print("  2. FactExtractionAgent - 事实抽取专家")
        print("多智能体司法信息抽取系统正在按计划稳步发展！")
    else:
        print("\n🔧 需要进一步调试和优化。")

if __name__ == "__main__":
    main()
