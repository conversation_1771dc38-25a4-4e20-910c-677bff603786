"""
实体抽取智能体 (EntityExtractionAgent)

基于LLM的智能实体抽取专家，专门负责从司法文本中智能抽取基础实体信息，包括：
- 人物信息（被告人、被害人、证人、法官、律师等）
- 时间信息（犯罪时间、审理时间等）
- 地点信息（犯罪地点、管辖地等）
- 机构信息（法院、检察院、公安机关等）

核心特色：
1. 基于GPT-4的智能语义理解和实体识别
2. 继承统一的智能体基类架构
3. 专业化的司法领域知识和推理能力
4. 智能化的上下文理解和歧义消解
5. 与证据权重分析系统的深度集成
"""

import logging
import json
import re
from typing import Dict, List

# 导入基类和相关类型
from .base_agent import BaseExtractionAgent
from extraction_types import ExtractionTaskType, InformationType, ExtractionResult

# 配置日志
logger = logging.getLogger(__name__)


class EntityExtractionAgent(BaseExtractionAgent):
    """基于LLM的实体抽取智能体 - 专门负责基础实体信息的智能抽取"""

    def __init__(self, legal_knowledge_base: Dict = None):
        super().__init__("llm_entity_extractor", legal_knowledge_base)
        
        # 专业化领域：基础实体信息
        self.specialization = [
            InformationType.DEFENDANT,
            InformationType.VICTIM,
            InformationType.WITNESS,
            InformationType.JUDGE,
            InformationType.LAWYER,
            InformationType.CRIME_TIME,
            InformationType.TRIAL_TIME,
            InformationType.CRIME_LOCATION
        ]
        
        logger.info(f"基于LLM的实体抽取智能体 {self.agent_id} 初始化完成")

    def extract(self, text: str, task_type: ExtractionTaskType) -> List[ExtractionResult]:
        """
        执行基于LLM的智能实体抽取

        Args:
            text: 待抽取的司法文本
            task_type: 抽取任务类型

        Returns:
            实体抽取结果列表
        """
        logger.info(f"开始基于LLM的实体抽取，文本长度: {len(text)} 字符")

        try:
            # 构建LLM抽取提示
            prompt = self._build_extraction_prompt(text, task_type)

            # 调用LLM进行实体抽取
            logger.info("调用LLM进行实体抽取...")
            response = self._call_llm(prompt, "judicial information extraction expert")

            # 解析LLM响应
            parsed_results = self._parse_llm_response(response, text)

            # 转换为ExtractionResult对象
            extraction_results = self._convert_to_extraction_results(parsed_results, text)

            # 后处理：质量评估和过滤
            filtered_results = self._post_process_results(extraction_results)

            # 更新性能统计
            self._update_performance_stats(filtered_results)

            logger.info(f"基于LLM的实体抽取完成，共抽取 {len(filtered_results)} 个实体")
            return filtered_results

        except Exception as e:
            logger.error(f"基于LLM的实体抽取过程出错: {e}")
            return []

    def _build_extraction_prompt(self, text: str, task_type: ExtractionTaskType) -> str:
        """构建LLM实体抽取提示"""
        
        # 根据任务类型确定目标实体类型
        target_types = []
        target_descriptions = []
        
        if task_type in [ExtractionTaskType.ENTITY_EXTRACTION, ExtractionTaskType.COMPREHENSIVE_EXTRACTION]:
            target_types = self.specialization
            target_descriptions = [
                "- 被告人：案件中的被指控人员",
                "- 被害人：案件中的受害人员", 
                "- 证人：案件中的证人",
                "- 法官：审理案件的法官",
                "- 律师：参与案件的律师",
                "- 犯罪时间：犯罪行为发生的时间",
                "- 审理时间：案件审理的时间",
                "- 犯罪地点：犯罪行为发生的地点"
            ]

        prompt = f"""作为一名专业的司法信息抽取专家，请从以下司法文本中准确抽取指定的实体信息。

【司法文本】
{text}

【抽取任务】
请从上述文本中抽取以下类型的实体信息：
{chr(10).join(target_descriptions)}

【抽取要求】
1. 准确性：确保抽取的实体信息准确无误，不要添加文本中不存在的信息
2. 完整性：尽可能抽取所有相关的实体信息，不要遗漏
3. 规范性：抽取的实体名称应当规范，去除无关的修饰词
4. 上下文理解：充分理解上下文，正确识别实体的角色和关系

【输出格式】
请严格按照以下JSON格式输出抽取结果：
{{
    "defendants": ["被告人姓名1", "被告人姓名2"],
    "victims": ["被害人姓名1", "被害人姓名2"],
    "witnesses": ["证人姓名1", "证人姓名2"],
    "judges": ["法官姓名1"],
    "lawyers": ["律师姓名1"],
    "crime_times": ["犯罪时间1", "犯罪时间2"],
    "trial_times": ["审理时间1"],
    "crime_locations": ["犯罪地点1", "犯罪地点2"]
}}

【注意事项】
1. 如果某类实体在文本中不存在，请返回空数组[]
2. 人名应当去除"某"字，如"张某"应抽取为"张某"（保持原文）
3. 时间信息应当保持原文格式，如"2023年3月15日晚上8时许"
4. 地点信息应当包含完整的地址描述
5. 只返回JSON格式的结果，不要添加其他说明文字

请开始抽取："""

        return prompt

    def _convert_to_extraction_results(self, parsed_data: Dict, original_text: str) -> List[ExtractionResult]:
        """将解析后的数据转换为ExtractionResult对象"""
        results = []
        
        # 实体类型映射
        entity_mapping = {
            "defendants": InformationType.DEFENDANT,
            "victims": InformationType.VICTIM,
            "witnesses": InformationType.WITNESS,
            "judges": InformationType.JUDGE,
            "lawyers": InformationType.LAWYER,
            "crime_times": InformationType.CRIME_TIME,
            "trial_times": InformationType.TRIAL_TIME,
            "crime_locations": InformationType.CRIME_LOCATION
        }

        for key, entity_type in entity_mapping.items():
            entities = parsed_data.get(key, [])
            for entity in entities:
                if entity and entity.strip():
                    # 计算置信度和证据权重
                    confidence = self._calculate_confidence(entity, entity_type, original_text)
                    evidence_weight = self._calculate_evidence_weight(entity, entity_type)

                    # 查找实体在原文中的位置
                    source_span = self._find_source_span(entity, original_text)
                    context = self._extract_context(entity, original_text)

                    result = ExtractionResult(
                        info_type=entity_type,
                        content=entity.strip(),
                        confidence=confidence,
                        evidence_weight=evidence_weight,
                        source_span=source_span,
                        context=context,
                        agent_id=self.agent_id,
                        metadata={
                            "extraction_method": "llm_based",
                            "entity_category": key
                        }
                    )
                    results.append(result)

        return results

    def _post_process_results(self, results: List[ExtractionResult]) -> List[ExtractionResult]:
        """后处理：质量评估和过滤"""
        filtered_results = []
        
        for result in results:
            # 基本质量检查
            if len(result.content) < 1:
                continue
                
            # 置信度阈值过滤
            if result.confidence < 0.3:
                continue
                
            # 内容合理性检查
            if self._is_reasonable_entity(result.content, result.info_type):
                filtered_results.append(result)
        
        return filtered_results

    def _is_reasonable_entity(self, content: str, info_type: InformationType) -> bool:
        """检查实体内容的合理性"""
        # 基本长度检查
        if len(content) > 100:  # 实体内容不应过长
            return False
            
        # 根据实体类型进行特定检查
        if info_type in [InformationType.DEFENDANT, InformationType.VICTIM, 
                        InformationType.WITNESS, InformationType.JUDGE, InformationType.LAWYER]:
            # 人名检查：不应包含明显的非人名内容
            invalid_patterns = ['案件', '法院', '判决', '刑法', '条款']
            if any(pattern in content for pattern in invalid_patterns):
                return False
                
        return True
