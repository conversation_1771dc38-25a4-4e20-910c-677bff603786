"""
消息总线系统 (MessageBus)

多智能体协作的核心通信基础设施，提供：
- 异步消息处理机制
- 优先级队列管理
- 智能体注册与管理
- 协作会话支持
- 消息历史追踪

学术价值：
- 标准化的智能体通信协议
- 支持复杂的多智能体协作模式
- 为协作推理提供基础设施支撑
"""

import json
import logging
import time
import threading
import queue
import concurrent.futures
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict
import uuid

# 配置日志
logger = logging.getLogger(__name__)


class MessageType(Enum):
    """消息类型枚举"""
    KNOWLEDGE_SHARE = "knowledge_share"
    COLLABORATION_REQUEST = "collaboration_request"
    EVIDENCE_UPDATE = "evidence_update"
    CONSENSUS_PROPOSAL = "consensus_proposal"
    CONFLICT_NOTIFICATION = "conflict_notification"
    TASK_COORDINATION = "task_coordination"


@dataclass
class AgentMessage:
    """智能体间消息"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    sender_id: str = ""
    receiver_id: str = ""  # 空字符串表示广播
    message_type: MessageType = MessageType.KNOWLEDGE_SHARE
    content: Dict[str, Any] = field(default_factory=dict)
    timestamp: float = field(default_factory=time.time)
    priority: int = 1  # 1-5，5为最高优先级
    requires_response: bool = False
    correlation_id: Optional[str] = None

    def __lt__(self, other):
        """为优先级队列提供比较方法"""
        if not isinstance(other, AgentMessage):
            return NotImplemented
        # 优先级高的消息排在前面（数值越大优先级越高）
        if self.priority != other.priority:
            return self.priority > other.priority
        # 优先级相同时，按时间戳排序（早的在前）
        return self.timestamp < other.timestamp


class MessageBus:
    """智能体消息总线系统 - 真正的异步实现"""

    def __init__(self, max_workers: int = 4):
        self.subscribers: Dict[str, List[Callable]] = defaultdict(list)
        self.message_queue: queue.PriorityQueue = queue.PriorityQueue()
        self.message_history: List[AgentMessage] = []
        self.active_agents: Dict[str, Dict] = {}
        self.collaboration_sessions: Dict[str, Dict] = {}
        self._lock = threading.Lock()

        # 异步处理组件
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=max_workers)
        self.processing_thread = None
        self.is_running = False
        self.processed_count = 0
        self.failed_count = 0

        # 启动异步消息处理线程
        self._start_async_processing()

        logger.info(f"异步消息总线系统初始化完成，工作线程数: {max_workers}")

    def register_agent(self, agent_id: str, agent_info: Dict):
        """注册智能体"""
        with self._lock:
            self.active_agents[agent_id] = {
                "info": agent_info,
                "last_active": time.time(),
                "message_count": 0,
                "collaboration_count": 0
            }
        logger.info(f"智能体 {agent_id} 已注册到消息总线")

    def subscribe(self, agent_id: str, message_type: MessageType, callback: Callable):
        """订阅消息类型"""
        subscription_key = f"{agent_id}:{message_type.value}"
        self.subscribers[subscription_key].append(callback)
        logger.debug(f"智能体 {agent_id} 订阅了消息类型 {message_type.value}")

    def _start_async_processing(self):
        """启动异步消息处理线程"""
        self.is_running = True
        self.processing_thread = threading.Thread(target=self._async_message_processor, daemon=True)
        self.processing_thread.start()
        logger.info("异步消息处理线程已启动")

    def _async_message_processor(self):
        """异步消息处理器 - 在独立线程中运行"""
        while self.is_running:
            try:
                # 从优先级队列获取消息（阻塞等待）
                message = self.message_queue.get(timeout=1.0)

                # 提交到线程池异步处理
                future = self.executor.submit(self._process_message_async, message)

                # 可选：等待处理完成或设置超时
                try:
                    future.result(timeout=5.0)  # 5秒超时
                    self.processed_count += 1
                except concurrent.futures.TimeoutError:
                    logger.warning(f"消息处理超时: {message.id}")
                    self.failed_count += 1
                except Exception as e:
                    logger.error(f"消息处理失败: {e}")
                    self.failed_count += 1

                # 标记任务完成
                self.message_queue.task_done()

            except queue.Empty:
                # 超时，继续循环
                continue
            except Exception as e:
                logger.error(f"异步消息处理器错误: {e}")

    def publish(self, message: AgentMessage) -> bool:
        """发布消息 - 异步处理"""
        try:
            with self._lock:
                # 添加到消息历史
                self.message_history.append(message)

                # 更新发送者活跃状态
                if message.sender_id in self.active_agents:
                    self.active_agents[message.sender_id]["last_active"] = time.time()
                    self.active_agents[message.sender_id]["message_count"] += 1

            # 添加到优先级队列进行异步处理
            # 直接使用消息对象，利用其__lt__方法进行排序
            self.message_queue.put(message)

            logger.debug(f"消息已加入异步处理队列: {message.id}, 优先级: {message.priority}")
            return True

        except Exception as e:
            logger.error(f"消息发布失败: {e}")
            return False

    def _process_message_async(self, message: AgentMessage):
        """异步处理消息 - 在线程池中执行"""
        try:
            # 确定接收者
            with self._lock:
                if message.receiver_id:
                    # 点对点消息
                    receivers = [message.receiver_id] if message.receiver_id in self.active_agents else []
                else:
                    # 广播消息
                    receivers = list(self.active_agents.keys())
                    if message.sender_id in receivers:
                        receivers.remove(message.sender_id)  # 不发送给自己

            # 分发消息
            for receiver_id in receivers:
                subscription_key = f"{receiver_id}:{message.message_type.value}"
                callbacks = self.subscribers.get(subscription_key, [])

                for callback in callbacks:
                    try:
                        # 在线程池中执行回调
                        callback(message)
                        logger.debug(f"消息已投递给 {receiver_id}: {message.id}")
                    except Exception as e:
                        logger.error(f"消息处理回调失败 {receiver_id}: {e}")

            logger.debug(f"消息处理完成: {message.id}")

        except Exception as e:
            logger.error(f"异步消息处理失败: {e}")
            raise

    def _process_message(self, message: AgentMessage):
        """同步处理消息 - 保持向后兼容"""
        # 为了保持测试兼容性，保留同步版本
        self._process_message_async(message)

    def shutdown(self):
        """关闭消息总线"""
        logger.info("正在关闭异步消息总线...")
        self.is_running = False

        # 等待队列中的消息处理完成
        try:
            self.message_queue.join()  # 等待所有任务完成
        except:
            pass

        # 关闭线程池
        self.executor.shutdown(wait=True)

        # 等待处理线程结束
        if self.processing_thread and self.processing_thread.is_alive():
            self.processing_thread.join(timeout=2.0)

        logger.info(f"消息总线已关闭，处理统计: 成功 {self.processed_count}, 失败 {self.failed_count}")

    def get_processing_stats(self) -> Dict:
        """获取处理统计信息"""
        return {
            "processed_count": self.processed_count,
            "failed_count": self.failed_count,
            "queue_size": self.message_queue.qsize(),
            "active_agents": len(self.active_agents),
            "is_running": self.is_running
        }

    def get_collaboration_history(self, agent_id: str) -> List[AgentMessage]:
        """获取智能体的协作历史"""
        return [msg for msg in self.message_history
                if msg.sender_id == agent_id or msg.receiver_id == agent_id]

    def create_collaboration_session(self, session_id: str, participants: List[str],
                                   topic: str) -> Dict:
        """创建协作会话"""
        session = {
            "id": session_id,
            "participants": participants,
            "topic": topic,
            "created_at": time.time(),
            "messages": [],
            "status": "active"
        }
        self.collaboration_sessions[session_id] = session

        # 通知参与者
        for participant in participants:
            notification = AgentMessage(
                sender_id="system",
                receiver_id=participant,
                message_type=MessageType.TASK_COORDINATION,
                content={
                    "action": "collaboration_session_created",
                    "session_id": session_id,
                    "topic": topic,
                    "participants": participants
                }
            )
            self.publish(notification)

        logger.info(f"创建协作会话 {session_id}，参与者: {participants}")
        return session

    def get_session_stats(self) -> Dict:
        """获取会话统计信息"""
        active_sessions = sum(1 for s in self.collaboration_sessions.values() 
                            if s["status"] == "active")
        
        return {
            "total_sessions": len(self.collaboration_sessions),
            "active_sessions": active_sessions,
            "total_messages": len(self.message_history),
            "active_agents": len(self.active_agents)
        }
