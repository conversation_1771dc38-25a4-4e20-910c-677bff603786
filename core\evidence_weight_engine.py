"""
证据权重引导引擎 (Evidence Weight Guidance Engine)

核心学术创新：多维度证据权重计算与引导机制

实现五个维度的量化评估：
1. 证据类型权重 (0.25) - 基于证据类型的基础权重
2. 可靠性评分 (0.25) - 证据来源和质量的可靠性
3. 完整性指标 (0.20) - 证据信息的完整程度
4. 相互印证度 (0.20) - 证据间的相互支撑关系
5. 相关性分析 (0.10) - 与案件核心事实的相关程度

学术价值：
- 首次提出多维度证据权重量化评估模型
- 创新的权重引导策略调整机制
- 动态权重学习和自适应优化算法
- 为司法AI系统提供理论基础和技术支撑
"""

import logging
import time
import math
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from enum import Enum

# 配置日志
logger = logging.getLogger(__name__)


class EvidenceType(Enum):
    """证据类型枚举"""
    PHYSICAL = "物证"           # 物理证据
    DOCUMENTARY = "书证"       # 文书证据
    TESTIMONIAL = "言词证据"   # 证人证言
    EXPERT = "鉴定意见"        # 专家鉴定
    INSPECTION = "勘验笔录"    # 勘验检查
    AUDIO_VIDEO = "视听资料"   # 音视频资料
    ELECTRONIC = "电子数据"    # 电子证据
    OTHER = "其他证据"         # 其他类型


@dataclass
class EvidenceItem:
    """证据项数据结构"""
    id: str
    content: str
    evidence_type: EvidenceType
    source: Optional[str] = None
    timestamp: Optional[float] = None

    # 五维度评分
    type_weight: float = 0.0      # 证据类型权重
    reliability_score: float = 0.0 # 可靠性评分
    completeness_score: float = 0.0 # 完整性指标
    corroboration_score: float = 0.0 # 相互印证度
    relevance_score: float = 0.0   # 相关性分析

    # 综合权重
    final_weight: float = 0.0
    confidence: float = 0.0

    # 元数据
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
        if self.timestamp is None:
            self.timestamp = time.time()


class EvidenceWeightEngine:
    """证据权重引导引擎"""

    # 五维度权重配置
    DIMENSION_WEIGHTS = {
        'type_weight': 0.25,        # 证据类型权重
        'reliability_score': 0.25,  # 可靠性评分
        'completeness_score': 0.20, # 完整性指标
        'corroboration_score': 0.20, # 相互印证度
        'relevance_score': 0.10     # 相关性分析
    }

    # 证据类型基础权重
    TYPE_BASE_WEIGHTS = {
        EvidenceType.PHYSICAL: 0.90,      # 物证权重最高
        EvidenceType.EXPERT: 0.85,        # 专家鉴定次之
        EvidenceType.DOCUMENTARY: 0.80,   # 书证较高
        EvidenceType.AUDIO_VIDEO: 0.75,   # 音视频资料
        EvidenceType.ELECTRONIC: 0.70,    # 电子数据
        EvidenceType.INSPECTION: 0.65,    # 勘验笔录
        EvidenceType.TESTIMONIAL: 0.60,   # 言词证据较低
        EvidenceType.OTHER: 0.50          # 其他证据最低
    }

    def __init__(self, get_completion_func):
        """
        初始化证据权重引导引擎

        Args:
            get_completion_func: LLM调用函数
        """
        self.get_completion = get_completion_func
        self.evidence_items: List[EvidenceItem] = []
        self.weight_history: List[Dict] = []
        self.learning_rate = 0.1
        self.convergence_threshold = 0.01

        logger.info("证据权重引导引擎初始化完成")

    def calculate_evidence_weights(self, evidence_items: List[EvidenceItem],
                                 case_context: str) -> List[EvidenceItem]:
        """
        计算证据权重（五维度量化评估）

        Args:
            evidence_items: 证据项列表
            case_context: 案件上下文

        Returns:
            更新权重后的证据项列表
        """
        logger.info(f"开始计算 {len(evidence_items)} 个证据的权重")

        # 1. 计算证据类型权重
        self._calculate_type_weights(evidence_items)

        # 2. 计算可靠性评分
        self._calculate_reliability_scores(evidence_items, case_context)

        # 3. 计算完整性指标
        self._calculate_completeness_scores(evidence_items)

        # 4. 计算相互印证度
        self._calculate_corroboration_scores(evidence_items)

        # 5. 计算相关性分析
        self._calculate_relevance_scores(evidence_items, case_context)

        # 6. 综合计算最终权重
        self._calculate_final_weights(evidence_items)

        # 7. 记录权重历史
        self._record_weight_history(evidence_items)

        logger.info("证据权重计算完成")
        return evidence_items

    def _calculate_type_weights(self, evidence_items: List[EvidenceItem]):
        """计算证据类型权重 (维度1: 0.25)"""
        logger.debug("计算证据类型权重...")

        for item in evidence_items:
            # 基础类型权重
            base_weight = self.TYPE_BASE_WEIGHTS.get(item.evidence_type, 0.50)

            # 根据证据内容质量调整
            content_quality = self._assess_content_quality(item.content)

            # 最终类型权重 = 基础权重 * 内容质量调整
            item.type_weight = base_weight * (0.8 + 0.2 * content_quality)

            # 确保权重在[0,1]范围内
            item.type_weight = max(0.0, min(1.0, item.type_weight))

            logger.debug(f"证据 {item.id} 类型权重: {item.type_weight:.3f}")

    def _calculate_reliability_scores(self, evidence_items: List[EvidenceItem],
                                    case_context: str):
        """计算可靠性评分 (维度2: 0.25)"""
        logger.debug("计算可靠性评分...")

        for item in evidence_items:
            # 来源可靠性评估
            source_reliability = self._assess_source_reliability(item.source)

            # 内容一致性评估
            content_consistency = self._assess_content_consistency(item.content, case_context)

            # 时间逻辑性评估
            temporal_logic = self._assess_temporal_logic(item)

            # 综合可靠性评分
            item.reliability_score = (
                0.4 * source_reliability +
                0.4 * content_consistency +
                0.2 * temporal_logic
            )

            logger.debug(f"证据 {item.id} 可靠性评分: {item.reliability_score:.3f}")

    def _calculate_completeness_scores(self, evidence_items: List[EvidenceItem]):
        """计算完整性指标 (维度3: 0.20)"""
        logger.debug("计算完整性指标...")

        for item in evidence_items:
            # 信息完整度评估
            info_completeness = self._assess_information_completeness(item.content)

            # 关键要素覆盖度
            key_elements_coverage = self._assess_key_elements_coverage(item.content)

            # 描述详细程度
            description_detail = self._assess_description_detail(item.content)

            # 综合完整性指标
            item.completeness_score = (
                0.4 * info_completeness +
                0.4 * key_elements_coverage +
                0.2 * description_detail
            )

            logger.debug(f"证据 {item.id} 完整性指标: {item.completeness_score:.3f}")

    def _calculate_corroboration_scores(self, evidence_items: List[EvidenceItem]):
        """计算相互印证度 (维度4: 0.20)"""
        logger.debug("计算相互印证度...")

        n = len(evidence_items)
        if n < 2:
            # 只有一个证据时，印证度为0
            for item in evidence_items:
                item.corroboration_score = 0.0
            return

        # 计算证据间的相似度矩阵
        similarity_matrix = self._calculate_similarity_matrix(evidence_items)

        for i, item in enumerate(evidence_items):
            # 计算与其他证据的平均相似度
            similarities = []
            for j in range(n):
                if i != j:
                    similarities.append(similarity_matrix[i][j])

            if similarities:
                # 相互印证度 = 平均相似度 * 印证证据数量权重
                avg_similarity = sum(similarities) / len(similarities)
                quantity_weight = min(1.0, len(similarities) / 5.0)  # 最多5个证据达到满分

                item.corroboration_score = avg_similarity * (0.7 + 0.3 * quantity_weight)
            else:
                item.corroboration_score = 0.0

            logger.debug(f"证据 {item.id} 相互印证度: {item.corroboration_score:.3f}")

    def _calculate_relevance_scores(self, evidence_items: List[EvidenceItem],
                                  case_context: str):
        """计算相关性分析 (维度5: 0.10)"""
        logger.debug("计算相关性分析...")

        # 提取案件关键词
        case_keywords = self._extract_case_keywords(case_context)

        for item in evidence_items:
            # 关键词匹配度
            keyword_match = self._calculate_keyword_match(item.content, case_keywords)

            # 主题相关性
            topic_relevance = self._assess_topic_relevance(item.content, case_context)

            # 事实关联度
            fact_correlation = self._assess_fact_correlation(item.content, case_context)

            # 综合相关性分析
            item.relevance_score = (
                0.3 * keyword_match +
                0.4 * topic_relevance +
                0.3 * fact_correlation
            )

            logger.debug(f"证据 {item.id} 相关性分析: {item.relevance_score:.3f}")

    def _calculate_final_weights(self, evidence_items: List[EvidenceItem]):
        """综合计算最终权重"""
        logger.debug("计算最终权重...")

        for item in evidence_items:
            # 加权求和
            item.final_weight = (
                self.DIMENSION_WEIGHTS['type_weight'] * item.type_weight +
                self.DIMENSION_WEIGHTS['reliability_score'] * item.reliability_score +
                self.DIMENSION_WEIGHTS['completeness_score'] * item.completeness_score +
                self.DIMENSION_WEIGHTS['corroboration_score'] * item.corroboration_score +
                self.DIMENSION_WEIGHTS['relevance_score'] * item.relevance_score
            )

            # 计算置信度（基于各维度的方差）
            scores = [
                item.type_weight, item.reliability_score, item.completeness_score,
                item.corroboration_score, item.relevance_score
            ]
            variance = np.var(scores)
            item.confidence = max(0.0, min(1.0, 1.0 - variance))

            logger.debug(f"证据 {item.id} 最终权重: {item.final_weight:.3f}, 置信度: {item.confidence:.3f}")

    def _assess_content_quality(self, content: str) -> float:
        """评估内容质量"""
        if not content:
            return 0.0

        # 基于内容长度、结构化程度等评估
        length_score = min(1.0, len(content) / 200.0)
        structure_score = 0.5  # 简化评估

        return 0.6 * length_score + 0.4 * structure_score

    def _assess_source_reliability(self, source: Optional[str]) -> float:
        """评估来源可靠性"""
        if not source:
            return 0.5  # 无来源信息时的默认值

        # 基于来源类型评估可靠性
        high_reliability_sources = ["法院", "公安", "检察院", "鉴定机构", "专业机构"]
        medium_reliability_sources = ["证人", "当事人", "相关人员"]

        source_lower = source.lower()

        for high_source in high_reliability_sources:
            if high_source in source_lower:
                return 0.9

        for medium_source in medium_reliability_sources:
            if medium_source in source_lower:
                return 0.6

        return 0.5  # 默认中等可靠性

    def _assess_content_consistency(self, content: str, case_context: str) -> float:
        """评估内容一致性"""
        if not content or not case_context:
            return 0.5

        # 简化的一致性评估：检查关键词重叠
        content_words = set(content.lower().split())
        context_words = set(case_context.lower().split())

        if not content_words or not context_words:
            return 0.5

        overlap = len(content_words.intersection(context_words))
        union = len(content_words.union(context_words))

        return overlap / union if union > 0 else 0.0

    def _assess_temporal_logic(self, item: EvidenceItem) -> float:
        """评估时间逻辑性"""
        # 简化评估：基于时间戳的合理性
        if item.timestamp:
            current_time = time.time()
            time_diff = abs(current_time - item.timestamp)

            # 时间越近，逻辑性越高（在合理范围内）
            if time_diff < 86400:  # 1天内
                return 1.0
            elif time_diff < 604800:  # 1周内
                return 0.8
            elif time_diff < 2592000:  # 1月内
                return 0.6
            else:
                return 0.4

        return 0.7  # 无时间戳时的默认值

    def _assess_information_completeness(self, content: str) -> float:
        """评估信息完整度"""
        if not content:
            return 0.0

        # 检查关键信息要素
        key_elements = ["时间", "地点", "人物", "事件", "原因", "结果"]
        element_keywords = {
            "时间": ["年", "月", "日", "时", "分", "秒", "上午", "下午", "晚上"],
            "地点": ["在", "于", "处", "地", "室", "楼", "街", "路", "区"],
            "人物": ["人", "者", "员", "师", "生", "某", "张", "李", "王"],
            "事件": ["发生", "进行", "实施", "执行", "完成", "开始", "结束"],
            "原因": ["因为", "由于", "因", "缘于", "起因", "导致"],
            "结果": ["结果", "后果", "影响", "造成", "导致", "产生"]
        }

        present_elements = 0
        for element, keywords in element_keywords.items():
            if any(keyword in content for keyword in keywords):
                present_elements += 1

        return present_elements / len(key_elements)

    def _assess_key_elements_coverage(self, content: str) -> float:
        """评估关键要素覆盖度"""
        if not content:
            return 0.0

        # 司法证据的关键要素
        judicial_elements = ["当事人", "时间", "地点", "行为", "后果", "证据来源"]
        coverage_score = 0.0

        # 简化的关键词匹配
        for element in judicial_elements:
            if element in content:
                coverage_score += 1.0

        return coverage_score / len(judicial_elements)

    def _assess_description_detail(self, content: str) -> float:
        """评估描述详细程度"""
        if not content:
            return 0.0

        # 基于内容长度和描述性词汇评估
        length_factor = min(1.0, len(content) / 300.0)

        # 描述性词汇
        descriptive_words = ["详细", "具体", "明确", "清楚", "准确", "完整"]
        descriptive_count = sum(1 for word in descriptive_words if word in content)
        descriptive_factor = min(1.0, descriptive_count / 3.0)

        return 0.7 * length_factor + 0.3 * descriptive_factor

    def _calculate_similarity_matrix(self, evidence_items: List[EvidenceItem]) -> List[List[float]]:
        """计算证据间相似度矩阵"""
        n = len(evidence_items)
        matrix = [[0.0 for _ in range(n)] for _ in range(n)]

        for i in range(n):
            for j in range(n):
                if i == j:
                    matrix[i][j] = 1.0
                else:
                    # 计算内容相似度
                    similarity = self._calculate_content_similarity(
                        evidence_items[i].content,
                        evidence_items[j].content
                    )
                    matrix[i][j] = similarity

        return matrix

    def _calculate_content_similarity(self, content1: str, content2: str) -> float:
        """计算内容相似度"""
        if not content1 or not content2:
            return 0.0

        # 简化的Jaccard相似度
        words1 = set(content1.lower().split())
        words2 = set(content2.lower().split())

        if not words1 or not words2:
            return 0.0

        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))

        return intersection / union if union > 0 else 0.0

    def _extract_case_keywords(self, case_context: str) -> List[str]:
        """提取案件关键词"""
        if not case_context:
            return []

        # 简化的关键词提取
        import re

        # 法律相关关键词
        legal_keywords = re.findall(r'[犯罪|违法|侵害|损害|盗窃|抢劫|故意|过失|合同|协议|纠纷]+', case_context)

        # 实体关键词
        entity_keywords = re.findall(r'[张|李|王|刘|陈|杨|赵|黄|周|吴]\w*', case_context)

        # 时间地点关键词
        temporal_keywords = re.findall(r'\d{4}年|\d{1,2}月|\d{1,2}日', case_context)
        location_keywords = re.findall(r'\w+[市|区|县|街|路|号]', case_context)

        all_keywords = legal_keywords + entity_keywords + temporal_keywords + location_keywords
        return list(set(all_keywords))  # 去重

    def _calculate_keyword_match(self, content: str, keywords: List[str]) -> float:
        """计算关键词匹配度"""
        if not content or not keywords:
            return 0.0

        matched_count = sum(1 for keyword in keywords if keyword in content)
        return matched_count / len(keywords)

    def _assess_topic_relevance(self, content: str, case_context: str) -> float:
        """评估主题相关性"""
        # 使用内容相似度作为主题相关性的代理指标
        return self._calculate_content_similarity(content, case_context)

    def _assess_fact_correlation(self, content: str, case_context: str) -> float:
        """评估事实关联度"""
        if not content or not case_context:
            return 0.0

        # 检查事实性描述的重叠
        fact_indicators = ["发生", "进行", "实施", "造成", "导致", "产生", "完成"]

        content_facts = sum(1 for indicator in fact_indicators if indicator in content)
        context_facts = sum(1 for indicator in fact_indicators if indicator in case_context)

        if content_facts == 0 or context_facts == 0:
            return 0.0

        # 基于事实描述密度计算关联度
        content_density = content_facts / len(content.split())
        context_density = context_facts / len(case_context.split())

        # 密度相似度
        density_similarity = 1.0 - abs(content_density - context_density) / max(content_density, context_density)

        return max(0.0, min(1.0, density_similarity))

    def _record_weight_history(self, evidence_items: List[EvidenceItem]):
        """记录权重历史"""
        history_entry = {
            "timestamp": time.time(),
            "evidence_count": len(evidence_items),
            "weights": {
                item.id: {
                    "type_weight": item.type_weight,
                    "reliability_score": item.reliability_score,
                    "completeness_score": item.completeness_score,
                    "corroboration_score": item.corroboration_score,
                    "relevance_score": item.relevance_score,
                    "final_weight": item.final_weight,
                    "confidence": item.confidence
                }
                for item in evidence_items
            }
        }

        self.weight_history.append(history_entry)

        # 保持历史记录在合理范围内
        if len(self.weight_history) > 100:
            self.weight_history = self.weight_history[-100:]

    def apply_weight_guidance(self, evidence_items: List[EvidenceItem],
                            guidance_strategy: str = "threshold_filter") -> List[EvidenceItem]:
        """
        应用权重引导策略

        Args:
            evidence_items: 证据项列表
            guidance_strategy: 引导策略类型

        Returns:
            引导后的证据项列表
        """
        logger.info(f"应用权重引导策略: {guidance_strategy}")

        if guidance_strategy == "threshold_filter":
            return self._apply_threshold_filter(evidence_items)
        elif guidance_strategy == "priority_ranking":
            return self._apply_priority_ranking(evidence_items)
        elif guidance_strategy == "adaptive_weighting":
            return self._apply_adaptive_weighting(evidence_items)
        else:
            logger.warning(f"未知的引导策略: {guidance_strategy}")
            return evidence_items

    def _apply_threshold_filter(self, evidence_items: List[EvidenceItem],
                              threshold: float = 0.5) -> List[EvidenceItem]:
        """应用阈值过滤策略"""
        filtered_items = []

        for item in evidence_items:
            if item.final_weight >= threshold:
                filtered_items.append(item)
            else:
                logger.debug(f"证据 {item.id} 权重 {item.final_weight:.3f} 低于阈值 {threshold}，被过滤")

        logger.info(f"阈值过滤完成: {len(filtered_items)}/{len(evidence_items)} 个证据通过")
        return filtered_items

    def _apply_priority_ranking(self, evidence_items: List[EvidenceItem]) -> List[EvidenceItem]:
        """应用优先级排序策略"""
        # 按最终权重降序排序
        sorted_items = sorted(evidence_items, key=lambda x: x.final_weight, reverse=True)

        # 为每个证据分配优先级
        for i, item in enumerate(sorted_items):
            item.metadata["priority_rank"] = i + 1
            item.metadata["priority_score"] = 1.0 - (i / len(sorted_items))

        logger.info(f"优先级排序完成，最高权重: {sorted_items[0].final_weight:.3f}")
        return sorted_items

    def _apply_adaptive_weighting(self, evidence_items: List[EvidenceItem]) -> List[EvidenceItem]:
        """应用自适应权重调整策略"""
        if len(self.weight_history) < 2:
            return evidence_items

        # 计算权重变化趋势
        recent_weights = self.weight_history[-2:]

        for item in evidence_items:
            if item.id in recent_weights[0]["weights"] and item.id in recent_weights[1]["weights"]:
                old_weight = recent_weights[0]["weights"][item.id]["final_weight"]
                current_weight = recent_weights[1]["weights"][item.id]["final_weight"]

                # 计算权重变化率
                weight_change = (current_weight - old_weight) / old_weight if old_weight > 0 else 0

                # 自适应调整
                if abs(weight_change) > 0.1:  # 权重变化超过10%
                    adjustment_factor = 1.0 + self.learning_rate * weight_change
                    item.final_weight *= adjustment_factor
                    item.final_weight = max(0.0, min(1.0, item.final_weight))

                    item.metadata["adaptive_adjustment"] = adjustment_factor

        logger.info("自适应权重调整完成")
        return evidence_items

    def dynamic_weight_adjustment(self, evidence_items: List[EvidenceItem],
                                context_updates: Dict[str, Any]) -> List[EvidenceItem]:
        """
        动态权重调整系统

        Args:
            evidence_items: 证据项列表
            context_updates: 上下文更新信息

        Returns:
            调整后的证据项列表
        """
        logger.info("开始动态权重调整")

        # 1. 上下文感知调整
        self._context_aware_adjustment(evidence_items, context_updates)

        # 2. 协作信息调整
        self._collaboration_based_adjustment(evidence_items, context_updates)

        # 3. 学习优化调整
        self._learning_based_adjustment(evidence_items)

        # 4. 收敛性检查
        convergence_achieved = self._check_convergence(evidence_items)

        if convergence_achieved:
            logger.info("权重调整已收敛")
        else:
            logger.info("权重调整未收敛，继续优化")

        return evidence_items

    def _context_aware_adjustment(self, evidence_items: List[EvidenceItem],
                                context_updates: Dict[str, Any]):
        """上下文感知的权重调整"""
        new_context = context_updates.get("case_context", "")
        context_importance = context_updates.get("context_importance", 1.0)

        if not new_context:
            return

        # 重新计算相关性分析
        for item in evidence_items:
            old_relevance = item.relevance_score
            new_relevance = self._assess_topic_relevance(item.content, new_context)

            # 加权更新相关性分数
            item.relevance_score = (
                0.7 * old_relevance +
                0.3 * new_relevance * context_importance
            )

            # 重新计算最终权重
            self._recalculate_final_weight(item)

    def _collaboration_based_adjustment(self, evidence_items: List[EvidenceItem],
                                      context_updates: Dict[str, Any]):
        """基于智能体协作信息的权重调整"""
        collaboration_feedback = context_updates.get("collaboration_feedback", {})

        for item in evidence_items:
            if item.id in collaboration_feedback:
                feedback = collaboration_feedback[item.id]

                # 根据协作反馈调整可靠性评分
                reliability_adjustment = feedback.get("reliability_adjustment", 0.0)
                item.reliability_score += reliability_adjustment
                item.reliability_score = max(0.0, min(1.0, item.reliability_score))

                # 根据协作反馈调整相互印证度
                corroboration_adjustment = feedback.get("corroboration_adjustment", 0.0)
                item.corroboration_score += corroboration_adjustment
                item.corroboration_score = max(0.0, min(1.0, item.corroboration_score))

                # 重新计算最终权重
                self._recalculate_final_weight(item)

    def _learning_based_adjustment(self, evidence_items: List[EvidenceItem]):
        """基于学习的权重调整"""
        if len(self.weight_history) < 3:
            return

        # 分析权重历史趋势
        for item in evidence_items:
            weight_trend = self._analyze_weight_trend(item.id)

            if weight_trend["trend"] == "increasing":
                # 权重持续增加，适度提升
                boost_factor = 1.0 + self.learning_rate * weight_trend["strength"]
                item.final_weight *= boost_factor
            elif weight_trend["trend"] == "decreasing":
                # 权重持续下降，适度降低
                reduction_factor = 1.0 - self.learning_rate * weight_trend["strength"]
                item.final_weight *= reduction_factor

            # 确保权重在合理范围内
            item.final_weight = max(0.0, min(1.0, item.final_weight))

    def _analyze_weight_trend(self, evidence_id: str) -> Dict[str, Any]:
        """分析权重变化趋势"""
        if len(self.weight_history) < 3:
            return {"trend": "stable", "strength": 0.0}

        recent_weights = []
        for history in self.weight_history[-3:]:
            if evidence_id in history["weights"]:
                recent_weights.append(history["weights"][evidence_id]["final_weight"])

        if len(recent_weights) < 3:
            return {"trend": "stable", "strength": 0.0}

        # 计算趋势
        diff1 = recent_weights[1] - recent_weights[0]
        diff2 = recent_weights[2] - recent_weights[1]

        if diff1 > 0 and diff2 > 0:
            trend = "increasing"
            strength = (abs(diff1) + abs(diff2)) / 2
        elif diff1 < 0 and diff2 < 0:
            trend = "decreasing"
            strength = (abs(diff1) + abs(diff2)) / 2
        else:
            trend = "stable"
            strength = 0.0

        return {"trend": trend, "strength": min(1.0, strength)}

    def _check_convergence(self, evidence_items: List[EvidenceItem]) -> bool:
        """检查权重调整的收敛性"""
        if len(self.weight_history) < 2:
            return False

        recent_weights = self.weight_history[-2:]
        total_change = 0.0
        count = 0

        for item in evidence_items:
            if (item.id in recent_weights[0]["weights"] and
                item.id in recent_weights[1]["weights"]):

                old_weight = recent_weights[0]["weights"][item.id]["final_weight"]
                new_weight = recent_weights[1]["weights"][item.id]["final_weight"]

                change = abs(new_weight - old_weight)
                total_change += change
                count += 1

        if count == 0:
            return True

        average_change = total_change / count
        return average_change < self.convergence_threshold

    def _recalculate_final_weight(self, item: EvidenceItem):
        """重新计算最终权重"""
        item.final_weight = (
            self.DIMENSION_WEIGHTS['type_weight'] * item.type_weight +
            self.DIMENSION_WEIGHTS['reliability_score'] * item.reliability_score +
            self.DIMENSION_WEIGHTS['completeness_score'] * item.completeness_score +
            self.DIMENSION_WEIGHTS['corroboration_score'] * item.corroboration_score +
            self.DIMENSION_WEIGHTS['relevance_score'] * item.relevance_score
        )

        # 确保权重在[0,1]范围内
        item.final_weight = max(0.0, min(1.0, item.final_weight))

    def get_weight_analysis_report(self, evidence_items: List[EvidenceItem]) -> Dict[str, Any]:
        """生成权重分析报告"""
        if not evidence_items:
            return {"error": "没有证据项可分析"}

        # 统计信息
        weights = [item.final_weight for item in evidence_items]
        confidences = [item.confidence for item in evidence_items]

        report = {
            "evidence_count": len(evidence_items),
            "weight_statistics": {
                "mean": np.mean(weights),
                "std": np.std(weights),
                "min": np.min(weights),
                "max": np.max(weights),
                "median": np.median(weights)
            },
            "confidence_statistics": {
                "mean": np.mean(confidences),
                "std": np.std(confidences),
                "min": np.min(confidences),
                "max": np.max(confidences)
            },
            "dimension_analysis": self._analyze_dimensions(evidence_items),
            "quality_assessment": self._assess_overall_quality(evidence_items),
            "recommendations": self._generate_recommendations(evidence_items)
        }

        return report

    def _analyze_dimensions(self, evidence_items: List[EvidenceItem]) -> Dict[str, Any]:
        """分析各维度表现"""
        dimensions = {
            "type_weight": [item.type_weight for item in evidence_items],
            "reliability_score": [item.reliability_score for item in evidence_items],
            "completeness_score": [item.completeness_score for item in evidence_items],
            "corroboration_score": [item.corroboration_score for item in evidence_items],
            "relevance_score": [item.relevance_score for item in evidence_items]
        }

        analysis = {}
        for dim_name, scores in dimensions.items():
            analysis[dim_name] = {
                "mean": np.mean(scores),
                "std": np.std(scores),
                "strength": "high" if np.mean(scores) > 0.7 else "medium" if np.mean(scores) > 0.4 else "low"
            }

        return analysis

    def _assess_overall_quality(self, evidence_items: List[EvidenceItem]) -> str:
        """评估整体质量"""
        avg_weight = np.mean([item.final_weight for item in evidence_items])
        avg_confidence = np.mean([item.confidence for item in evidence_items])

        overall_score = 0.6 * avg_weight + 0.4 * avg_confidence

        if overall_score > 0.8:
            return "excellent"
        elif overall_score > 0.6:
            return "good"
        elif overall_score > 0.4:
            return "fair"
        else:
            return "poor"

    def _generate_recommendations(self, evidence_items: List[EvidenceItem]) -> List[str]:
        """生成改进建议"""
        recommendations = []

        # 分析各维度表现
        dimension_analysis = self._analyze_dimensions(evidence_items)

        for dim_name, analysis in dimension_analysis.items():
            if analysis["strength"] == "low":
                if dim_name == "type_weight":
                    recommendations.append("建议收集更多高质量的物证和专家鉴定意见")
                elif dim_name == "reliability_score":
                    recommendations.append("建议验证证据来源的可靠性，寻找更权威的证据来源")
                elif dim_name == "completeness_score":
                    recommendations.append("建议补充证据的关键信息要素，提高描述的完整性")
                elif dim_name == "corroboration_score":
                    recommendations.append("建议寻找更多相互印证的证据，构建完整的证据链")
                elif dim_name == "relevance_score":
                    recommendations.append("建议重新评估证据与案件核心事实的相关性")

        # 整体建议
        avg_weight = np.mean([item.final_weight for item in evidence_items])
        if avg_weight < 0.5:
            recommendations.append("整体证据权重偏低，建议重新收集和整理证据")

        return recommendations
