#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重构系统功能验证测试

验证模块化重构后的司法信息抽取系统功能，包括：
1. 重构版本协调器测试
2. 模块化组件独立测试
3. 智能体协作机制测试
4. 性能对比测试

使用方法：
python test_refactored_system.py
"""

import sys
import logging
import time
from typing import Dict, List

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_refactored_coordinator():
    """测试重构版本的协调器"""
    logger.info("测试重构版本协调器...")
    
    try:
        from judicial_ie_coordinator_refactored import JudicialIECoordinator
        from extraction_types import ExtractionTaskType
        
        # 初始化重构版本协调器
        coordinator = JudicialIECoordinator(
            enable_parallel=False,
            enable_cache=False,
            enable_collaboration=True
        )
        
        # 测试文本
        test_text = "被告人张某于2023年3月在北京市朝阳区盗窃他人财物，价值5000元。"
        
        # 执行信息抽取
        result = coordinator.extract_information(
            test_text, 
            ExtractionTaskType.COMPREHENSIVE_EXTRACTION
        )
        
        # 检查结果
        assert isinstance(result, dict)
        assert 'status' in result
        assert 'results' in result
        assert 'metadata' in result
        
        # 关闭系统
        coordinator.shutdown()
        
        logger.info(f"✅ 重构版本协调器测试成功")
        logger.info(f"   抽取状态: {result.get('status', 'unknown')}")
        logger.info(f"   抽取结果数量: {len(result.get('results', []))}")
        logger.info(f"   处理时间: {result.get('metadata', {}).get('processing_time', 0):.2f}秒")
        return True
        
    except Exception as e:
        logger.error(f"❌ 重构版本协调器测试失败: {e}")
        return False

def test_modular_components():
    """测试模块化组件"""
    logger.info("测试模块化组件...")
    
    try:
        from core.message_bus import MessageBus, AgentMessage, MessageType
        from core.shared_knowledge import SharedKnowledgeBase
        from core.agent_dependency import AgentDependencyManager
        from core.collaborative_reasoning import CollaborativeReasoningEngine
        
        # 测试消息总线
        message_bus = MessageBus(max_workers=2)
        assert hasattr(message_bus, 'publish')
        assert hasattr(message_bus, 'register_agent')
        
        # 测试共享知识库
        knowledge_base = SharedKnowledgeBase()
        assert hasattr(knowledge_base, 'store_knowledge')
        assert hasattr(knowledge_base, 'search_knowledge')
        
        # 测试依赖关系管理器
        dependency_manager = AgentDependencyManager()
        assert hasattr(dependency_manager, 'add_dependency')
        assert hasattr(dependency_manager, 'get_execution_order')
        
        # 测试协作推理引擎
        reasoning_engine = CollaborativeReasoningEngine(message_bus, knowledge_base)
        assert hasattr(reasoning_engine, 'start_collaborative_reasoning')
        assert hasattr(reasoning_engine, 'calculate_consensus_level')
        
        # 关闭消息总线
        message_bus.shutdown()
        
        logger.info("✅ 模块化组件测试成功")
        return True
        
    except Exception as e:
        logger.error(f"❌ 模块化组件测试失败: {e}")
        return False

def test_agent_collaboration():
    """测试智能体协作机制"""
    logger.info("测试智能体协作机制...")
    
    try:
        from core.message_bus import MessageBus, AgentMessage, MessageType
        from core.shared_knowledge import SharedKnowledgeBase
        
        # 创建协作组件
        message_bus = MessageBus(max_workers=2)
        knowledge_base = SharedKnowledgeBase()
        
        # 注册测试智能体
        message_bus.register_agent("agent1", {"type": "test_agent"})
        message_bus.register_agent("agent2", {"type": "test_agent"})
        
        # 存储测试知识
        success = knowledge_base.store_knowledge(
            knowledge_id="test_knowledge",
            content={"test": "data", "confidence": 0.9},
            contributor_id="agent1",
            knowledge_type="test"
        )
        assert success
        
        # 搜索知识
        results = knowledge_base.search_knowledge(
            query={"type": "test", "keywords": ["test"]},
            requester_id="agent2"
        )
        assert len(results) > 0
        
        # 发布测试消息
        message = AgentMessage(
            sender_id="agent1",
            receiver_id="agent2",
            message_type=MessageType.KNOWLEDGE_SHARE,
            content={"test": "collaboration"}
        )
        success = message_bus.publish(message)
        assert success
        
        # 等待消息处理
        time.sleep(0.5)
        
        # 获取统计信息
        stats = message_bus.get_processing_stats()
        assert "processed_count" in stats
        
        # 关闭消息总线
        message_bus.shutdown()
        
        logger.info("✅ 智能体协作机制测试成功")
        logger.info(f"   知识库条目: {len(knowledge_base.knowledge_store)}")
        logger.info(f"   消息处理统计: {stats}")
        return True
        
    except Exception as e:
        logger.error(f"❌ 智能体协作机制测试失败: {e}")
        return False

def test_performance_comparison():
    """测试性能对比"""
    logger.info("测试性能对比...")
    
    try:
        from judicial_ie_coordinator_refactored import JudicialIECoordinator
        from extraction_types import ExtractionTaskType
        
        test_text = "被告人李某于2023年5月在上海市浦东新区故意伤害他人身体，造成轻伤。"
        
        # 测试协作模式
        start_time = time.time()
        coordinator_collab = JudicialIECoordinator(enable_collaboration=True)
        result_collab = coordinator_collab.extract_information(test_text, ExtractionTaskType.COMPREHENSIVE_EXTRACTION)
        collab_time = time.time() - start_time
        coordinator_collab.shutdown()
        
        # 测试非协作模式
        start_time = time.time()
        coordinator_simple = JudicialIECoordinator(enable_collaboration=False)
        result_simple = coordinator_simple.extract_information(test_text, ExtractionTaskType.COMPREHENSIVE_EXTRACTION)
        simple_time = time.time() - start_time
        coordinator_simple.shutdown()
        
        logger.info("✅ 性能对比测试成功")
        logger.info(f"   协作模式: {collab_time:.2f}秒, 结果数: {len(result_collab.get('results', []))}")
        logger.info(f"   简单模式: {simple_time:.2f}秒, 结果数: {len(result_simple.get('results', []))}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 性能对比测试失败: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    logger.info("开始重构系统功能验证")
    logger.info("=" * 50)
    
    tests = [
        ("重构版本协调器", test_refactored_coordinator),
        ("模块化组件", test_modular_components),
        ("智能体协作机制", test_agent_collaboration),
        ("性能对比", test_performance_comparison)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 执行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"测试 {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果总结
    logger.info("\n" + "=" * 50)
    logger.info("📋 测试结果总结")
    logger.info("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name:15} : {status}")
        if result:
            passed += 1
    
    logger.info(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        logger.info("🎉 所有测试通过！重构系统功能正常！")
        return True
    else:
        logger.warning("⚠️  部分测试失败，需要进一步检查")
        return False

def main():
    """主函数"""
    print("司法信息抽取系统 - 重构版本功能验证")
    print("=" * 60)
    
    success = run_all_tests()
    
    if success:
        print("\n✅ 重构验证完成：模块化架构工作正常")
        print("📝 系统已准备好进行学术研究和论文撰写")
    else:
        print("\n❌ 重构验证发现问题，请检查错误日志")
        print("🔧 建议修复问题后重新运行测试")

if __name__ == "__main__":
    main()
