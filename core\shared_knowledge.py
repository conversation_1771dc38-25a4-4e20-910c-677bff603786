"""
共享知识库系统 (SharedKnowledgeBase)

多智能体协作的知识共享基础设施，提供：
- 版本化知识存储
- 智能知识搜索
- 依赖关系追踪
- 访问统计分析
- 知识置信度评估

学术价值：
- 支持智能体间的知识共享和积累
- 提供语义相似度计算和智能搜索
- 为协作推理提供知识基础
"""

import json
import logging
import time
import threading
from typing import Dict, List, Optional, Any
from collections import defaultdict

# 配置日志
logger = logging.getLogger(__name__)


class SharedKnowledgeBase:
    """智能体共享知识库系统"""

    def __init__(self):
        self.knowledge_store: Dict[str, Dict] = {}
        self.version_history: Dict[str, List[Dict]] = defaultdict(list)
        self.access_log: List[Dict] = []
        self.knowledge_dependencies: Dict[str, List[str]] = defaultdict(list)
        self._lock = threading.Lock()

        logger.info("共享知识库系统初始化完成")

    def store_knowledge(self, knowledge_id: str, content: Dict,
                       contributor_id: str, knowledge_type: str = "general") -> bool:
        """存储知识"""
        try:
            with self._lock:
                # 创建知识条目
                knowledge_entry = {
                    "id": knowledge_id,
                    "content": content,
                    "type": knowledge_type,
                    "contributor": contributor_id,
                    "created_at": time.time(),
                    "version": len(self.version_history[knowledge_id]) + 1,
                    "access_count": 0,
                    "confidence": content.get("confidence", 0.8),
                    "tags": content.get("tags", [])
                }

                # 存储到主知识库
                self.knowledge_store[knowledge_id] = knowledge_entry

                # 记录版本历史
                self.version_history[knowledge_id].append(knowledge_entry.copy())

                # 记录访问日志
                self.access_log.append({
                    "action": "store",
                    "knowledge_id": knowledge_id,
                    "agent_id": contributor_id,
                    "timestamp": time.time()
                })

            logger.info(f"知识 {knowledge_id} 已存储，贡献者: {contributor_id}")
            return True

        except Exception as e:
            logger.error(f"知识存储失败: {e}")
            return False

    def retrieve_knowledge(self, knowledge_id: str, requester_id: str) -> Optional[Dict]:
        """检索知识"""
        try:
            with self._lock:
                if knowledge_id in self.knowledge_store:
                    knowledge = self.knowledge_store[knowledge_id].copy()
                    knowledge["access_count"] += 1

                    # 记录访问日志
                    self.access_log.append({
                        "action": "retrieve",
                        "knowledge_id": knowledge_id,
                        "agent_id": requester_id,
                        "timestamp": time.time()
                    })

                    return knowledge
                else:
                    return None

        except Exception as e:
            logger.error(f"知识检索失败: {e}")
            return None

    def update_knowledge(self, knowledge_id: str, updates: Dict,
                        updater_id: str) -> bool:
        """更新知识"""
        try:
            with self._lock:
                if knowledge_id in self.knowledge_store:
                    # 备份当前版本
                    current = self.knowledge_store[knowledge_id].copy()
                    self.version_history[knowledge_id].append(current)

                    # 应用更新
                    self.knowledge_store[knowledge_id]["content"].update(updates)
                    self.knowledge_store[knowledge_id]["version"] += 1
                    self.knowledge_store[knowledge_id]["last_updated"] = time.time()
                    self.knowledge_store[knowledge_id]["last_updater"] = updater_id

                    # 记录访问日志
                    self.access_log.append({
                        "action": "update",
                        "knowledge_id": knowledge_id,
                        "agent_id": updater_id,
                        "timestamp": time.time()
                    })

                    return True
                else:
                    return False

        except Exception as e:
            logger.error(f"知识更新失败: {e}")
            return False

    def search_knowledge(self, query: Dict, requester_id: str) -> List[Dict]:
        """智能知识搜索"""
        results = []

        try:
            with self._lock:
                for knowledge_id, knowledge in self.knowledge_store.items():
                    # 计算多维度匹配分数
                    match_components = self._calculate_match_components(query, knowledge)

                    # 动态权重计算（基于查询类型和知识特征）
                    weights = self._calculate_dynamic_weights(query, knowledge)

                    # 加权总分
                    match_score = sum(weights[component] * score
                                    for component, score in match_components.items())

                    # 置信度阈值检查
                    confidence_threshold = query.get("min_confidence", 0.5)
                    min_match_threshold = query.get("min_match_score", 0.2)

                    if (knowledge["confidence"] >= confidence_threshold and
                        match_score >= min_match_threshold):
                        result = knowledge.copy()
                        result["match_score"] = match_score
                        result["match_components"] = match_components
                        result["match_weights"] = weights
                        results.append(result)

            # 按匹配分数排序
            results.sort(key=lambda x: x["match_score"], reverse=True)

            # 记录搜索日志
            self.access_log.append({
                "action": "search",
                "query": query,
                "agent_id": requester_id,
                "result_count": len(results),
                "timestamp": time.time()
            })

            return results

        except Exception as e:
            logger.error(f"知识搜索失败: {e}")
            return []

    def _calculate_match_components(self, query: Dict, knowledge: Dict) -> Dict[str, float]:
        """计算多维度匹配分数"""
        components = {}

        # 1. 类型匹配
        query_type = query.get("type", "")
        knowledge_type = knowledge.get("type", "")
        components["type_match"] = 1.0 if query_type == knowledge_type else 0.0

        # 2. 关键词匹配
        query_keywords = set(str(kw).lower() for kw in query.get("keywords", []))
        knowledge_text = str(knowledge.get("content", "")).lower()
        knowledge_tags = set(str(tag).lower() for tag in knowledge.get("tags", []))

        if query_keywords:
            keyword_matches = sum(1 for kw in query_keywords 
                                if kw in knowledge_text or kw in knowledge_tags)
            components["keyword_match"] = keyword_matches / len(query_keywords)
        else:
            components["keyword_match"] = 0.5

        # 3. 标签匹配
        query_tags = set(str(tag).lower() for tag in query.get("tags", []))
        if query_tags and knowledge_tags:
            tag_intersection = query_tags.intersection(knowledge_tags)
            components["tag_match"] = len(tag_intersection) / len(query_tags.union(knowledge_tags))
        else:
            components["tag_match"] = 0.0

        # 4. 时间相关性
        time_preference = query.get("time_preference", "neutral")
        components["temporal_relevance"] = self._calculate_temporal_relevance(time_preference, knowledge)

        # 5. 置信度匹配
        components["confidence_match"] = knowledge.get("confidence", 0.5)

        return components

    def _calculate_dynamic_weights(self, query: Dict, knowledge: Dict) -> Dict[str, float]:
        """动态权重计算"""
        # 基础权重
        weights = {
            "type_match": 0.3,
            "keyword_match": 0.25,
            "tag_match": 0.2,
            "temporal_relevance": 0.15,
            "confidence_match": 0.1
        }

        # 根据查询类型调整权重
        query_type = query.get("type", "")
        if "extraction" in query_type:
            weights["type_match"] = 0.4
            weights["keyword_match"] = 0.3
        elif "reasoning" in query_type:
            weights["confidence_match"] = 0.2
            weights["temporal_relevance"] = 0.1

        # 根据知识特征调整权重
        if knowledge.get("access_count", 0) > 10:
            # 高访问量的知识增加置信度权重
            weights["confidence_match"] += 0.05

        return weights

    def _calculate_temporal_relevance(self, time_preference: str, knowledge: Dict) -> float:
        """计算时间相关性"""
        knowledge_age_days = (time.time() - knowledge.get("created_at", time.time())) / 86400

        if time_preference == "recent":
            # 偏好最近的知识
            if knowledge_age_days < 1:
                return 1.0
            elif knowledge_age_days < 7:
                return 0.8
            elif knowledge_age_days < 30:
                return 0.6
            else:
                return 0.4
        elif time_preference == "historical":
            # 偏好历史知识
            if knowledge_age_days > 30:
                return 1.0
            elif knowledge_age_days > 7:
                return 0.8
            else:
                return 0.6
        else:
            # 中性时间偏好
            return 0.7

    def add_dependency(self, knowledge_id: str, depends_on: str):
        """添加知识依赖关系"""
        self.knowledge_dependencies[knowledge_id].append(depends_on)

    def get_knowledge_stats(self) -> Dict:
        """获取知识库统计信息"""
        return {
            "total_knowledge_items": len(self.knowledge_store),
            "total_versions": sum(len(versions) for versions in self.version_history.values()),
            "total_accesses": len(self.access_log),
            "knowledge_types": list(set(k["type"] for k in self.knowledge_store.values())),
            "most_accessed": max(self.knowledge_store.items(),
                               key=lambda x: x[1]["access_count"], default=(None, {"access_count": 0}))[0]
        }

    def get_version_history(self, knowledge_id: str) -> List[Dict]:
        """获取知识版本历史"""
        return self.version_history.get(knowledge_id, [])

    def get_access_log(self, knowledge_id: Optional[str] = None, 
                      agent_id: Optional[str] = None) -> List[Dict]:
        """获取访问日志"""
        logs = self.access_log

        if knowledge_id:
            logs = [log for log in logs if log.get("knowledge_id") == knowledge_id]

        if agent_id:
            logs = [log for log in logs if log.get("agent_id") == agent_id]

        return logs

    def cleanup_old_knowledge(self, max_age_days: int = 30, min_access_count: int = 1):
        """清理旧知识"""
        current_time = time.time()
        cutoff_time = current_time - (max_age_days * 86400)

        with self._lock:
            to_remove = []
            for knowledge_id, knowledge in self.knowledge_store.items():
                if (knowledge.get("created_at", current_time) < cutoff_time and
                    knowledge.get("access_count", 0) < min_access_count):
                    to_remove.append(knowledge_id)

            for knowledge_id in to_remove:
                del self.knowledge_store[knowledge_id]
                if knowledge_id in self.version_history:
                    del self.version_history[knowledge_id]

            logger.info(f"清理了 {len(to_remove)} 个旧知识条目")

    def export_knowledge(self, knowledge_type: Optional[str] = None) -> Dict:
        """导出知识库"""
        with self._lock:
            if knowledge_type:
                filtered_knowledge = {
                    k: v for k, v in self.knowledge_store.items()
                    if v.get("type") == knowledge_type
                }
            else:
                filtered_knowledge = self.knowledge_store.copy()

            return {
                "knowledge_store": filtered_knowledge,
                "export_time": time.time(),
                "total_items": len(filtered_knowledge)
            }
