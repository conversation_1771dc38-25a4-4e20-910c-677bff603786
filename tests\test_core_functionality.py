"""
核心功能测试脚本

测试多智能体司法信息抽取系统的核心功能
"""

import sys
import traceback

def test_entity_extractor():
    """测试实体抽取智能体"""
    print("=" * 60)
    print("测试实体抽取智能体")
    print("=" * 60)
    
    try:
        # 导入实体抽取智能体
        from extraction_agents.entity_extractor import EntityExtractionAgent, ExtractionTaskType
        print("✅ 实体抽取智能体导入成功")
        
        # 创建智能体实例
        agent = EntityExtractionAgent()
        print("✅ 实体抽取智能体初始化成功")
        
        # 测试案例
        test_cases = [
            "被告人张某于2023年3月15日在某市某区犯故意伤害罪。",
            "被害人李某在某街道受到伤害，证人王某目击了整个过程。",
            "法官刘某某主持审理，律师陈某为被告人辩护。"
        ]
        
        total_results = 0
        for i, test_text in enumerate(test_cases, 1):
            print(f"\n测试案例 {i}: {test_text}")
            
            # 执行抽取
            results = agent.extract(test_text, ExtractionTaskType.ENTITY_EXTRACTION)
            total_results += len(results)
            
            print(f"抽取到 {len(results)} 个实体:")
            for result in results:
                print(f"  - {result.info_type.value}: {result.content} (置信度: {result.confidence:.2f})")
        
        print(f"\n✅ 实体抽取测试完成，总共抽取到 {total_results} 个实体")
        return True
        
    except Exception as e:
        print(f"❌ 实体抽取测试失败: {e}")
        traceback.print_exc()
        return False

def test_ie_coordinator():
    """测试信息抽取协调器"""
    print("\n" + "=" * 60)
    print("测试信息抽取协调器")
    print("=" * 60)
    
    try:
        # 导入协调器
        from judicial_ie_coordinator import JudicialIECoordinator, ExtractionTaskType
        print("✅ 信息抽取协调器导入成功")
        
        # 创建协调器实例
        coordinator = JudicialIECoordinator()
        print("✅ 信息抽取协调器初始化成功")
        
        # 测试案例
        test_text = """
        被告人张某于2023年3月15日晚上8时许，在某市某区某街道因琐事与被害人李某发生争执，
        张某持木棒击打李某头部，致李某轻伤二级。案发后，张某主动投案自首，如实供述犯罪事实，
        认罪态度良好。本院认为，被告人张某故意伤害他人身体，致人轻伤，其行为已构成故意伤害罪。
        判决被告人张某犯故意伤害罪，判处拘役六个月。
        """
        
        print(f"\n测试文本长度: {len(test_text)} 字符")
        
        # 执行信息抽取
        result = coordinator.extract_information(test_text, ExtractionTaskType.ENTITY_EXTRACTION)
        
        print(f"抽取状态: {result.get('status', 'unknown')}")
        
        if result.get('status') == 'success':
            results_data = result.get('results', {})
            detailed_results = results_data.get('detailed_results', [])
            quality_metrics = results_data.get('quality_metrics', {})
            
            print(f"抽取到 {len(detailed_results)} 个结果")
            print(f"平均置信度: {quality_metrics.get('average_confidence', 0):.3f}")
            print(f"平均证据权重: {quality_metrics.get('average_evidence_weight', 0):.3f}")
            
            print("\n详细结果:")
            for result_item in detailed_results[:5]:  # 只显示前5个
                print(f"  - {result_item.get('info_type', 'unknown')}: {result_item.get('content', '')}")
                print(f"    置信度: {result_item.get('confidence', 0):.2f}, 证据权重: {result_item.get('evidence_weight', 0):.2f}")
        
        print("✅ 信息抽取协调器测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 信息抽取协调器测试失败: {e}")
        traceback.print_exc()
        return False

def test_evidence_weight_integration():
    """测试证据权重分析集成"""
    print("\n" + "=" * 60)
    print("测试证据权重分析集成")
    print("=" * 60)
    
    try:
        # 导入证据权重分析器
        from evidence_weight_analyzer import EvidenceWeightAnalyzer, Evidence
        from judicial_cola import get_completion
        print("✅ 证据权重分析器导入成功")
        
        # 创建分析器实例
        analyzer = EvidenceWeightAnalyzer(get_completion)
        print("✅ 证据权重分析器初始化成功")
        
        # 测试证据权重分析
        test_fact = "被告人张某持木棒击打被害人李某头部，致李某轻伤二级。"
        
        # 创建测试证据
        evidence_list = [
            Evidence(
                id="evidence_1",
                type="physical_evidence",
                content="木棒作为作案工具",
                source="现场勘查",
                weight=0.8,
                reliability=0.9,
                relevance=0.9,
                consistency=0.8
            ),
            Evidence(
                id="evidence_2", 
                type="medical_evidence",
                content="被害人轻伤二级鉴定",
                source="法医鉴定",
                weight=0.9,
                reliability=0.95,
                relevance=0.9,
                consistency=0.9
            )
        ]
        
        print(f"测试事实: {test_fact}")
        print(f"证据数量: {len(evidence_list)}")
        
        # 执行证据权重分析
        analysis_result = analyzer.analyze_evidence_weights(test_fact, evidence_list)
        
        print("证据权重分析结果:")
        evidence_weights = analysis_result.get("evidence_weights", {})
        for evidence_id, weight in evidence_weights.items():
            print(f"  - {evidence_id}: 权重 {weight:.3f}")
        
        key_evidence = analysis_result.get("key_evidence", [])
        print(f"关键证据: {key_evidence}")
        
        print("✅ 证据权重分析集成测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 证据权重分析集成测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始核心功能测试")
    print("测试多智能体司法信息抽取系统的核心组件")
    
    test_results = []
    
    # 测试实体抽取智能体
    test_results.append(("实体抽取智能体", test_entity_extractor()))
    
    # 测试信息抽取协调器
    test_results.append(("信息抽取协调器", test_ie_coordinator()))
    
    # 测试证据权重分析集成
    test_results.append(("证据权重分析集成", test_evidence_weight_integration()))
    
    # 输出测试总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    passed = 0
    failed = 0
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\n总计: {passed} 个测试通过, {failed} 个测试失败")
    
    if failed == 0:
        print("🎉 所有核心功能测试通过！系统基础架构运行正常。")
    else:
        print("⚠️  部分测试失败，需要进一步调试。")
    
    return failed == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
