"""
共享的信息抽取类型定义
避免循环导入问题
"""

from typing import Dict, Any
from dataclasses import dataclass
from enum import Enum

class ExtractionTaskType(Enum):
    """信息抽取任务类型"""
    ENTITY_EXTRACTION = "entity_extraction"
    FACT_EXTRACTION = "fact_extraction"
    LEGAL_ELEMENT_EXTRACTION = "legal_element_extraction"
    SENTENCE_EXTRACTION = "sentence_extraction"
    COMPREHENSIVE_EXTRACTION = "comprehensive_extraction"

class InformationType(Enum):
    """司法信息类型"""
    # 基础实体
    DEFENDANT = "defendant"
    VICTIM = "victim"
    WITNESS = "witness"
    JUDGE = "judge"
    LAWYER = "lawyer"

    # 时空信息
    CRIME_TIME = "crime_time"
    TRIAL_TIME = "trial_time"
    CRIME_LOCATION = "crime_location"

    # 案件核心信息
    CASE_FACTS = "case_facts"
    EVIDENCE = "evidence"
    CHARGES = "charges"
    LEGAL_ARTICLES = "legal_articles"
    LEGAL_ELEMENTS = "legal_elements"
    SENTENCING_FACTORS = "sentencing_factors"

    # 判决信息
    VERDICT = "verdict"
    SENTENCE = "sentence"
    IMPRISONMENT = "imprisonment"
    JUDGMENT_REASONING = "judgment_reasoning"
    EXECUTION_METHOD = "execution_method"
    FINE = "fine"
    PROBATION = "probation"

@dataclass
class ExtractionResult:
    """信息抽取结果"""
    info_type: InformationType
    content: str
    confidence: float
    evidence_weight: float  # 核心创新：证据权重
    source_span: tuple
    context: str
    agent_id: str
    metadata: Dict[str, Any] = None

    def to_dict(self) -> Dict:
        return {
            "info_type": self.info_type.value,
            "content": self.content,
            "confidence": self.confidence,
            "evidence_weight": self.evidence_weight,
            "source_span": self.source_span,
            "context": self.context,
            "agent_id": self.agent_id,
            "metadata": self.metadata or {}
        }
