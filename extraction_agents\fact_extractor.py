"""
事实抽取智能体 (FactExtractionAgent)

基于LLM的智能事实抽取专家，专门负责从司法文本中智能抽取案件事实信息，包括：
- 案件基本事实（犯罪行为、时间、地点、方式等）
- 因果关系分析（行为与结果的因果链）
- 情节要素（从轻、从重情节）
- 损害后果（财产损失、人身伤害等）

核心特色：
1. 基于GPT-4的智能语义理解和事实识别
2. 继承统一的智能体基类架构
3. 专业化的司法事实分析和逻辑推理能力
4. 智能化的事实完整性检验和补充机制
5. 与证据权重分析系统的深度集成
"""

import logging
import json
from typing import Dict, List

# 导入基类和相关类型
from .base_agent import BaseExtractionAgent
from extraction_types import ExtractionTaskType, InformationType, ExtractionResult

# 配置日志
logger = logging.getLogger(__name__)


class FactExtractionAgent(BaseExtractionAgent):
    """基于LLM的事实抽取智能体 - 专门负责案件事实信息的智能抽取"""

    def __init__(self, legal_knowledge_base: Dict = None):
        super().__init__("llm_fact_extractor", legal_knowledge_base)
        
        # 专业化领域：案件事实信息
        self.specialization = [
            InformationType.CASE_FACTS,
            InformationType.EVIDENCE,
            InformationType.CHARGES
        ]
        
        # 扩展性能统计
        self.performance_stats.update({
            "fact_completeness_scores": [],
            "causal_relationships_identified": 0,
            "mitigating_factors_found": 0,
            "aggravating_factors_found": 0
        })
        
        logger.info(f"基于LLM的事实抽取智能体 {self.agent_id} 初始化完成")

    def extract(self, text: str, task_type: ExtractionTaskType) -> List[ExtractionResult]:
        """
        执行基于LLM的智能事实抽取

        Args:
            text: 待抽取的司法文本
            task_type: 抽取任务类型

        Returns:
            事实抽取结果列表
        """
        logger.info(f"开始基于LLM的事实抽取，文本长度: {len(text)} 字符")

        try:
            # 构建LLM事实抽取提示
            prompt = self._build_extraction_prompt(text, task_type)

            # 调用LLM进行事实抽取
            logger.info("调用LLM进行事实抽取...")
            response = self._call_llm(prompt, "judicial fact extraction expert")

            # 解析LLM响应
            parsed_results = self._parse_llm_response(response, text)

            # 转换为ExtractionResult对象
            extraction_results = self._convert_to_extraction_results(parsed_results, text)

            # 后处理：事实完整性检验和质量评估
            validated_results = self._validate_fact_completeness(extraction_results, text)

            # 更新性能统计
            self._update_performance_stats(validated_results)

            logger.info(f"基于LLM的事实抽取完成，共抽取 {len(validated_results)} 个事实")
            return validated_results

        except Exception as e:
            logger.error(f"基于LLM的事实抽取过程出错: {e}")
            return []

    def _build_extraction_prompt(self, text: str, task_type: ExtractionTaskType) -> str:
        """构建LLM事实抽取提示"""
        
        prompt = f"""作为一名专业的司法事实抽取专家，请从以下司法文本中准确抽取案件的核心事实信息。

【司法文本】
{text}

【抽取任务】
请从上述文本中抽取以下类型的事实信息：

1. **案件基本事实**：
   - 犯罪行为的具体描述
   - 行为发生的时间、地点、方式
   - 涉及的人员及其角色关系
   - 行为的直接后果

2. **因果关系链**：
   - 行为与结果之间的因果关系
   - 多个行为之间的逻辑关系
   - 时间顺序和逻辑顺序

3. **情节要素**：
   - 从轻处罚情节（如自首、认罪认罚、积极赔偿等）
   - 从重处罚情节（如累犯、造成严重后果、拒不认罪等）
   - 特殊情节（如未成年人、精神病人等）

4. **损害后果**：
   - 财产损失的具体数额和性质
   - 人身伤害的程度和影响
   - 社会影响和其他后果

【抽取要求】
1. **准确性**：确保抽取的事实信息准确无误，严格基于文本内容
2. **完整性**：尽可能抽取所有相关的事实信息，构建完整的事实链
3. **逻辑性**：注意事实之间的逻辑关系和因果关系
4. **客观性**：只抽取客观事实，不添加主观判断或推测

【输出格式】
请严格按照以下JSON格式输出抽取结果：
{{
    "basic_facts": [
        {{
            "fact": "具体事实描述",
            "type": "行为/时间/地点/人员/后果",
            "confidence": 0.95
        }}
    ],
    "causal_relationships": [
        {{
            "cause": "原因事实",
            "effect": "结果事实",
            "relationship_type": "直接因果/间接因果",
            "confidence": 0.90
        }}
    ],
    "mitigating_factors": [
        {{
            "factor": "从轻情节描述",
            "legal_basis": "法律依据",
            "confidence": 0.85
        }}
    ],
    "aggravating_factors": [
        {{
            "factor": "从重情节描述",
            "legal_basis": "法律依据",
            "confidence": 0.85
        }}
    ],
    "damages": [
        {{
            "damage_type": "财产损失/人身伤害/其他",
            "description": "损害描述",
            "amount": "具体数额（如适用）",
            "confidence": 0.80
        }}
    ]
}}

【注意事项】
1. 如果某类事实在文本中不存在，请返回空数组[]
2. 置信度应根据文本中信息的明确程度进行评估
3. 只返回JSON格式的结果，不要添加其他说明文字

请开始抽取："""

        return prompt

    def _convert_to_extraction_results(self, parsed_data: Dict, original_text: str) -> List[ExtractionResult]:
        """将解析后的数据转换为ExtractionResult对象"""
        results = []
        
        # 处理基本事实
        basic_facts = parsed_data.get("basic_facts", [])
        for fact in basic_facts:
            if fact and fact.get("fact"):
                confidence = fact.get("confidence", 0.8)
                evidence_weight = self._calculate_evidence_weight(fact.get("fact", ""), InformationType.CASE_FACTS)
                source_span = self._find_source_span(fact.get("fact", ""), original_text)
                context = self._extract_context(fact.get("fact", ""), original_text)

                result = ExtractionResult(
                    info_type=InformationType.CASE_FACTS,
                    content=fact.get("fact", ""),
                    confidence=confidence,
                    evidence_weight=evidence_weight,
                    source_span=source_span,
                    context=context,
                    agent_id=self.agent_id,
                    metadata={
                        "extraction_method": "llm_based",
                        "fact_category": "basic_fact",
                        "fact_type": fact.get("type", "unknown")
                    }
                )
                results.append(result)

        # 处理因果关系
        causal_relationships = parsed_data.get("causal_relationships", [])
        for relationship in causal_relationships:
            if relationship and relationship.get("cause") and relationship.get("effect"):
                confidence = relationship.get("confidence", 0.8)
                content = f"因果关系：{relationship.get('cause')} → {relationship.get('effect')}"
                evidence_weight = self._calculate_evidence_weight(content, InformationType.CASE_FACTS)
                source_span = self._find_source_span(relationship.get("cause", ""), original_text)
                context = self._extract_context(relationship.get("cause", ""), original_text)

                result = ExtractionResult(
                    info_type=InformationType.CASE_FACTS,
                    content=content,
                    confidence=confidence,
                    evidence_weight=evidence_weight,
                    source_span=source_span,
                    context=context,
                    agent_id=self.agent_id,
                    metadata={
                        "extraction_method": "llm_based",
                        "fact_category": "causal_relationship",
                        "relationship_type": relationship.get("relationship_type", "unknown"),
                        "cause": relationship.get("cause", ""),
                        "effect": relationship.get("effect", "")
                    }
                )
                results.append(result)

        # 处理从轻情节
        mitigating_factors = parsed_data.get("mitigating_factors", [])
        for factor in mitigating_factors:
            if factor and factor.get("factor"):
                confidence = factor.get("confidence", 0.8)
                evidence_weight = self._calculate_evidence_weight(factor.get("factor", ""), InformationType.CASE_FACTS)
                source_span = self._find_source_span(factor.get("factor", ""), original_text)
                context = self._extract_context(factor.get("factor", ""), original_text)

                result = ExtractionResult(
                    info_type=InformationType.CASE_FACTS,
                    content=factor.get("factor", ""),
                    confidence=confidence,
                    evidence_weight=evidence_weight,
                    source_span=source_span,
                    context=context,
                    agent_id=self.agent_id,
                    metadata={
                        "extraction_method": "llm_based",
                        "fact_category": "mitigating_factor",
                        "legal_basis": factor.get("legal_basis", "")
                    }
                )
                results.append(result)

        # 处理从重情节
        aggravating_factors = parsed_data.get("aggravating_factors", [])
        for factor in aggravating_factors:
            if factor and factor.get("factor"):
                confidence = factor.get("confidence", 0.8)
                evidence_weight = self._calculate_evidence_weight(factor.get("factor", ""), InformationType.CASE_FACTS)
                source_span = self._find_source_span(factor.get("factor", ""), original_text)
                context = self._extract_context(factor.get("factor", ""), original_text)

                result = ExtractionResult(
                    info_type=InformationType.CASE_FACTS,
                    content=factor.get("factor", ""),
                    confidence=confidence,
                    evidence_weight=evidence_weight,
                    source_span=source_span,
                    context=context,
                    agent_id=self.agent_id,
                    metadata={
                        "extraction_method": "llm_based",
                        "fact_category": "aggravating_factor",
                        "legal_basis": factor.get("legal_basis", "")
                    }
                )
                results.append(result)

        # 处理损害后果
        damages = parsed_data.get("damages", [])
        for damage in damages:
            if damage and damage.get("description"):
                confidence = damage.get("confidence", 0.8)
                evidence_weight = self._calculate_evidence_weight(damage.get("description", ""), InformationType.CASE_FACTS)
                source_span = self._find_source_span(damage.get("description", ""), original_text)
                context = self._extract_context(damage.get("description", ""), original_text)

                result = ExtractionResult(
                    info_type=InformationType.CASE_FACTS,
                    content=damage.get("description", ""),
                    confidence=confidence,
                    evidence_weight=evidence_weight,
                    source_span=source_span,
                    context=context,
                    agent_id=self.agent_id,
                    metadata={
                        "extraction_method": "llm_based",
                        "fact_category": "damage",
                        "damage_type": damage.get("damage_type", "unknown"),
                        "amount": damage.get("amount", "")
                    }
                )
                results.append(result)

        return results

    def _validate_fact_completeness(self, results: List[ExtractionResult], text: str) -> List[ExtractionResult]:
        """验证事实完整性并进行补充抽取"""
        # 计算完整性分数
        completeness_score = self._calculate_completeness_score(results, text)
        self.performance_stats["fact_completeness_scores"].append(completeness_score)
        
        # 如果完整性不足，进行补充抽取
        if completeness_score < 0.7:
            logger.info(f"事实完整性不足 ({completeness_score:.2f})，进行补充抽取")
            supplementary_results = self._supplementary_extraction(results, text)
            results.extend(supplementary_results)
        
        return results

    def _calculate_completeness_score(self, results: List[ExtractionResult], text: str) -> float:
        """计算事实完整性分数"""
        # 基础分数
        base_score = 0.5
        
        # 根据抽取结果数量调整
        if len(results) > 0:
            base_score += 0.2
        if len(results) > 3:
            base_score += 0.2
            
        # 根据事实类型多样性调整
        fact_categories = set()
        for result in results:
            if result.metadata and "fact_category" in result.metadata:
                fact_categories.add(result.metadata["fact_category"])
        
        diversity_bonus = min(0.1 * len(fact_categories), 0.3)
        
        return min(1.0, base_score + diversity_bonus)

    def _supplementary_extraction(self, existing_results: List[ExtractionResult], text: str) -> List[ExtractionResult]:
        """补充抽取缺失的事实信息"""
        # 分析已有结果，确定缺失的事实类型
        existing_categories = set()
        for result in existing_results:
            if result.metadata and "fact_category" in result.metadata:
                existing_categories.add(result.metadata["fact_category"])
        
        # 构建补充抽取提示
        missing_categories = []
        if "basic_fact" not in existing_categories:
            missing_categories.append("基本事实")
        if "causal_relationship" not in existing_categories:
            missing_categories.append("因果关系")
        if "mitigating_factor" not in existing_categories:
            missing_categories.append("从轻情节")
        if "damage" not in existing_categories:
            missing_categories.append("损害后果")
        
        if not missing_categories:
            return []
        
        prompt = f"""请重新分析以下司法文本，专门抽取以下缺失的事实类型：{', '.join(missing_categories)}

【司法文本】
{text}

请特别关注可能被遗漏的{', '.join(missing_categories)}信息，使用相同的JSON格式输出。"""

        try:
            # 调用LLM进行补充抽取
            response = self._call_llm(prompt, "judicial fact extraction expert")
            
            # 解析补充结果
            parsed_results = self._parse_llm_response(response, text)
            supplementary_results = self._convert_to_extraction_results(parsed_results, text)
            
            logger.info(f"补充抽取完成，新增 {len(supplementary_results)} 个事实")
            return supplementary_results
            
        except Exception as e:
            logger.error(f"补充抽取失败: {e}")
            return []
