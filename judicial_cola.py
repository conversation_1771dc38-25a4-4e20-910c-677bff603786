import time
import logging
import json
import os
import jsonlines
import re
import traceback
import requests
from tqdm import tqdm
from collections import Counter

# 导入自定义模块
from utils import extract_key_elements, convert_cn_num
from evidence_weight_analyzer import EvidenceWeightAnalyzer

# 定义罪名提取模式常量
ACCUSATION_PATTERNS = [
    # 标准模式
    r'判[决定]被告人[\u4e00-\u9fa5\w]+犯([\u4e00-\u9fa5]+罪)',
    r'认定被告人[\u4e00-\u9fa5\w]+犯([\u4e00-\u9fa5]+罪)',
    r'构成([\u4e00-\u9fa5]+罪)',
    r'犯([\u4e00-\u9fa5]+罪)',

    # 处理带有"涉嫌"等前缀的情况
    r'判[决定]被告人[\u4e00-\u9fa5\w]+(?:涉嫌|被控)(?:犯)?([\u4e00-\u9fa5]+罪)',
    r'认定被告人[\u4e00-\u9fa5\w]+(?:涉嫌|被控)(?:犯)?([\u4e00-\u9fa5]+罪)',

    # 更宽松的模式
    r'(?:判[决定]|认定)(?:.*?)(?:犯|构成|涉嫌)([\u4e00-\u9fa5]+罪)',
    r'(?:罪名认定|罪名)(?:.*?)(?:犯|构成|涉嫌)([\u4e00-\u9fa5]+罪)',
    r'(?:罪名认定|罪名)(?:.*?)([\u4e00-\u9fa5]+罪)',

    # 直接提取罪名
    r'([\u4e00-\u9fa5]{2,10}罪)(?:，|。|；)',

    # 编码问题的模式
    r'鍒[鍐喅]琚憡浜?[\u4e00-\u9fa5\w]+鐘?([\u4e00-\u9fa5]+缃?)',
    r'璁ゅ畾琚憡浜?[\u4e00-\u9fa5\w]+鐘?([\u4e00-\u9fa5]+缃?)',
    r'鏋勬垚([\u4e00-\u9fa5]+缃?)',
    r'鐘?([\u4e00-\u9fa5]+缃?)'
]

# 定义刑期提取模式常量
IMPRISONMENT_PATTERNS = [
    # 判决结果部分的模式 - 优先级最高
    (r'判[决定](?:.*?)有期徒刑(\d+)年(\d+)(?:个)?月',
     lambda m: int(m.group(1)) * 12 + int(m.group(2))),
    (r'判[决定](?:.*?)有期徒刑(\d+)年',
     lambda m: int(m.group(1)) * 12),
    (r'判[决定](?:.*?)有期徒刑(\d+)(?:个)?月',
     lambda m: int(m.group(1))),

    # 处理带有"涉嫌"等前缀的情况
    (r'判[决定](?:.*?)(?:涉嫌|被控|犯)(?:.*?)有期徒刑(\d+)年(\d+)(?:个)?月',
     lambda m: int(m.group(1)) * 12 + int(m.group(2))),
    (r'判[决定](?:.*?)(?:涉嫌|被控|犯)(?:.*?)有期徒刑(\d+)年',
     lambda m: int(m.group(1)) * 12),
    (r'判[决定](?:.*?)(?:涉嫌|被控|犯)(?:.*?)有期徒刑(\d+)(?:个)?月',
     lambda m: int(m.group(1))),

    # 判决结果部分的中文数字模式 - 扩展支持更多中文数字组合
    (r'判[决定](?:.*?)有期徒刑([零一二三四五六七八九十百千万亿两]+)年([零一二三四五六七八九十百千万亿两]+)(?:个)?月',
     lambda m: convert_cn_num(m.group(1)) * 12 + convert_cn_num(m.group(2))),
    (r'判[决定](?:.*?)有期徒刑([零一二三四五六七八九十百千万亿两]+)年',
     lambda m: convert_cn_num(m.group(1)) * 12),
    (r'判[决定](?:.*?)有期徒刑([零一二三四五六七八九十百千万亿两]+)(?:个)?月',
     lambda m: convert_cn_num(m.group(1))),

    # 阿拉伯数字年月模式 - 更通用
    (r'(?:判处|决定|判令|宣告|处以)(?:.*?)有期徒刑(\d+)年(\d+)(?:个)?月',
     lambda m: int(m.group(1)) * 12 + int(m.group(2))),
    (r'(?:判处|决定|判令|宣告|处以)(?:.*?)有期徒刑(\d+)年',
     lambda m: int(m.group(1)) * 12),
    (r'(?:判处|决定|判令|宣告|处以)(?:.*?)有期徒刑(\d+)(?:个)?月',
     lambda m: int(m.group(1))),

    # 拘役和管制模式
    (r'(?:判处|决定|判令|宣告|处以)(?:.*?)拘役(\d+)(?:个)?月', lambda m: int(m.group(1))),
    (r'(?:判处|决定|判令|宣告|处以)(?:.*?)管制(\d+)(?:个)?月', lambda m: int(m.group(1))),
    (r'(?:判处|决定|判令|宣告|处以)(?:.*?)拘役([零一二三四五六七八九十百千万亿两]+)(?:个)?月',
     lambda m: convert_cn_num(m.group(1))),
    (r'(?:判处|决定|判令|宣告|处以)(?:.*?)管制([零一二三四五六七八九十百千万亿两]+)(?:个)?月',
     lambda m: convert_cn_num(m.group(1))),

    # 中文数字年月模式 - 更通用
    (r'(?:判处|决定|判令|宣告|处以)(?:.*?)有期徒刑([零一二三四五六七八九十百千万亿两]+)年([零一二三四五六七八九十百千万亿两]+)(?:个)?月',
     lambda m: convert_cn_num(m.group(1)) * 12 + convert_cn_num(m.group(2))),
    (r'(?:判处|决定|判令|宣告|处以)(?:.*?)有期徒刑([零一二三四五六七八九十百千万亿两]+)年',
     lambda m: convert_cn_num(m.group(1)) * 12),
    (r'(?:判处|决定|判令|宣告|处以)(?:.*?)有期徒刑([零一二三四五六七八九十百千万亿两]+)(?:个)?月',
     lambda m: convert_cn_num(m.group(1))),

    # 更通用的模式 - 不依赖特定前缀
    (r'有期徒刑(\d+)年(\d+)(?:个)?月', lambda m: int(m.group(1)) * 12 + int(m.group(2))),
    (r'有期徒刑(\d+)年', lambda m: int(m.group(1)) * 12),
    (r'有期徒刑(\d+)(?:个)?月', lambda m: int(m.group(1))),
    (r'有期徒刑([零一二三四五六七八九十百千万亿两]+)年([零一二三四五六七八九十百千万亿两]+)(?:个)?月',
     lambda m: convert_cn_num(m.group(1)) * 12 + convert_cn_num(m.group(2))),
    (r'有期徒刑([零一二三四五六七八九十百千万亿两]+)年',
     lambda m: convert_cn_num(m.group(1)) * 12),
    (r'有期徒刑([零一二三四五六七八九十百千万亿两]+)(?:个)?月',
     lambda m: convert_cn_num(m.group(1))),

    # 刑期直接表述模式
    (r'刑期(?:为|是)?(\d+)年(\d+)(?:个)?月', lambda m: int(m.group(1)) * 12 + int(m.group(2))),
    (r'刑期(?:为|是)?(\d+)年', lambda m: int(m.group(1)) * 12),
    (r'刑期(?:为|是)?(\d+)(?:个)?月', lambda m: int(m.group(1))),
    (r'刑期(?:为|是)?([零一二三四五六七八九十百千万亿两]+)年([零一二三四五六七八九十百千万亿两]+)(?:个)?月',
     lambda m: convert_cn_num(m.group(1)) * 12 + convert_cn_num(m.group(2))),
    (r'刑期(?:为|是)?([零一二三四五六七八九十百千万亿两]+)年',
     lambda m: convert_cn_num(m.group(1)) * 12),
    (r'刑期(?:为|是)?([零一二三四五六七八九十百千万亿两]+)(?:个)?月',
     lambda m: convert_cn_num(m.group(1))),

    # 量刑建议模式
    (r'(?:建议|应当|应该|可以|可处|处于)(?:判处|判决|量刑)(?:.*?)(\d+)年(\d+)(?:个)?月',
     lambda m: int(m.group(1)) * 12 + int(m.group(2))),
    (r'(?:建议|应当|应该|可以|可处|处于)(?:判处|判决|量刑)(?:.*?)(\d+)年',
     lambda m: int(m.group(1)) * 12),
    (r'(?:建议|应当|应该|可以|可处|处于)(?:判处|判决|量刑)(?:.*?)(\d+)(?:个)?月',
     lambda m: int(m.group(1))),
    (r'(?:建议|应当|应该|可以|可处|处于)(?:判处|判决|量刑)(?:.*?)([零一二三四五六七八九十百千万亿两]+)年([零一二三四五六七八九十百千万亿两]+)(?:个)?月',
     lambda m: convert_cn_num(m.group(1)) * 12 + convert_cn_num(m.group(2))),
    (r'(?:建议|应当|应该|可以|可处|处于)(?:判处|判决|量刑)(?:.*?)([零一二三四五六七八九十百千万亿两]+)年',
     lambda m: convert_cn_num(m.group(1)) * 12),
    (r'(?:建议|应当|应该|可以|可处|处于)(?:判处|判决|量刑)(?:.*?)([零一二三四五六七八九十百千万亿两]+)(?:个)?月',
     lambda m: convert_cn_num(m.group(1))),

    # 最终刑期表述模式
    (r'最终刑期(?:为|是)?(\d+)年(\d+)(?:个)?月', lambda m: int(m.group(1)) * 12 + int(m.group(2))),
    (r'最终刑期(?:为|是)?(\d+)年', lambda m: int(m.group(1)) * 12),
    (r'最终刑期(?:为|是)?(\d+)(?:个)?月', lambda m: int(m.group(1))),
    (r'最终刑期(?:为|是)?([零一二三四五六七八九十百千万亿两]+)年([零一二三四五六七八九十百千万亿两]+)(?:个)?月',
     lambda m: convert_cn_num(m.group(1)) * 12 + convert_cn_num(m.group(2))),
    (r'最终刑期(?:为|是)?([零一二三四五六七八九十百千万亿两]+)年',
     lambda m: convert_cn_num(m.group(1)) * 12),
    (r'最终刑期(?:为|是)?([零一二三四五六七八九十百千万亿两]+)(?:个)?月',
     lambda m: convert_cn_num(m.group(1))),

    # 最宽松的模式 - 用于捕获其他可能的表述
    (r'(?:徒刑|刑期|判处|判决|处罚|处刑)(?:.*?)(\d+)年(\d+)(?:个)?月',
     lambda m: int(m.group(1)) * 12 + int(m.group(2))),
    (r'(?:徒刑|刑期|判处|判决|处罚|处刑)(?:.*?)(\d+)年',
     lambda m: int(m.group(1)) * 12),
    (r'(?:徒刑|刑期|判处|判决|处罚|处刑)(?:.*?)(\d+)(?:个)?月',
     lambda m: int(m.group(1))),
    (r'(?:徒刑|刑期|判处|判决|处罚|处刑)(?:.*?)([零一二三四五六七八九十百千万亿两]+)年([零一二三四五六七八九十百千万亿两]+)(?:个)?月',
     lambda m: convert_cn_num(m.group(1)) * 12 + convert_cn_num(m.group(2))),
    (r'(?:徒刑|刑期|判处|判决|处罚|处刑)(?:.*?)([零一二三四五六七八九十百千万亿两]+)年',
     lambda m: convert_cn_num(m.group(1)) * 12),
    (r'(?:徒刑|刑期|判处|判决|处罚|处刑)(?:.*?)([零一二三四五六七八九十百千万亿两]+)(?:个)?月',
     lambda m: convert_cn_num(m.group(1)))
]

# 定义判决结果部分提取模式常量
RESULT_SECTION_PATTERNS = [
    r'判决结果[:：]?([\s\S]+?)(?:$|8\.)',
    r'7\.\s*判决结果[:：]?([\s\S]+?)(?:$|8\.)',
    r'七、\s*判决结果[:：]?([\s\S]+?)(?:$|八、)',
    # 添加编码问题的模式
    r'鍒ゅ喅缁撴灉[:：]?([\s\S]+?)(?:$|8\.)',
    r'7\.\s*鍒ゅ喅缁撴灉[:：]?([\s\S]+?)(?:$|8\.)'
]

# 定义量刑因素部分提取模式常量
SENTENCING_SECTION_PATTERNS = [
    r'量刑因素[:：]?([\s\S]+?)(?:判决结果|$)',
    r'6\.\s*量刑因素[:：]?([\s\S]+?)(?:判决结果|$)',
    r'六、\s*量刑因素[:：]?([\s\S]+?)(?:判决结果|$)'
]

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 设置 API 配置
API_URL = "https://chatapi.littlewheat.com/v1/chat/completions"
API_KEY = "sk-1PYK9cXL2KoDuuOKZ3PzeHf9HRxXmsrF6tLnetB4dej8bwoJ"
DEFAULT_MODEL = "gpt-4"  # 更改默认模型为 GPT-4

# 从外部文件加载专家角色映射和法律知识库
def load_json_data(file_path):
    """
    从JSON文件加载数据

    Args:
        file_path: JSON文件路径

    Returns:
        加载的数据字典
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        logging.info(f"Successfully loaded data from {file_path}")
        return data
    except FileNotFoundError:
        logging.error(f"JSON file not found: {file_path}")
        return {}
    except json.JSONDecodeError as e:
        logging.error(f"JSON decode error in file {file_path}: {e}")
        return {}
    except Exception as e:
        logging.error(f"Error loading JSON file {file_path}: {e}")
        logging.error(traceback.format_exc())
        return {}

# 加载专家角色映射和法律知识库
try:
    legal_role_map = load_json_data('legal_role_map.json')
    logging.info(f"Loaded {len(legal_role_map)} role mappings")
except Exception as e:
    logging.error(f"Error loading legal role map: {e}")
    # 提供默认映射作为备份
    legal_role_map = {
        "盗窃罪": "theft law expert",
        "诈骗罪": "fraud law expert",
        "抢劫罪": "robbery law expert",
        "敲诈勒索罪": "extortion law expert",
        "贪污罪": "corruption law expert",
        "故意伤害罪": "intentional injury law expert",
        "故意杀人罪": "homicide law expert",
        "诽谤罪": "defamation law expert"
    }

try:
    legal_knowledge_base = load_json_data('legal_knowledge_base.json')
    logging.info(f"Loaded {len(legal_knowledge_base)} legal knowledge entries")
except Exception as e:
    logging.error(f"Error loading legal knowledge base: {e}")
    # 提供默认知识库作为备份
    legal_knowledge_base = {
        "盗窃罪": {
            "法条": "《中华人民共和国刑法》第二百六十四条",
            "构成要件": "以非法占有为目的，秘密窃取公私财物，数额较大或者多次盗窃、入户盗窃、携带凶器盗窃、扒窃的行为。",
            "量刑标准": "数额较大的，处三年以下有期徒刑、拘役或者管制，并处或者单处罚金；数额巨大或者有其他严重情节的，处三年以上十年以下有期徒刑，并处罚金；数额特别巨大或者有其他特别严重情节的，处十年以上有期徒刑或者无期徒刑，并处罚金或者没收财产。",
            "从轻情节": "自首、认罪认罚、积极退赃、初犯偶犯、未造成实际损失",
            "从重情节": "累犯、惯犯、多次作案、入户盗窃、携带凶器盗窃、团伙作案"
        },
        "诈骗罪": {
            "法条": "《中华人民共和国刑法》第二百六十六条",
            "构成要件": "以非法占有为目的，用虚构事实或者隐瞒真相的方法，骗取数额较大的公私财物的行为。",
            "量刑标准": "数额较大的，处三年以下有期徒刑、拘役或者管制，并处或者单处罚金；数额巨大或者有其他严重情节的，处三年以上十年以下有期徒刑，并处罚金；数额特别巨大或者有其他特别严重情节的，处十年以上有期徒刑或者无期徒刑，并处罚金或者没收财产。",
            "从轻情节": "自首、认罪认罚、积极退赃、初犯偶犯、未造成实际损失",
            "从重情节": "累犯、惯犯、多次作案、冒充国家工作人员诈骗、以赈灾、救灾为名诈骗"
        }
    }

def load_jsonl_data(file_path, limit=None):
    """
    加载 JSONL 文件数据

    Args:
        file_path: JSONL文件路径
        limit: 限制加载的数据条数（可选）

    Returns:
        加载的数据列表
    """
    data = []
    try:
        with jsonlines.open(file_path) as reader:
            for i, obj in enumerate(reader):
                if limit and i >= limit:
                    break
                data.append(obj)
        logging.info(f"Successfully loaded {len(data)} records from {file_path}")
        return data
    except FileNotFoundError:
        logging.error(f"JSONL file not found: {file_path}")
        return []
    except json.JSONDecodeError as e:
        logging.error(f"JSON decode error in file {file_path}: {e}")
        return []
    except Exception as e:
        logging.error(f"Error loading JSONL file {file_path}: {e}")
        logging.error(traceback.format_exc())
        return []

def get_completion(prompt, role=None, max_tokens=4096, model=None, temperature=0):
    """
    获取 API 响应，并处理长文本

    使用requests库直接调用API，按照示例代码格式实现

    Args:
        prompt: 提示文本或指令
        role: 系统角色提示（可选）
        max_tokens: 最大生成token数
        model: 使用的模型名称，默认使用DEFAULT_MODEL (gpt-4)
        temperature: 生成多样性参数

    Returns:
        API响应文本
    """
    max_retries = 5
    max_content_length = 8000  # gpt-4 支持约 8k tokens 的上下文窗口

    # 如果未指定模型，使用默认模型
    if model is None:
        model = DEFAULT_MODEL

    # 如果是带角色的调用，处理指令和内容
    if role and isinstance(prompt, tuple) and len(prompt) == 2:
        instruction, content = prompt

        # 如果内容过长，进行截断处理
        if len(content) > max_content_length:
            logging.warning(f"Content too long ({len(content)} chars), truncating to ~{max_content_length} chars")
            content = content[:max_content_length] + "...[内容过长已截断]"

        # 准备消息
        messages = [
            {"role": "system", "content": f"You are a {role}."},
            {"role": "user", "content": f"{instruction}\n{content}"}
        ]
    else:
        # 如果是普通调用，直接使用prompt
        if isinstance(prompt, str) and len(prompt) > max_content_length:
            logging.warning(f"Prompt too long ({len(prompt)} chars), truncating to ~{max_content_length} chars")
            prompt = prompt[:max_content_length] + "...[内容过长已截断]"

        # 准备消息
        messages = [{"role": "user", "content": prompt}]

    # 准备请求数据 - 使用json.dumps格式化
    payload = json.dumps({
        "model": model,
        "messages": messages,
        "temperature": temperature,
        "max_tokens": max_tokens,
        "stream": False
    })

    # 准备请求头
    headers = {
        'Authorization': f'Bearer {API_KEY}',
        'Content-Type': 'application/json'
    }

    # 重试逻辑
    for i in range(max_retries):
        try:
            # 发送请求 - 使用示例代码中的方式
            response = requests.request("POST", API_URL, headers=headers, data=payload)

            # 检查响应状态
            response.raise_for_status()

            # 解析响应
            result = response.json()

            # 可以取消注释下面这行代码来查看API响应详情（调试用）
            # print("API响应详情:", json.dumps(result, indent=2, ensure_ascii=False))

            # 提取内容
            if 'choices' in result and len(result['choices']) > 0:
                if 'message' in result['choices'][0] and 'content' in result['choices'][0]['message']:
                    return result['choices'][0]['message']['content']

            # 如果无法提取内容，记录错误并返回错误信息
            logging.error(f"Unexpected API response format: {result}")
            return "API返回格式异常，无法提取响应内容。"

        except requests.exceptions.HTTPError as e:
            # 处理HTTP错误
            if response.status_code == 429:  # 速率限制
                wait_time = (i+1) * 5
                logging.warning(f"Rate limit error: {e}. Retrying in {wait_time} seconds...")
                time.sleep(wait_time)
            else:
                if i < max_retries - 1:
                    wait_time = (i+1) * 2
                    logging.warning(f"HTTP error: {e}. Retrying in {wait_time} seconds...")
                    time.sleep(wait_time)
                else:
                    logging.error(f'Max retries reached: {e}')
                    return f"API请求失败: {e}"

        except requests.exceptions.ConnectionError as e:
            # 处理连接错误
            wait_time = (i+1) * 3
            logging.warning(f"Connection error: {e}. Retrying in {wait_time} seconds...")
            time.sleep(wait_time)

        except json.JSONDecodeError as e:
            # 处理JSON解析错误
            logging.error(f"JSON decode error: {e}. Response text: {response.text}")
            if i < max_retries - 1:
                wait_time = (i+1) * 2
                logging.warning(f"Retrying in {wait_time} seconds...")
                time.sleep(wait_time)
            else:
                return f"API响应格式错误: {e}"

        except Exception as e:
            # 处理其他错误
            if i < max_retries - 1:
                wait_time = (i+1) * 2
                logging.warning(f"API error: {e}. Retrying in {wait_time} seconds...")
                time.sleep(wait_time)
            else:
                logging.error(f'Max retries reached: {e}')
                return f"由于API连接问题，无法获取完整分析: {e}"

# 为了保持向后兼容性，定义一个包装函数
def get_completion_with_role(role, instruction, content, max_tokens=4096):
    """使用特定角色获取 API 响应的包装函数"""
    return get_completion((instruction, content), role=role, max_tokens=max_tokens)

def get_legal_knowledge(accusation):
    """获取特定罪名的法律知识"""
    knowledge = legal_knowledge_base.get(accusation, {})
    if not knowledge:
        # 如果没有找到精确匹配，尝试模糊匹配
        for crime, info in legal_knowledge_base.items():
            if crime in accusation or accusation in crime:
                knowledge = info
                break

    if knowledge:
        knowledge_text = f"关于{accusation}的法律知识：\n"
        knowledge_text += f"法条依据：{knowledge.get('法条', '未知')}\n"
        knowledge_text += f"构成要件：{knowledge.get('构成要件', '未知')}\n"
        knowledge_text += f"量刑标准：{knowledge.get('量刑标准', '未知')}\n"
        knowledge_text += f"从轻情节：{knowledge.get('从轻情节', '未知')}\n"
        knowledge_text += f"从重情节：{knowledge.get('从重情节', '未知')}\n"
        return knowledge_text
    else:
        return f"未找到关于{accusation}的具体法律知识。请根据《中华人民共和国刑法》相关条款进行分析。"

# 此函数已移至utils.py模块

def evidence_weight_analysis(fact, clue_analysis_result=None):
    """
    执行证据权重分析与伪证识别，评估各证据的重要性、一致性和可靠性

    Args:
        fact: 案件事实文本
        clue_analysis_result: 线索分析结果（可选）

    Returns:
        证据权重分析结果
    """
    # 创建证据权重分析器
    analyzer = EvidenceWeightAnalyzer(get_completion)

    # 分析证据权重
    evidence_analysis_result = analyzer.analyze_evidence_weights(fact, clue_analysis_result)

    # 生成证据分析总结
    summary = analyzer.generate_evidence_analysis_summary(evidence_analysis_result)

    # 提取关键证据信息
    key_evidence_info = evidence_analysis_result.get("key_evidence", [])

    # 返回完整结果和总结
    return {
        "evidence_analysis_result": evidence_analysis_result,
        "key_evidence": key_evidence_info,
        "summary": summary
    }



def clue_analysis(fact):
    """
    线索分析专家分析案件事实

    Args:
        fact: 案件事实文本

    Returns:
        线索分析结果
    """
    # 提取关键要素
    key_elements = extract_key_elements(fact)
    elements_text = "\n".join([f"{k}：{', '.join(v) if isinstance(v, list) else v}" for k, v in key_elements.items() if v])

    instruction = f"""作为一名线索分析专家，请分析以下案件事实中的关键线索，包括时间、地点、人物、行为、动机等要素。
请详细说明这些线索如何构成案件的基本事实框架，以及它们之间的逻辑关系。只分析事实，不做法律判断。

我已经从案件中提取了以下关键要素，请在分析中重点关注这些要素：
{elements_text}

请按照以下结构进行分析：
1. 案件概述：简要概述案件的基本情况
2. 关键时间点分析：分析案件中的关键时间点及其意义
3. 关键地点分析：分析案件发生的地点及其重要性
4. 人物关系分析：分析案件中各人物之间的关系
5. 行为分析：分析案件中的关键行为及其因果关系
6. 动机分析：分析行为人可能的动机
7. 证据链分析：分析案件中的证据如何相互印证
8. 结论：总结案件的关键事实框架

请确保分析客观、全面、准确，不要遗漏重要线索，也不要添加案件中没有的事实。"""

    return get_completion((instruction, fact), role="forensic evidence analyst")

def legal_expert_analysis(fact, accusation):
    """
    法律专家分析案件事实与指控的关系

    Args:
        fact: 案件事实文本
        accusation: 指控罪名

    Returns:
        法律分析结果
    """
    # 获取相关法律知识
    legal_knowledge = get_legal_knowledge(accusation)

    # 获取专家角色，但直接在get_completion中使用固定角色
    expert_role = "senior criminal law expert with 30 years of experience in Chinese criminal law and judicial practice"

    instruction = f"""作为一名专精于{accusation}的资深法律专家，请严格分析以下案件事实是否符合{accusation}的构成要件。

{legal_knowledge}

【重要提示】
1. 请严格按照中国刑法和司法解释的规定进行分析，不要引入其他法域的概念
2. 罪名认定必须严格基于案件事实和法律规定，不要随意扩大或缩小罪名适用范围
3. 必须明确指出案件是否构成{accusation}，不要模糊表述
4. 如果案件事实不符合{accusation}的构成要件，请明确指出不构成该罪，不要勉强认定
5. 如果案件事实可能构成其他罪名，请在结论中明确指出

请按照以下结构进行分析：
1. 罪名概述：简要介绍{accusation}的法律定义和基本特征
2. 构成要件分析：
   a. 客观要件分析：详细分析案件事实中的行为是否符合该罪名的客观要件
   b. 主观要件分析：详细分析行为人的主观心态是否符合该罪名的主观要件
3. 法条适用分析：分析相关法条如何适用于本案
4. 量刑因素分析：分析可能影响量刑的因素，包括从重和从轻情节
5. 结论：明确指出案件事实是否构成{accusation}，并给出法律依据

【罪名认定要求】
1. 罪名认定必须使用明确的表述："构成{accusation}"或"不构成{accusation}"
2. 不要使用模糊表述，如"可能构成"、"倾向于构成"等
3. 如果证据不足以认定某罪名，应当明确指出"证据不足，不能认定构成{accusation}"
4. 如果案件事实符合多个罪名的构成要件，应当明确指出"构成数罪并罚"

请确保分析专业、准确、全面，严格依据法律条文和司法解释进行分析。"""

    return get_completion((instruction, fact), role=expert_role)

def defense_lawyer_analysis(fact, accusation):
    """
    辩护律师分析案件事实，寻找有利于被告的因素

    Args:
        fact: 案件事实文本
        accusation: 指控罪名

    Returns:
        辩护分析结果
    """
    # 获取相关法律知识
    legal_knowledge = get_legal_knowledge(accusation)

    instruction = f"""作为一名辩护律师，请分析以下案件事实中有利于被告的因素。针对{accusation}的指控，请找出可能的辩护理由。

{legal_knowledge}

请按照以下结构进行分析：
1. 案件事实质疑：质疑指控的案件事实是否清晰、确凿
2. 构成要件辩护：
   a. 客观要件辩护：分析被告行为是否不符合该罪名的客观要件
   b. 主观要件辩护：分析被告主观心态是否不符合该罪名的主观要件
3. 证据有效性辩护：质疑证据的合法性、关联性和证明力
4. 从轻处罚情节：分析可能导致从轻处罚的情节，如自首、立功、认罪认罚等
5. 替代性罪名建议：如果确有犯罪事实，是否可能构成较轻的罪名
6. 辩护结论：总结主要辩护观点和建议

请确保辩护观点合理、专业，基于案件事实和法律规定，不要捏造事实或曲解法律。"""

    return get_completion((instruction, fact), role="defense attorney")

def prosecutor_analysis(fact, accusation):
    """
    检察官分析案件事实，强调指控的合理性

    Args:
        fact: 案件事实文本
        accusation: 指控罪名

    Returns:
        检察官分析结果
    """
    # 获取相关法律知识
    legal_knowledge = get_legal_knowledge(accusation)

    instruction = f"""作为一名检察官，请分析以下案件事实如何支持对被告的{accusation}指控。

{legal_knowledge}

请按照以下结构进行分析：
1. 案件事实概述：简要概述指控的犯罪事实
2. 构成要件分析：
   a. 客观要件证明：详细分析证据如何证明被告行为符合该罪名的客观要件
   b. 主观要件证明：详细分析证据如何证明被告主观心态符合该罪名的主观要件
3. 证据链分析：分析各项证据如何相互印证，形成完整证据链
4. 排除合理怀疑：分析为何应排除可能的无罪解释
5. 从重处罚情节：分析可能导致从重处罚的情节
6. 量刑建议：基于案件事实和法律规定，提出具体的量刑建议
7. 指控结论：总结主要指控观点

请确保分析严谨、全面、有力，基于案件事实和法律规定，不要夸大事实或曲解法律。"""

    return get_completion((instruction, fact), role="prosecutor")

def sentencing_expert_analysis(fact, accusation):
    """
    量刑专家分析案件事实和法律适用，专注于刑期预测

    Args:
        fact: 案件事实文本
        accusation: 指控罪名

    Returns:
        量刑专家分析结果
    """
    # 获取相关法律知识
    legal_knowledge = get_legal_knowledge(accusation)

    # 提取关键要素
    key_elements = extract_key_elements(fact)
    elements_text = "\n".join([f"{k}：{', '.join(v) if isinstance(v, list) else v}" for k, v in key_elements.items() if v])

    # 设置刑期范围
    severity_ranges = {
        "轻微": (6, 36, 12),  # 最低6个月，最高3年，标准1年
        "一般": (36, 84, 60),  # 最低3年，最高7年，标准5年
        "严重": (84, 180, 120),  # 最低7年，最高15年，标准10年
        "特别严重": (180, 360, 240)  # 最低15年，最高30年，标准20年
    }

    # 使用通用的方法获取标准刑期，不再硬编码特定罪名的刑期
    # 首先尝试从法律知识库中获取刑期信息
    legal_info = get_legal_knowledge(accusation)
    standard_imprisonment = None

    # 尝试从法律知识中提取刑期信息
    imprisonment_match = re.search(r'(\d+)个月|(\d+)年', legal_info)
    if imprisonment_match:
        # 如果找到了"X个月"的表述
        if imprisonment_match.group(1):
            standard_imprisonment = int(imprisonment_match.group(1))
        # 如果找到了"X年"的表述
        elif imprisonment_match.group(2):
            standard_imprisonment = int(imprisonment_match.group(2)) * 12

    # 如果无法从法律知识中提取，使用通用分类方法
    if standard_imprisonment is None:
        # 基于罪名关键词确定严重程度
        severity_level = "一般"  # 默认严重程度

        # 使用更通用的方法确定严重程度
        if any(keyword in accusation for keyword in ["轻微", "侮辱", "诽谤", "隐私", "偷窥", "侵犯"]):
            severity_level = "轻微"
        elif any(keyword in accusation for keyword in ["严重", "重大", "恶劣", "贪污", "贿赂", "挪用公款", "走私", "制造毒品"]):
            severity_level = "严重"
        elif any(keyword in accusation for keyword in ["特别严重", "故意杀人", "绑架", "爆炸", "放火", "投毒"]):
            severity_level = "特别严重"

        # 获取对应严重程度的刑期范围
        min_imprisonment, max_imprisonment, standard_imprisonment = severity_ranges.get(severity_level)
    else:
        # 如果成功获取了标准刑期，计算合理的最小和最大刑期
        min_imprisonment = max(1, int(standard_imprisonment * 0.5))  # 最低为标准刑期的50%
        max_imprisonment = min(360, int(standard_imprisonment * 2.0))  # 最高为标准刑期的200%

    instruction = f"""作为一名资深的量刑专家，请分析以下案件事实，专注于刑期预测。你的任务是根据案件事实和法律规定，给出精确的刑期预测。

案件涉及的罪名：{accusation}

案件关键要素：
{elements_text}

适用法律知识：
{legal_knowledge}

【{accusation}的刑期标准】
根据相关法律和司法解释，{accusation}的标准刑期为{standard_imprisonment}个月。
请务必将{standard_imprisonment}个月作为基准刑期，然后根据案件具体情况进行适当调整。

【刑期参考信息】
1. {accusation}的标准刑期：{standard_imprisonment}个月
2. {accusation}的法定刑期范围：最低{min_imprisonment}个月，最高{max_imprisonment}个月
3. 近年来类似案件的平均刑期：{int(standard_imprisonment * 0.9)}至{int(standard_imprisonment * 1.1)}个月

请按照以下结构进行分析：
1. 案件概述：简要概述案件的基本情况，重点关注与量刑相关的事实
2. 量刑因素分析：
   a. 加重因素：详细分析可能导致加重处罚的情节，并引用相关法律条文和司法解释
   b. 减轻因素：详细分析可能导致减轻处罚的情节，并引用相关法律条文和司法解释
3. 量刑计算：
   a. 基准刑期：{standard_imprisonment}个月
   b. 加重因素调整：分析每个加重因素对刑期的具体影响（精确到月）
   c. 减轻因素调整：分析每个减轻因素对刑期的具体影响（精确到月）
   d. 最终刑期计算：详细说明如何得出最终刑期，包括具体的计算过程
4. 刑期建议：给出明确的刑期建议（精确到月），并详细说明理由
5. 类似案例参考：引用2-3个类似案例的判决结果，支持你的刑期建议

【量刑因素详细指导】
请在量刑时详细考虑以下因素，并明确说明每个因素如何影响最终刑期：

1. 案件情节严重程度：
   - 轻微情节：减轻20-30%的刑期
   - 一般情节：维持标准刑期
   - 严重情节：加重20-30%的刑期
   - 特别严重情节：加重30-50%的刑期

2. 行为人主观方面：
   - 过失行为：减轻30-50%的刑期
   - 间接故意：维持标准刑期
   - 直接故意：加重10-20%的刑期
   - 预谋行为：加重20-40%的刑期

3. 行为人态度：
   - 自首并积极认罪：减轻30-40%的刑期
   - 如实供述并认罪：减轻20-30%的刑期
   - 部分认罪：减轻10-20%的刑期
   - 拒不认罪：维持标准刑期

4. 行为人前科情况：
   - 无前科：减轻10-20%的刑期
   - 有轻微前科：维持标准刑期
   - 有类似前科：加重20-30%的刑期
   - 累犯：加重30-50%的刑期

5. 造成的实际后果：
   - 未造成实际损失：减轻30-50%的刑期
   - 轻微损失：减轻10-30%的刑期
   - 重大损失：加重20-40%的刑期
   - 特别严重损失：加重40-60%的刑期

6. 共同犯罪中的地位和作用：
   - 从犯：减轻20-40%的刑期
   - 胁从犯：减轻40-60%的刑期
   - 主犯：维持或加重10-30%的刑期
   - 首要分子：加重30-50%的刑期

7. 犯罪中止或未遂情况：
   - 犯罪中止：减轻40-70%的刑期
   - 犯罪未遂：减轻30-50%的刑期

【通用量刑考虑因素】
根据《中华人民共和国刑法》和相关司法解释，请特别注意以下通用量刑因素：

1. 犯罪行为的性质和情节：
   - 犯罪手段的残忍程度
   - 犯罪行为的持续时间
   - 犯罪行为的范围和规模
   - 犯罪行为的社会危害性

2. 犯罪结果的严重程度：
   - 造成的人身伤害程度
   - 造成的财产损失金额
   - 对社会秩序的破坏程度
   - 对公共安全的危害程度

3. 犯罪主观方面：
   - 犯罪动机和目的
   - 主观恶性的大小
   - 犯罪预谋的程度
   - 犯罪时的心理状态

4. 犯罪后的表现：
   - 是否自首或投案
   - 是否如实供述犯罪事实
   - 是否积极退赃、赔偿损失
   - 是否有悔罪表现
   - 是否有立功表现

5. 犯罪人的个人情况：
   - 前科情况
   - 年龄和健康状况
   - 一贯表现
   - 家庭和社会关系

6. 法定和酌定量刑情节：
   - 法定从重情节（如累犯、主犯等）
   - 法定从轻情节（如未遂、从犯等）
   - 酌定从重情节（如犯罪后逃避等）
   - 酌定从轻情节（如初犯偶犯等）

【量刑调整参考】
根据上述因素，可以对基准刑期进行如下调整：

- 情节较轻：减轻20-40%的刑期（约{int(standard_imprisonment * 0.7)}个月左右）
- 情节一般：维持基准刑期（{standard_imprisonment}个月）
- 情节较重：加重20-40%的刑期（约{int(standard_imprisonment * 1.3)}个月左右）
- 情节特别严重：加重40-80%的刑期（约{int(standard_imprisonment * 1.6)}个月左右）

【{accusation}的关键考量点】
根据法律规定和司法实践，对于{accusation}案件，请特别关注以下方面：
- 犯罪行为的具体表现形式
- 犯罪行为造成的实际危害后果
- 犯罪人的主观恶性大小
- 犯罪人的认罪悔罪态度
- 是否有法定或酌定的从重、从轻情节

【量刑精确性要求】
1. 请确保最终刑期建议精确到月，不要使用模糊表述
2. 刑期必须在法定刑期范围内，不得超出最高或最低限制
3. 刑期应当与案件事实和情节相匹配，避免明显不合理的结果
4. 对于每个量刑因素的影响，请给出具体的月数调整，而不是笼统的百分比
5. 最终刑期应当是一个具体的数字，而不是一个范围

请确保分析客观、公正、专业，严格基于案件事实和法律规定。
特别注意：请在结论中明确指出建议的具体刑期（以月为单位），这对后续判决至关重要。"""

    # 使用更具体的角色提示
    return get_completion((instruction, fact), role="senior sentencing expert with 25 years of experience in criminal law and judicial sentencing")

def debate_analysis(fact, accusation, legal_response, defense_response, prosecutor_response, sentencing_response=None):
    """
    增强版辩论分析，综合各方观点，减少幻觉，提高准确性

    实现了更结构化的辩论流程，包括多轮交叉质询和反驳，以及最终的共识形成

    Args:
        fact: 案件事实文本
        accusation: 指控罪名
        legal_response: 法律专家分析结果
        defense_response: 辩护律师分析结果
        prosecutor_response: 检察官分析结果
        sentencing_response: 量刑专家分析结果（可选）

    Returns:
        辩论分析结果，包含各方观点的交叉验证和最终共识
    """
    # 获取相关法律知识
    legal_knowledge = get_legal_knowledge(accusation)

    # 提取案件关键要素
    key_elements = extract_key_elements(fact)
    elements_text = "\n".join([f"{k}：{', '.join(v) if isinstance(v, list) else v}" for k, v in key_elements.items() if v])

    # 获取罪名的标准刑期范围
    # 使用通用的方法确定严重程度，不针对特定罪名
    severity_level = "一般"  # 默认严重程度

    # 基于罪名关键词确定严重程度，使用更通用的分类方法
    if any(keyword in accusation for keyword in ["轻微", "侮辱", "诽谤", "隐私", "偷窥", "侵犯"]):
        severity_level = "轻微"
    elif any(keyword in accusation for keyword in ["严重", "重大", "恶劣", "贪污", "贿赂", "挪用", "走私", "毒品"]):
        severity_level = "严重"
    elif any(keyword in accusation for keyword in ["特别严重", "故意杀人", "绑架", "爆炸", "放火", "投毒"]):
        severity_level = "特别严重"

    # 基于严重程度估计刑期
    severity_ranges = {
        "轻微": (6, 36, 18),  # 最低6个月，最高3年，标准18个月
        "一般": (36, 84, 60),  # 最低3年，最高7年，标准5年
        "严重": (84, 180, 120),  # 最低7年，最高15年，标准10年
        "特别严重": (180, 360, 240)  # 最低15年，最高30年，标准20年
    }

    min_imprisonment, max_imprisonment, standard_imprisonment = severity_ranges.get(severity_level)

    # 构建增强版辩论指令，实现更结构化的辩论流程
    if sentencing_response:
        instruction = f"""作为一名资深法律辩论主持人和量刑专家，请组织一场关于{accusation}案件的结构化法律辩论，并通过多轮交叉质询和反驳，找出事实真相和法律共识。

案件涉及的罪名：{accusation}

案件关键要素：
{elements_text}

适用法律知识：
{legal_knowledge}

请按照以下结构化辩论流程进行分析：

## 第一阶段：案件事实和证据分析

1. 案件关键事实提取：
   - 提取案件中的关键时间、地点、人物、行为和动机
   - 识别案件中的争议事实和无争议事实
   - 分析证据链的完整性和可靠性

2. 各方对事实认定的分歧：
   - 法律专家认定的关键事实
   - 辩护方强调的有利事实
   - 控方强调的不利事实
   - 量刑专家关注的量刑事实
   - 对事实认定分歧的交叉质询和辩论

## 第二阶段：法律适用辩论

3. {accusation}的构成要件分析：
   - 客观要件：行为、对象、结果、因果关系
   - 主观要件：故意、过失、目的、动机
   - 各方对构成要件的不同解读
   - 案件事实与构成要件的对应关系

4. 法律适用争议点：
   - 辩护方提出的法律适用异议
   - 控方对法律适用的坚持
   - 法律专家对争议的分析
   - 对法律适用争议的交叉质询和辩论

## 第三阶段：量刑因素辩论

5. 量刑情节分析：
   - 法定从重情节分析
   - 法定从轻情节分析
   - 酌定从重情节分析
   - 酌定从轻情节分析

6. 量刑建议辩论：
   - 控方的量刑建议及理由
   - 辩护方的量刑建议及理由
   - 量刑专家的专业意见
   - 对量刑建议的交叉质询和辩论

## 第四阶段：辩论总结与共识形成

7. 事实认定共识：
   - 各方已达成共识的事实
   - 经辩论后形成的事实认定
   - 仍存在争议的事实及其影响

8. 法律适用共识：
   - 各方已达成共识的法律适用
   - 经辩论后形成的法律适用意见
   - 仍存在争议的法律适用问题及其影响

9. 量刑共识：
   - 各方已达成共识的量刑因素
   - 经辩论后形成的量刑建议
   - 量刑建议的合理区间及理由

## 第五阶段：最终法律意见

10. 综合法律分析：
    - 基于事实认定、法律适用和量刑因素的综合分析
    - 对各方观点的客观评价
    - 基于法律规定和司法实践的专业判断

11. 最终法律意见：
    - 明确指出被告人是否构成{accusation}，并详细说明理由
    - 提出具体的量刑建议，包括确切的刑期范围（以月为单位）
    - 分析该量刑建议的法律依据和合理性

【刑期参考信息】
根据《中华人民共和国刑法》和司法实践，{accusation}的刑期范围通常为：
- 法定最低刑期：{min_imprisonment}个月
- 法定最高刑期：{max_imprisonment}个月
- 标准参考刑期：{standard_imprisonment}个月

请确保辩论过程客观、公正、专业，不偏向任何一方，严格基于案件事实和法律规定。特别注意识别和纠正各方观点中可能存在的事实错误、法律误用或逻辑谬误，以减少幻觉和提高准确性。

在最终法律意见中，必须明确指出罪名认定和具体的刑期建议（以月为单位），这对后续判决至关重要。刑期建议应当考虑案件的具体情节、被告人的主观恶性、认罪态度、前科情况以及造成的实际危害后果等因素。"""

        # 构建更全面的辩论上下文，包含所有专家意见
        debate_context = f"""案件事实：
{fact}

法律专家分析：
{legal_response}

辩护律师分析：
{defense_response}

检察官分析：
{prosecutor_response}

量刑专家分析：
{sentencing_response}

【辩论要求】
1. 请严格按照辩论流程进行分析，确保每个阶段都得到充分讨论
2. 对各方提出的观点进行交叉质询，找出其中的事实错误、法律误用或逻辑谬误
3. 特别关注各方观点中的矛盾之处，通过辩论澄清事实
4. 在形成最终意见时，必须基于充分的事实和法律依据，而非简单采纳某一方观点
5. 最终法律意见必须包含明确的罪名认定和具体的刑期建议（以月为单位）
6. 如果发现任何专家观点中存在明显的幻觉或与案件事实不符的内容，请明确指出并纠正"""
    else:
        instruction = f"""作为一名资深法律辩论主持人和量刑专家，请组织一场关于{accusation}案件的结构化法律辩论，并通过多轮交叉质询和反驳，找出事实真相和法律共识。

案件涉及的罪名：{accusation}

案件关键要素：
{elements_text}

适用法律知识：
{legal_knowledge}

请按照以下结构化辩论流程进行分析：

## 第一阶段：案件事实和证据分析

1. 案件关键事实提取：
   - 提取案件中的关键时间、地点、人物、行为和动机
   - 识别案件中的争议事实和无争议事实
   - 分析证据链的完整性和可靠性

2. 各方对事实认定的分歧：
   - 法律专家认定的关键事实
   - 辩护方强调的有利事实
   - 控方强调的不利事实
   - 对事实认定分歧的交叉质询和辩论

## 第二阶段：法律适用辩论

3. {accusation}的构成要件分析：
   - 客观要件：行为、对象、结果、因果关系
   - 主观要件：故意、过失、目的、动机
   - 各方对构成要件的不同解读
   - 案件事实与构成要件的对应关系

4. 法律适用争议点：
   - 辩护方提出的法律适用异议
   - 控方对法律适用的坚持
   - 法律专家对争议的分析
   - 对法律适用争议的交叉质询和辩论

## 第三阶段：量刑因素辩论

5. 量刑情节分析：
   - 法定从重情节分析
   - 法定从轻情节分析
   - 酌定从重情节分析
   - 酌定从轻情节分析

6. 量刑建议辩论：
   - 控方的量刑建议及理由
   - 辩护方的量刑建议及理由
   - 对量刑建议的交叉质询和辩论

## 第四阶段：辩论总结与共识形成

7. 事实认定共识：
   - 各方已达成共识的事实
   - 经辩论后形成的事实认定
   - 仍存在争议的事实及其影响

8. 法律适用共识：
   - 各方已达成共识的法律适用
   - 经辩论后形成的法律适用意见
   - 仍存在争议的法律适用问题及其影响

9. 量刑共识：
   - 各方已达成共识的量刑因素
   - 经辩论后形成的量刑建议
   - 量刑建议的合理区间及理由

## 第五阶段：最终法律意见

10. 综合法律分析：
    - 基于事实认定、法律适用和量刑因素的综合分析
    - 对各方观点的客观评价
    - 基于法律规定和司法实践的专业判断

11. 最终法律意见：
    - 明确指出被告人是否构成{accusation}，并详细说明理由
    - 提出具体的量刑建议，包括确切的刑期范围（以月为单位）
    - 分析该量刑建议的法律依据和合理性

【刑期参考信息】
根据《中华人民共和国刑法》和司法实践，{accusation}的刑期范围通常为：
- 法定最低刑期：{min_imprisonment}个月
- 法定最高刑期：{max_imprisonment}个月
- 标准参考刑期：{standard_imprisonment}个月

请确保辩论过程客观、公正、专业，不偏向任何一方，严格基于案件事实和法律规定。特别注意识别和纠正各方观点中可能存在的事实错误、法律误用或逻辑谬误，以减少幻觉和提高准确性。

在最终法律意见中，必须明确指出罪名认定和具体的刑期建议（以月为单位），这对后续判决至关重要。刑期建议应当考虑案件的具体情节、被告人的主观恶性、认罪态度、前科情况以及造成的实际危害后果等因素。"""

        # 构建更全面的辩论上下文
        debate_context = f"""案件事实：
{fact}

法律专家分析：
{legal_response}

辩护律师分析：
{defense_response}

检察官分析：
{prosecutor_response}

【辩论要求】
1. 请严格按照辩论流程进行分析，确保每个阶段都得到充分讨论
2. 对各方提出的观点进行交叉质询，找出其中的事实错误、法律误用或逻辑谬误
3. 特别关注各方观点中的矛盾之处，通过辩论澄清事实
4. 在形成最终意见时，必须基于充分的事实和法律依据，而非简单采纳某一方观点
5. 最终法律意见必须包含明确的罪名认定和具体的刑期建议（以月为单位）
6. 如果发现任何专家观点中存在明显的幻觉或与案件事实不符的内容，请明确指出并纠正"""

    # 使用更具体的角色提示，强调辩论主持和交叉验证能力
    return get_completion(
        (instruction, debate_context),
        role="senior legal debate moderator with 25 years of experience in criminal law, specialized in cross-examination and fact verification, known for identifying factual inconsistencies and legal misapplications, and with expertise in resolving complex legal disputes through structured debate"
    )

def judge_decision(fact, clue_response, legal_response, defense_response, prosecutor_response, accusation, config=None, sentencing_response=None, evidence_analysis_response=None):
    """
    法官根据各方分析做出最终判决

    Args:
        fact: 案件事实文本
        clue_response: 线索分析专家分析结果
        legal_response: 法律专家分析结果
        defense_response: 辩护律师分析结果
        prosecutor_response: 检察官分析结果
        accusation: 指控罪名
        config: 配置参数（可选）
        sentencing_response: 量刑专家分析结果（可选）
        cea_response: 反事实证据分析结果（可选）

    Returns:
        法官判决结果
    """
    # 获取相关法律知识
    legal_knowledge = get_legal_knowledge(accusation)

    # 进行多方辩论分析，减少幻觉
    if config and config.get("disable_debate"):
        debate_result = "辩论机制已禁用"
    else:
        debate_result = debate_analysis(fact, accusation, legal_response, defense_response, prosecutor_response, sentencing_response)

    # 提取关键要素
    key_elements = extract_key_elements(fact)
    elements_text = "\n".join([f"{k}：{', '.join(v) if isinstance(v, list) else v}" for k, v in key_elements.items() if v])

    # 获取参考刑期信息 - 使用通用的方法，不针对特定罪名
    # 设置默认刑期
    original_imprisonment = None

    # 使用更通用的方法设置默认刑期，不针对特定罪名
    # 从法律知识库中获取刑期信息
    legal_info = get_legal_knowledge(accusation)

    # 尝试从法律知识中提取刑期信息
    imprisonment_match = re.search(r'(\d+)个月', legal_info)
    if imprisonment_match:
        original_imprisonment = int(imprisonment_match.group(1))
    else:
        # 如果无法从法律知识中提取，使用通用的中等刑期
        original_imprisonment = 36

    # 设置刑期范围 - 使用通用的计算方法
    min_imprisonment = max(6, int(original_imprisonment * 0.5))  # 最低不少于6个月
    max_imprisonment = min(360, int(original_imprisonment * 2))  # 最高不超过30年

    # 根据刑期范围生成严重程度级别
    severity_range = max_imprisonment - min_imprisonment
    step = severity_range / 4  # 将范围分为4个级别

    severity_levels = {
        "轻微": (min_imprisonment, min_imprisonment + step),
        "一般": (min_imprisonment + step, min_imprisonment + 2 * step),
        "严重": (min_imprisonment + 2 * step, min_imprisonment + 3 * step),
        "特别严重": (min_imprisonment + 3 * step, max_imprisonment)
    }

    # 智能处理长文本，确保重要信息不被截断
    max_fact_length = 4000  # 设置案件事实的最大长度，适合 gpt-3.5-turbo-16k
    max_response_length = 2000  # 设置各种分析结果的最大长度，适合 gpt-3.5-turbo-16k

    # 智能截断案件事实，保留关键信息
    if len(fact) > max_fact_length:
        # 提取包含关键词的段落
        key_words = ["被告人", "犯罪事实", "指控", "证据", "判决", "法院认为",
                     "构成", "罪名", "量刑", "刑期", "有期徒刑", accusation]

        # 保留前部分
        first_part = fact[:max_fact_length//3]

        # 查找包含关键词的段落
        key_paragraphs = []
        for keyword in key_words:
            if keyword in fact:
                # 找到关键词所在的段落
                keyword_pos = fact.find(keyword)
                if keyword_pos > max_fact_length//3 and keyword_pos < len(fact) - max_fact_length//3:
                    start = max(max_fact_length//3, fact.rfind("\n", 0, keyword_pos) + 1)
                    end = min(len(fact) - max_fact_length//3, fact.find("\n", keyword_pos))
                    if end == -1:
                        end = len(fact) - max_fact_length//3
                    if start < end and end - start < 500:  # 限制段落长度
                        key_paragraphs.append(fact[start:end])

        # 保留后部分
        last_part = fact[-max_fact_length//3:]

        # 组合处理后的文本
        middle_part = "\n\n".join(key_paragraphs[:5])  # 限制关键段落数量
        fact = first_part + "\n\n[...案件中间部分省略，以下是提取的关键信息...]\n\n" + middle_part + "\n\n[...案件结尾部分...]\n\n" + last_part

    # 智能截断分析结果，保留关键信息
    def smart_truncate(text, max_length, key_words):
        if text is None:
            return "无分析结果"

        if len(text) <= max_length:
            return text

        # 查找包含关键词的段落
        key_paragraphs = []
        for keyword in key_words:
            if keyword in text:
                # 找到关键词所在的段落
                keyword_pos = text.find(keyword)
                if keyword_pos > max_length//3 and keyword_pos < len(text) - max_length//3:
                    start = max(max_length//3, text.rfind("\n", 0, keyword_pos) + 1)
                    end = min(len(text) - max_length//3, text.find("\n", keyword_pos))
                    if end == -1:
                        end = len(text) - max_length//3
                    if start < end and end - start < 300:  # 限制段落长度
                        key_paragraphs.append(text[start:end])

        # 保留前部分和后部分
        first_part = text[:max_length//3]
        last_part = text[-max_length//3:]

        # 组合处理后的文本
        middle_part = "\n\n".join(key_paragraphs[:3])  # 限制关键段落数量
        return first_part + "\n\n[...内容中间部分省略，以下是提取的关键信息...]\n\n" + middle_part + "\n\n[...内容结尾部分...]\n\n" + last_part

    # 定义各种分析结果的关键词
    clue_keywords = ["关键证据", "关键线索", "重要信息", "关键要素", "关键点", accusation]
    legal_keywords = ["法律依据", "法条", "构成要件", "法律规定", "量刑标准", "刑期", accusation]
    defense_keywords = ["无罪", "从轻", "减轻", "辩护", "证据不足", "情节轻微", accusation]
    prosecutor_keywords = ["有罪", "从重", "加重", "指控", "证据充分", "情节严重", accusation]
    debate_keywords = ["争议", "分歧", "辩论", "结论", "共识", "刑期", accusation]

    # 确保所有分析结果都不为None
    if clue_response is None:
        clue_response = "线索分析专家未提供分析结果"
    if legal_response is None:
        legal_response = "法律专家未提供分析结果"
    if defense_response is None:
        defense_response = "辩护律师未提供分析结果"
    if prosecutor_response is None:
        prosecutor_response = "检察官未提供分析结果"
    if sentencing_response is None:
        sentencing_response = "量刑专家未提供分析结果"
    if debate_result is None:
        debate_result = "辩论分析未提供结果"

    # 定义量刑专家分析结果的关键词
    sentencing_keywords = ["量刑因素", "从重因素", "从轻因素", "刑期建议", "基准刑期", "最终刑期", accusation]

    # 智能截断各种分析结果
    clue_response = smart_truncate(clue_response, max_response_length, clue_keywords)
    legal_response = smart_truncate(legal_response, max_response_length, legal_keywords)
    defense_response = smart_truncate(defense_response, max_response_length, defense_keywords)
    prosecutor_response = smart_truncate(prosecutor_response, max_response_length, prosecutor_keywords)
    sentencing_response = smart_truncate(sentencing_response, max_response_length, sentencing_keywords)
    debate_result = smart_truncate(debate_result, max_response_length, debate_keywords)

    # 提取证据权重分析的关键信息
    evidence_analysis_summary = ""
    if evidence_analysis_response and isinstance(evidence_analysis_response, dict):
        evidence_analysis_summary = evidence_analysis_response.get("summary", "")

        # 如果有关键证据信息，添加到摘要中
        key_evidence = evidence_analysis_response.get("key_evidence", [])
        if key_evidence and not evidence_analysis_summary:
            evidence_analysis_summary = "证据权重分析发现以下关键证据对案件结论有重要影响：\n\n"
            for evidence in key_evidence[:3]:  # 最多显示3个关键证据
                evidence_analysis_summary += f"- {evidence.get('type', '证据')}: {evidence.get('content', '')[:100]}...\n"
                evidence_analysis_summary += f"  权重: {evidence.get('weight', 0):.2f}, 可靠性: {evidence.get('reliability', 0):.2f}\n"
                evidence_analysis_summary += f"  伪证概率: {evidence.get('false_probability', 0):.2f}\n"

    prompt = f"""作为一名经验丰富的最高人民法院法官，请根据以下信息对案件做出最终判决，特别注重罪名认定的准确性和刑期预测的精确性：

案件关键要素：
{elements_text}

适用法律知识：
{legal_knowledge}

【证据权重分析与伪证识别】（特别重要，请优先考虑）
{evidence_analysis_summary}

线索分析专家的分析：
{clue_response}

法律专家关于{accusation}的分析：
{legal_response}

辩护律师的分析：
{defense_response}

检察官的分析：
{prosecutor_response}

量刑专家的分析：
{sentencing_response}

法律辩论分析（用于减少可能的幻觉）：
{debate_result}

【证据评估指导 - 必须严格遵循】
1. 请特别关注证据权重分析结果，这是判决的重要基础
2. 对于被识别为"关键证据"的证据，应当给予更高的采信权重
3. 对于被识别为"潜在伪证"的证据，应当谨慎评估其可信度，特别是伪证概率高于0.5的证据
4. 证据一致性评分低于0.7的案件，应当更加谨慎地评估证据之间的矛盾点
5. 证据链完整性评分低于0.6的案件，应当考虑证据是否足以支持定罪

请按照以下结构做出判决：
1. 案件基本情况：简要概述案件的基本情况
2. 证据认定：认定哪些证据可以采信，哪些证据不予采信，以及理由
3. 事实认定：基于证据，认定案件的基本事实
4. 法律适用：详细分析适用的法律条文，以及如何适用于本案
5. 罪名认定：明确指出行为人是否构成{accusation}，并给出详细理由
6. 量刑因素：详细分析影响量刑的各种因素，包括加重和减轻情节
7. 判决结果：明确给出判决结果，包括罪名和具体刑期

请确保判决公正、客观、严谨，严格依据法律条文和司法解释，充分考虑案件的具体情况。

【罪名认定重要性 - 最高优先级】
准确的罪名认定是司法公正的基础。请严格按照中国刑法的规定，基于案件事实认定罪名。
罪名认定必须明确、准确，不得模糊表述。必须使用明确的表述："构成{accusation}"或"不构成{accusation}"。
如果案件事实不符合{accusation}的构成要件，请明确指出不构成该罪，不要勉强认定。
如果案件事实可能构成其他罪名，请在结论中明确指出。

【刑期预测重要性 - 请特别注意】
准确的刑期预测对于司法公正至关重要。请仔细分析案件情节，确保刑期预测与案件严重程度相匹配。
刑期预测是本次判决的核心任务，请务必给予足够重视。

【{accusation}的刑期标准 - 必须严格遵循】
根据相关法律和司法解释，{accusation}的标准刑期为{original_imprisonment}个月。
请务必将{original_imprisonment}个月作为基准刑期，然后根据案件具体情况进行适当调整。

【刑期参考信息 - 重要参考依据】
1. {accusation}的标准刑期：{original_imprisonment}个月
2. {accusation}的法定刑期范围：最低{min_imprisonment}个月，最高{max_imprisonment}个月
3. 根据案件情节严重程度，{accusation}的刑期范围通常为：
   - 情节较轻：{severity_levels["轻微"][0]}-{severity_levels["轻微"][1]}个月
   - 情节一般：{severity_levels["一般"][0]}-{severity_levels["一般"][1]}个月
   - 情节严重：{severity_levels["严重"][0]}-{severity_levels["严重"][1]}个月
   - 情节特别严重：{severity_levels["特别严重"][0]}-{severity_levels["特别严重"][1]}个月

【量刑指导 - 必须严格遵循】
1. 请以{original_imprisonment}个月作为基准刑期，然后根据案件具体情况进行调整。
2. 如果案件情节与一般情况相似，请判处{original_imprisonment}个月有期徒刑。
3. 如果案件情节较轻，可以适当减轻刑期，但通常不应低于{original_imprisonment//2}个月。
4. 如果案件情节较重，可以适当加重刑期，但通常不应超过{min(original_imprisonment*2, max_imprisonment)}个月。
5. 请避免判处超出法定范围的刑期。

【量刑因素详细指导】
请在量刑时系统考虑以下因素，并明确量化每个因素对最终刑期的影响。请注意，这些调整应当在法定刑期范围内进行，且最终刑期应当与案件整体情况相匹配：

1. 案件情节严重程度（权重：30%）：
   - 轻微情节：减轻20-30%的刑期
   - 一般情节：维持标准刑期
   - 严重情节：加重20-30%的刑期
   - 特别严重情节：加重30-50%的刑期

2. 行为人主观方面（权重：25%）：
   - 过失行为：减轻30-50%的刑期
   - 间接故意：维持标准刑期
   - 直接故意：加重10-20%的刑期
   - 预谋行为：加重20-40%的刑期

3. 行为人认罪态度（权重：15%）：
   - 自首并积极认罪：减轻30-40%的刑期
   - 如实供述并认罪：减轻20-30%的刑期
   - 部分认罪：减轻10-20%的刑期
   - 拒不认罪：维持标准刑期

4. 行为人前科情况（权重：10%）：
   - 无前科：减轻10-20%的刑期
   - 有轻微前科：维持标准刑期
   - 有类似前科：加重20-30%的刑期
   - 累犯：加重30-50%的刑期

5. 造成的实际后果（权重：20%）：
   - 未造成实际损失：减轻30-50%的刑期
   - 轻微损失：减轻10-30%的刑期
   - 重大损失：加重20-40%的刑期
   - 特别严重损失：加重40-60%的刑期

请注意：各因素的权重表示其在量刑决定中的相对重要性，应当综合考虑所有因素的影响，而不是简单相加。最终刑期必须在法定刑期范围内，且应当与类似案件的量刑标准保持一致。

【量刑考虑的通用指导】
根据相关司法解释和审判实践，请特别注意以下量刑因素：

1. 犯罪情节严重程度：
   - 评估犯罪行为的社会危害性
   - 考虑犯罪行为的持续时间、范围和影响
   - 分析犯罪行为是否具有特殊恶劣性

2. 犯罪数量和规模：
   - 评估涉案数量（如发票数量、器官数量、信息数量等）
   - 考虑涉案金额大小
   - 分析犯罪行为的组织规模和复杂程度

3. 犯罪后果严重程度：
   - 评估对被害人造成的实际损害
   - 考虑对社会秩序的破坏程度
   - 分析是否造成经济损失或其他不良后果

4. 犯罪主观恶性：
   - 评估犯罪动机和目的
   - 考虑犯罪预谋的程度
   - 分析犯罪人的人身危险性

5. 犯罪中的地位和作用：
   - 评估是主犯还是从犯
   - 考虑在共同犯罪中的作用大小
   - 分析是否有组织、领导、教唆行为

6. 认罪悔罪表现：
   - 评估是否自首、立功
   - 考虑是否如实供述、积极退赃
   - 分析悔罪态度的真诚程度

7. 前科情况：
   - 评估是否有前科劣迹
   - 考虑是否属于累犯或惯犯
   - 分析前科与本次犯罪的关联性

【量刑参考标准】
- 情节较轻：基准刑期减轻20-40%
- 情节一般：维持基准刑期
- 情节较重：基准刑期加重20-40%
- 情节特别严重：基准刑期加重40-100%

请根据案件的具体情况，综合考虑上述因素，在法定刑期范围内确定合理的刑期。量刑应当遵循罪责刑相适应原则，确保刑罚的公正性和个别化。

【判决格式要求 - 必须严格遵循】
1. 判决结果必须使用以下固定格式："判决被告人XXX犯{accusation}，判处有期徒刑X年Y个月"或"判决被告人XXX犯{accusation}，判处有期徒刑Z个月"
2. 不要在罪名前添加"涉嫌"、"被控"等词语
3. 罪名必须准确表述为"{accusation}"，不要改变或简化罪名
4. 刑期必须明确表示为具体的月数或年数
5. 如果不构成犯罪，请明确写出"判决被告人XXX无罪"

【刑期计算方法 - 通用指导】
为确保刑期计算准确，请遵循以下通用计算方法：

1. 基准刑期：以{original_imprisonment}个月作为基准刑期
2. 量刑因素综合评估：
   a. 对每个量刑因素进行单独评估，确定其影响方向（加重/减轻）和程度
   b. 考虑各因素的权重，计算综合调整比例
   c. 将综合调整比例应用于基准刑期

3. 计算示例：
   a. 案件情节较轻（减轻25%，权重30%）：影响 = -25% × 30% = -7.5%
   b. 主观恶性较重（加重15%，权重25%）：影响 = +15% × 25% = +3.75%
   c. 认罪态度良好（减轻25%，权重15%）：影响 = -25% × 15% = -3.75%
   d. 无前科（减轻15%，权重10%）：影响 = -15% × 10% = -1.5%
   e. 造成较大损失（加重25%，权重20%）：影响 = +25% × 20% = +5%
   f. 综合调整比例 = -7.5% + 3.75% - 3.75% - 1.5% + 5% = -4%
   g. 最终刑期 = {original_imprisonment} × (1 - 0.04) = {int(original_imprisonment * 0.96)}个月

4. 法定刑期范围检查：
   a. 确保最终刑期不低于法定最低刑期：{min_imprisonment}个月
   b. 确保最终刑期不超过法定最高刑期：{max_imprisonment}个月
   c. 如果计算结果超出法定范围，则应当调整至法定范围内

5. 量刑均衡性检查：
   a. 与类似案件的量刑标准进行比较
   b. 确保量刑结果符合罪责刑相适应原则
   c. 避免明显不合理的量刑结果

【最终判决要求 - 刑期精确性与合理性至关重要】
1. 判决结果必须包含明确的刑期数字，这对于司法公正和后续评估至关重要。
2. 请确保刑期符合以下要求：
   a. 刑期必须在法定范围内：最低{min_imprisonment}个月，最高{max_imprisonment}个月
   b. 刑期应当以{original_imprisonment}个月为基准，根据量刑因素进行合理调整
   c. 刑期应当与案件的整体情况相匹配，避免过轻或过重
   d. 刑期应当与类似案件的量刑标准保持一致，确保量刑公平性
3. 请在判决结果部分使用标准格式："判决被告人XXX犯{accusation}，判处有期徒刑X年Y个月"或"判处有期徒刑Z个月"。
4. 请在判决理由中详细说明量刑因素的评估过程和最终刑期的计算方法，确保判决透明、可解释。

最终判决前，请进行以下检查：
1. 刑期是否在法定范围内？
2. 刑期是否与案件情节相匹配？
3. 量刑因素是否得到全面考虑？
4. 刑期计算方法是否正确？
5. 判决结果是否清晰、准确？
"""
    # 使用较长的max_tokens，确保完整的判决结果，适合 gpt-3.5-turbo-16k
    return get_completion(prompt, max_tokens=4096)

def process_judicial_cases(data, output_file, limit=None, config=None):
    """
    处理司法案例数据

    Args:
        data: 案例数据列表
        output_file: 输出文件路径
        limit: 处理案例数量限制（可选）
        config: 配置参数（可选）

    Returns:
        处理结果列表
    """
    results = []
    config = config or {}  # 如果没有提供配置，使用空字典

    # 如果指定了限制，则只处理指定数量的案例
    cases_to_process = data[:limit] if limit else data

    logging.info(f"Starting to process {len(cases_to_process)} cases")

    # 使用tqdm显示进度条
    for case_index, case in enumerate(tqdm(cases_to_process, desc="Processing cases")):
        try:
            # 提取案件信息
            fact = case.get('fact', '')
            if not fact:
                logging.warning(f"Case {case_index+1}: Skipping case with no fact")
                continue

            # 预处理案件事实，减少长文本的影响
            if len(fact) > 10000:
                logging.warning(f"Case {case_index+1}: Fact too long ({len(fact)} chars), preprocessing...")
                fact = preprocess_long_fact(fact)
                logging.info(f"Case {case_index+1}: Preprocessed fact length: {len(fact)} chars")

            # 获取指控信息
            defendants = case.get('defendants', [])
            outcomes = case.get('outcomes', [])

            if not defendants or not outcomes:
                logging.warning(f"Case {case_index+1}: Skipping case with no defendants or outcomes")
                continue

            # 获取第一个被告的第一个指控
            defendant_name = defendants[0] if defendants else "被告"

            # 查找该被告的判决结果
            defendant_outcome = None
            for outcome in outcomes:
                if outcome.get('name') == defendant_name:
                    defendant_outcome = outcome
                    break

            if not defendant_outcome or not defendant_outcome.get('judgment'):
                logging.warning(f"Case {case_index+1}: Skipping case with no judgment for the defendant")
                continue

            # 获取第一个判决的指控
            accusation = defendant_outcome['judgment'][0].get('accusation', '未知罪名')
            standard_accusation = defendant_outcome['judgment'][0].get('standard_accusation', accusation)

            logging.info(f"Case {case_index+1}/{len(cases_to_process)}: Processing with accusation: {accusation}")

            # 多智能体分析 - 使用通用错误处理函数
            # 第一步：线索分析
            try:
                logging.info(f"Case {case_index+1}: Performing clue analysis...")
                clue_response = clue_analysis(fact)
            except Exception as e:
                error_msg = handle_analysis_error("clue analysis", e)
                clue_response = error_msg

            # 第二步：证据权重分析与伪证识别（使用线索分析结果）
            try:
                logging.info(f"Case {case_index+1}: Performing evidence weight analysis...")
                evidence_analysis_response = evidence_weight_analysis(fact, clue_response)
            except Exception as e:
                error_msg = handle_analysis_error("evidence weight analysis", e)
                evidence_analysis_response = error_msg

            # 其余步骤
            analysis_steps = [
                ("legal expert analysis", lambda: legal_expert_analysis(fact, accusation)),
                ("defense lawyer analysis", lambda: defense_lawyer_analysis(fact, accusation)),
                ("prosecutor analysis", lambda: prosecutor_analysis(fact, accusation)),
                ("sentencing expert analysis", lambda: sentencing_expert_analysis(fact, accusation)),
                ("debate analysis", lambda: debate_analysis(fact, accusation,
                                                          legal_response, defense_response, prosecutor_response,
                                                          sentencing_response))
            ]

            # 执行分析步骤
            legal_response = None
            defense_response = None
            prosecutor_response = None
            sentencing_response = None
            debate_result = None

            for step_name, step_func in analysis_steps:
                try:
                    logging.info(f"Case {case_index+1}: Performing {step_name}...")
                    result = step_func()

                    # 根据步骤名称保存结果
                    if step_name == "legal expert analysis":
                        legal_response = result
                    elif step_name == "defense lawyer analysis":
                        defense_response = result
                    elif step_name == "prosecutor analysis":
                        prosecutor_response = result
                    elif step_name == "sentencing expert analysis":
                        sentencing_response = result
                    elif step_name == "debate analysis":
                        debate_result = result

                except Exception as e:
                    error_msg = handle_analysis_error(step_name, e)

                    # 根据步骤名称保存错误信息
                    if step_name == "legal expert analysis":
                        legal_response = error_msg
                    elif step_name == "defense lawyer analysis":
                        defense_response = error_msg
                    elif step_name == "prosecutor analysis":
                        prosecutor_response = error_msg
                    elif step_name == "sentencing expert analysis":
                        sentencing_response = error_msg
                    elif step_name == "debate analysis":
                        debate_result = error_msg

            # 法官最终判决
            try:
                logging.info(f"Case {case_index+1}: Performing judge decision analysis...")
                final_decision = judge_decision(
                    fact,
                    clue_response,
                    legal_response,
                    defense_response,
                    prosecutor_response,
                    accusation,
                    config,
                    sentencing_response,
                    evidence_analysis_response
                )
            except Exception as e:
                final_decision = handle_analysis_error("judge decision", e)

            # 构建结果
            result = {
                'fact': fact,
                'defendant': defendant_name,
                'accusation': accusation,
                'standard_accusation': standard_accusation,
                'clue_analysis': clue_response,
                'evidence_weight_analysis': evidence_analysis_response,
                'legal_expert_analysis': legal_response,
                'defense_lawyer_analysis': defense_response,
                'prosecutor_analysis': prosecutor_response,
                'sentencing_expert_analysis': sentencing_response,
                'debate_analysis': debate_result,
                'judge_decision': final_decision,
                'original_judgment': defendant_outcome['judgment']
            }

            results.append(result)

            # 每处理一个案例就保存一次结果，避免因错误丢失处理结果
            try:
                save_results(results, output_file)
                logging.info(f"Case {case_index+1}: Saved results (total: {len(results)} cases)")
            except Exception as e:
                logging.error(f"Case {case_index+1}: Error saving results: {e}")

        except Exception as e:
            logging.error(f"Case {case_index+1}: Unhandled error: {e}")
            logging.error(traceback.format_exc())
            # 即使处理失败，也保存当前结果
            if results:
                try:
                    save_results(results, output_file)
                except Exception as save_error:
                    logging.error(f"Case {case_index+1}: Error saving results after failure: {save_error}")

    # 最终日志
    logging.info(f"Processing completed. Total cases processed: {len(results)}")
    return results

def handle_analysis_error(step_name, error, detailed=False):
    """
    通用错误处理函数

    该函数提供统一的错误处理机制，记录错误日志并返回格式化的错误信息。
    可以选择是否包含详细的错误信息和堆栈跟踪。

    Args:
        step_name: 步骤名称
        error: 错误对象
        detailed: 是否包含详细错误信息，默认为False

    Returns:
        错误信息字符串
    """
    # 记录基本错误信息
    logging.error(f"Error in {step_name}: {error}")

    # 如果需要详细信息，记录堆栈跟踪
    if detailed:
        logging.error(traceback.format_exc())
        return f"{step_name}过程中出错: {str(error)}\n详细错误信息: {traceback.format_exc()}"

    # 根据错误类型提供更具体的错误信息
    if isinstance(error, ValueError):
        return f"{step_name}过程中出现值错误: {str(error)}"
    elif isinstance(error, TypeError):
        return f"{step_name}过程中出现类型错误: {str(error)}"
    elif isinstance(error, KeyError):
        return f"{step_name}过程中出现键错误: {str(error)}"
    elif isinstance(error, IndexError):
        return f"{step_name}过程中出现索引错误: {str(error)}"
    elif isinstance(error, AttributeError):
        return f"{step_name}过程中出现属性错误: {str(error)}"
    elif isinstance(error, FileNotFoundError):
        return f"{step_name}过程中出现文件未找到错误: {str(error)}"
    elif isinstance(error, json.JSONDecodeError):
        return f"{step_name}过程中出现JSON解析错误: {str(error)}"
    elif isinstance(error, requests.RequestException):
        return f"{step_name}过程中出现网络请求错误: {str(error)}"
    elif isinstance(error, TimeoutError):
        return f"{step_name}过程中出现超时错误: {str(error)}"
    else:
        return f"{step_name}过程中出错: {str(error)}"

def fix_encoding_issues(text):
    """
    修复文本中的编码问题

    该函数使用通用的编码修复方法，不针对特定文本类型进行特殊处理。
    它尝试多种编码方式来修复可能的乱码问题，并使用统计方法识别和替换常见的乱码模式。

    Args:
        text: 需要修复的文本

    Returns:
        修复后的文本
    """
    if not text:
        return text

    # 检测是否需要修复
    if '\ufffd' not in text and not any(ord(c) > 0xffff for c in text):
        return text

    try:
        # 方法1: 尝试使用不同的编码方式重新解码
        encodings = ['utf-8', 'latin1', 'gbk', 'gb2312', 'gb18030']

        for enc in encodings:
            try:
                # 尝试将文本转换为 bytes 然后再解码
                if isinstance(text, str):
                    text_bytes = text.encode('latin1')
                    decoded_text = text_bytes.decode(enc)

                    # 检查解码结果是否有效（包含足够的中文字符）
                    if sum(1 for c in decoded_text if '\u4e00' <= c <= '\u9fff') > len(decoded_text) * 0.1:
                        return decoded_text
            except (UnicodeEncodeError, UnicodeDecodeError):
                continue

        # 方法2: 使用统计方法识别和替换常见的乱码模式
        # 这种方法不依赖于特定的映射表，而是基于文本中的统计特征

        # 识别重复出现的乱码模式
        pattern_counts = {}
        for i in range(len(text) - 3):
            pattern = text[i:i+4]
            if any(ord(c) > 0x7f for c in pattern):  # 只关注包含非ASCII字符的模式
                pattern_counts[pattern] = pattern_counts.get(pattern, 0) + 1

        # 找出频繁出现的模式
        common_patterns = [p for p, c in pattern_counts.items() if c > 3]

        # 对于每个频繁模式，尝试不同的编码方式
        for pattern in common_patterns:
            for enc in encodings:
                try:
                    pattern_bytes = pattern.encode('latin1')
                    decoded_pattern = pattern_bytes.decode(enc)

                    # 如果解码后的模式看起来合理（包含中文字符），则替换
                    if any('\u4e00' <= c <= '\u9fff' for c in decoded_pattern):
                        text = text.replace(pattern, decoded_pattern)
                except (UnicodeEncodeError, UnicodeDecodeError):
                    continue

        # 方法3: 使用正则表达式替换明显的乱码模式
        # 这种方法不依赖于特定的映射表，而是基于乱码的一般特征

        # 替换常见的乱码标点符号模式
        text = re.sub(r'锛[\?，。：；！]', lambda m: {'锛?': '，', '锛?': '。', '锛?': '：',
                                              '锛?': '；', '锛?': '！', '锛?': '？'}.get(m.group(0), m.group(0)), text)

        # 替换常见的乱码引号模式
        text = re.sub(r'鈥[\?]', lambda m: {'鈥?': '"', '鈥?': '"', '鈥?': ''',
                                         '鈥?': '''}.get(m.group(0), m.group(0)), text)

        # 替换常见的乱码括号模式
        text = re.sub(r'[锛銆][\?]', lambda m: {'锛?': '（', '锛?': '）', '銆?': '《',
                                            '銆?': '》', '銆?': '【', '銆?': '】'}.get(m.group(0), m.group(0)), text)

        # 方法4: 使用通用的中文字符替换规则
        # 这种方法基于常见的乱码模式，但不针对特定领域

        # 替换常见的乱码中文字符
        common_chars = {
            # 常见汉字的乱码模式
            "鍒": "判", "鍐": "决", "琚": "被", "浜": "人", "鐘": "犯",
            "缃": "罪", "鏈": "有", "寰": "徒", "骞": "年", "鏄": "是",
            "鏃": "时", "闂": "间", "鍦": "地", "鏂": "方", "鐗": "物"
        }

        for bad, good in common_chars.items():
            text = text.replace(bad, good)

    except Exception as e:
        logging.warning(f"Error fixing encoding issues: {e}")
        # 如果所有方法都失败，返回原始文本

    return text

def preprocess_long_fact(fact, max_length=10000):
    """
    预处理长文本案件事实，提取关键信息

    使用通用的文本处理方法，不针对特定案例类型进行特殊处理。
    该函数通过保留文本的开头、结尾和包含关键信息的中间部分，
    在保持文本完整性的同时减少长度。

    Args:
        fact: 原始案件事实文本
        max_length: 目标最大长度，默认10000字符

    Returns:
        处理后的案件事实文本
    """
    if not fact or len(fact) <= max_length:
        return fact

    # 计算保留前后部分的长度
    preserve_length = max_length // 3

    # 提取前部分和后部分
    first_part = fact[:preserve_length]
    last_part = fact[-preserve_length:]

    # 中间部分的长度
    middle_length = max_length - 2 * preserve_length - 100  # 100字符用于分隔符

    # 定义通用的法律文本关键词
    key_words = [
        "被告人", "被害人", "证人", "事实", "指控", "证据", "判决", "认为",
        "构成", "罪名", "量刑", "刑期", "有期徒刑", "情节", "行为", "后果",
        "法院", "检察院", "辩护", "自首", "认罪", "悔罪", "退赃", "赔偿"
    ]

    # 将中间部分分成句子
    middle_text = fact[preserve_length:-preserve_length]
    sentences = re.split(r'([。！？；.!?;])', middle_text)

    # 合并相邻的句子和标点
    proper_sentences = []
    for i in range(0, len(sentences)-1, 2):
        if i+1 < len(sentences):
            proper_sentences.append(sentences[i] + sentences[i+1])
        else:
            proper_sentences.append(sentences[i])

    # 如果句子数量为奇数，添加最后一个句子
    if len(sentences) % 2 == 1:
        proper_sentences.append(sentences[-1])

    # 为每个句子计算关键词得分
    sentence_scores = []
    for sentence in proper_sentences:
        # 基础分数：句子长度的倒数（避免过长句子）
        length_score = 1.0 / max(10, min(100, len(sentence)))

        # 关键词分数：包含关键词的数量
        keyword_score = sum(2.0 for word in key_words if word in sentence)

        # 位置分数：靠近文档中间的句子得分较高
        position = proper_sentences.index(sentence) / len(proper_sentences)
        position_score = 1.0 - 2.0 * abs(position - 0.5)  # 中间位置得分最高

        # 总分
        total_score = length_score + keyword_score + position_score
        sentence_scores.append((sentence, total_score))

    # 按分数排序
    sorted_sentences = sorted(sentence_scores, key=lambda x: x[1], reverse=True)

    # 选择得分最高的句子，直到达到中间部分的目标长度
    selected_sentences = []
    current_length = 0

    for sentence, _ in sorted_sentences:
        if current_length + len(sentence) <= middle_length:
            selected_sentences.append(sentence)
            current_length += len(sentence)
        else:
            # 如果已经选择了足够的句子，停止
            if current_length > 0:
                break
            # 如果一个句子都没选上（可能所有句子都太长），选择第一个并截断
            selected_sentences.append(sentence[:middle_length])
            break

    # 组合处理后的文本
    middle_part = "".join(selected_sentences)

    # 添加分隔符
    processed_fact = (
        first_part +
        "\n\n[...案件中间部分省略，以下是提取的关键信息...]\n\n" +
        middle_part +
        "\n\n[...案件结尾部分...]\n\n" +
        last_part
    )

    return processed_fact

def save_results(results, output_file):
    """保存结果到 JSONL 文件"""
    try:
        # jsonlines 库不支持 encoding 参数，使用 open 和 json 库
        with open(output_file, 'w', encoding='utf-8') as f:
            for result in results:
                f.write(json.dumps(result, ensure_ascii=False) + '\n')
        logging.info(f"Results saved to {output_file} with UTF-8 encoding")
    except Exception as e:
        logging.error(f"Error saving results with UTF-8 encoding: {e}")
        try:
            # 尝试使用 jsonlines 库
            with jsonlines.open(output_file, mode='w') as writer:
                for result in results:
                    writer.write(result)
            logging.info(f"Results saved to {output_file} using jsonlines library")
        except Exception as e:
            logging.error(f"Error saving results: {e}")

def extract_accusation_from_decision(decision_text):
    """
    从法官决策文本中提取罪名和刑期，采用更通用的方法

    Args:
        decision_text: 法官决策文本

    Returns:
        (罪名, 刑期(月))元组
    """
    if not decision_text:
        return None, None

    # 处理可能的编码问题
    decision_text = fix_encoding_issues(decision_text)

    # 提取判决结果部分
    result_section = None
    for pattern in RESULT_SECTION_PATTERNS:
        result_match = re.search(pattern, decision_text)
        if result_match:
            result_section = result_match.group(1).strip()
            break

    # 如果找不到判决结果部分，尝试查找包含"判决"的段落
    if not result_section:
        judgment_patterns = [
            r'([^。；\n]*判[决定][^。；\n]*[。；])',
            r'([^。；\n]*鍒[鍐喅][^。；\n]*[。；])'  # 编码问题模式
        ]

        for pattern in judgment_patterns:
            judgment_paragraphs = re.findall(pattern, decision_text)
            if judgment_paragraphs:
                result_section = ' '.join(judgment_paragraphs)
                break

    # 如果仍然找不到判决结果部分，使用整个文本
    if not result_section:
        result_section = decision_text

    # 存储所有找到的罪名
    all_accusations = []

    # 首先在判决结果部分查找罪名
    for pattern in ACCUSATION_PATTERNS:
        accusation_matches = re.findall(pattern, result_section)
        if accusation_matches:
            all_accusations.extend(accusation_matches)

    # 如果在判决结果部分找不到罪名，尝试在整个文本中查找
    if not all_accusations:
        for pattern in ACCUSATION_PATTERNS:
            accusation_matches = re.findall(pattern, decision_text)
            if accusation_matches:
                all_accusations.extend(accusation_matches)

    # 处理找到的所有罪名
    if all_accusations:
        # 清理罪名（去除可能的前缀和后缀）
        cleaned_accusations = []
        for acc in all_accusations:
            # 去除"涉嫌"、"被控"、"犯"等前缀
            acc = re.sub(r'^(涉嫌|被控|判决|认定|构成|犯)', '', acc)
            # 确保罪名以"罪"结尾
            if not acc.endswith('罪'):
                continue
            cleaned_accusations.append(acc)

        # 如果有清理后的罪名，选择最长的一个（通常最具体）
        if cleaned_accusations:
            accusation = max(cleaned_accusations, key=len)
        else:
            # 如果清理后没有有效罪名，使用原始找到的最长罪名
            accusation = max(all_accusations, key=len)
            # 去除"犯"前缀
            accusation = re.sub(r'^犯', '', accusation)
    else:
        accusation = None

    # 处理可能的编码问题
    if accusation and any(ord(c) > 0x7f for c in accusation):
        # 使用通用的编码修复函数，而不是硬编码特定罪名
        accusation = fix_encoding_issues(accusation)

    # 存储所有找到的刑期
    all_imprisonments = []

    # 首先在判决结果部分查找刑期
    for pattern, extractor in IMPRISONMENT_PATTERNS:
        matches = re.finditer(pattern, result_section)
        for match in matches:
            try:
                months = extractor(match)
                if 1 <= months <= 360:  # 合理的刑期范围：1个月到30年
                    all_imprisonments.append(months)
            except:
                pass

    # 如果在判决结果部分找不到刑期，尝试在整个文本中查找
    if not all_imprisonments:
        for pattern, extractor in IMPRISONMENT_PATTERNS:
            matches = re.finditer(pattern, decision_text)
            for match in matches:
                try:
                    months = extractor(match)
                    if 1 <= months <= 360:  # 合理的刑期范围：1个月到30年
                        all_imprisonments.append(months)
                except:
                    pass

    # 如果仍然找不到刑期，尝试从量刑因素部分提取
    if not all_imprisonments:
        # 查找量刑因素部分
        sentencing_section = None
        for pattern in SENTENCING_SECTION_PATTERNS:
            match = re.search(pattern, decision_text)
            if match:
                sentencing_section = match.group(1)
                break

        if sentencing_section:
            # 在量刑因素部分查找刑期
            for pattern, extractor in IMPRISONMENT_PATTERNS:
                matches = re.finditer(pattern, sentencing_section)
                for match in matches:
                    try:
                        months = extractor(match)
                        if 1 <= months <= 360:  # 合理的刑期范围：1个月到30年
                            all_imprisonments.append(months)
                    except:
                        pass

    # 如果仍然找不到刑期，尝试从数字中提取
    if not all_imprisonments:
        # 尝试从判决结果部分提取数字
        if result_section:
            # 查找判决结果中的数字，可能是刑期
            number_matches = re.findall(r'(\d+)', result_section)
            if number_matches:
                # 尝试找到最合理的数字作为刑期
                for num_str in number_matches:
                    try:
                        num = int(num_str)
                        # 如果数字在合理的刑期范围内
                        if 1 <= num <= 30:  # 可能是年
                            all_imprisonments.append(num * 12)
                        elif 1 <= num <= 360:  # 可能是月
                            all_imprisonments.append(num)
                    except:
                        pass

    # 如果找到了多个刑期，选择最合理的一个
    total_months = 0
    if all_imprisonments:
        # 对找到的所有刑期进行频率统计
        imprisonment_counter = Counter(all_imprisonments)

        # 选择出现频率最高的刑期
        most_common = imprisonment_counter.most_common(1)
        if most_common:
            total_months = most_common[0][0]
        else:
            # 如果没有明显的频率最高值，选择中位数
            all_imprisonments.sort()
            total_months = all_imprisonments[len(all_imprisonments) // 2]

    # 如果没有找到刑期，尝试从标准刑期中获取
    if total_months == 0:
        # 查找标准刑期的模式
        standard_patterns = [
            r'标准刑期.*?(\d+)个月',
            r'标准刑期.*?(\d+)年',
            r'参考刑期.*?(\d+)个月',
            r'参考刑期.*?(\d+)年',
            r'合理的刑期.*?(\d+)个月',
            r'合理的刑期.*?(\d+)年',
            r'建议刑期.*?(\d+)个月',
            r'建议刑期.*?(\d+)年'
        ]

        for pattern in standard_patterns:
            match = re.search(pattern, decision_text)
            if match:
                num = int(match.group(1))
                if '年' in pattern:
                    total_months = num * 12
                else:
                    total_months = num
                break

    # 如果仍然没有找到刑期，尝试使用通用的刑期估计方法
    if total_months == 0 and accusation:
        # 使用基于罪名的刑期估计方法，但采用更通用的方法而非特判

        # 基于中国刑法的通用刑期范围
        crime_categories = {
            # 轻微犯罪 - 通常3年以下有期徒刑
            "轻微": {
                "keywords": ["诽谤", "侮辱", "隐私", "偷窥", "窃听", "散布", "猥亵", "侵犯", "轻微伤害"],
                "base_imprisonment": 12,  # 基础刑期1年
                "range": (6, 36)  # 6个月到3年
            },
            # 一般犯罪 - 通常3-7年有期徒刑
            "一般": {
                "keywords": ["盗窃", "诈骗", "抢夺", "职务侵占", "敲诈勒索", "破坏", "妨害", "伪造", "利用信息网络"],
                "base_imprisonment": 48,  # 基础刑期4年
                "range": (36, 84)  # 3年到7年
            },
            # 严重犯罪 - 通常7-15年有期徒刑
            "严重": {
                "keywords": ["贪污", "贿赂", "挪用公款", "走私", "制造毒品", "出卖人体器官", "出售增值税专用发票", "抢劫", "故意伤害"],
                "base_imprisonment": 96,  # 基础刑期8年
                "range": (84, 180)  # 7年到15年
            },
            # 特别严重犯罪 - 通常15年以上有期徒刑
            "特别严重": {
                "keywords": ["故意杀人", "绑架", "爆炸", "放火", "投毒", "强奸", "贩卖人口", "武装抢劫", "恐怖活动"],
                "base_imprisonment": 180,  # 基础刑期15年
                "range": (180, 360)  # 15年到30年
            }
        }

        # 使用通用的方法获取刑期，不再硬编码特定罪名的刑期
        # 首先尝试从法律知识库中获取刑期信息
        legal_info = ""
        try:
            legal_info = get_legal_knowledge(accusation)
        except:
            pass

        # 尝试从法律知识中提取刑期信息
        imprisonment_match = re.search(r'(\d+)个月|(\d+)年', legal_info)
        if imprisonment_match:
            # 如果找到了"X个月"的表述
            if imprisonment_match.group(1):
                total_months = int(imprisonment_match.group(1))
            # 如果找到了"X年"的表述
            elif imprisonment_match.group(2):
                total_months = int(imprisonment_match.group(2)) * 12
        else:
            # 如果无法从法律知识中提取，使用通用分类方法
            severity_level = "一般"  # 默认严重程度

            # 使用更通用的方法确定严重程度，基于罪名类型的一般分类
            # 而不是硬编码特定关键词
            if "轻" in accusation or "侮辱" in accusation or "隐私" in accusation:
                severity_level = "轻微"
            elif "重" in accusation or "严重" in accusation or "恶劣" in accusation:
                severity_level = "严重"
            elif "特别" in accusation or "极其" in accusation:
                severity_level = "特别严重"

            # 使用对应严重程度的基础刑期
            total_months = crime_categories[severity_level]["base_imprisonment"]

        # 提取案件特征，调整刑期
        case_features = extract_case_features_from_text(decision_text)
        if case_features:
            # 根据案件特征调整刑期
            adjustment = 0

            # 从轻因素
            if "认罪" in case_features or "悔罪" in case_features or "坦白" in case_features:
                adjustment -= 0.2  # 减轻20%
            if "初犯" in case_features or "偶犯" in case_features or "无前科" in case_features:
                adjustment -= 0.1  # 减轻10%
            if "自首" in case_features or "投案" in case_features:
                adjustment -= 0.3  # 减轻30%
            if "未遂" in case_features or "中止" in case_features:
                adjustment -= 0.4  # 减轻40%
            if "从犯" in case_features or "胁从" in case_features or "次要作用" in case_features:
                adjustment -= 0.3  # 减轻30%
            if "情节轻微" in case_features or "危害不大" in case_features:
                adjustment -= 0.2  # 减轻20%

            # 从重因素
            if "累犯" in case_features or "再犯" in case_features or "前科" in case_features:
                adjustment += 0.3  # 加重30%
            if "情节严重" in case_features or "后果严重" in case_features:
                adjustment += 0.3  # 加重30%
            if "主犯" in case_features or "首要分子" in case_features or "组织者" in case_features:
                adjustment += 0.2  # 加重20%
            if "拒不认罪" in case_features or "态度恶劣" in case_features:
                adjustment += 0.1  # 加重10%

            # 应用调整
            total_months = int(total_months * (1 + adjustment))

            # 确保刑期在合理范围内
            # 使用通用的方法确保刑期在合理范围内，不再区分特定罪名和通用罪名
            # 使用当前计算出的刑期作为基准，设置合理的上下限
            min_months = max(1, int(total_months * 0.5))  # 最低为当前刑期的50%
            max_months = min(360, int(total_months * 2.0))  # 最高为当前刑期的200%，但不超过30年

            # 如果有严重程度信息，使用对应的范围
            if 'severity_level' in locals() and severity_level in crime_categories:
                severity_min, severity_max = crime_categories[severity_level]["range"]
                # 取两种方法的交集
                min_months = max(min_months, severity_min)
                max_months = min(max_months, severity_max)

            # 确保最终刑期在合理范围内
            total_months = max(min_months, min(total_months, max_months))

    # 最后的合理性检查
    if total_months > 0:
        # 确保刑期在合理范围内
        if total_months > 360:  # 超过30年
            total_months = 360
        elif total_months < 1:  # 小于1个月
            total_months = 1

    return accusation, total_months if total_months > 0 else None

# 此函数已移至utils.py模块

def extract_case_features_from_text(text):
    """
    从文本中提取案件特征，用于刑期预测

    Args:
        text: 案件文本

    Returns:
        特征列表
    """
    if not text:
        return []

    # 特征关键词映射
    feature_keywords = {
        # 从轻因素
        "认罪": ["认罪", "认罪态度好", "认罪认罚", "供认"],
        "悔罪": ["悔罪", "悔改", "真诚悔罪", "深刻悔罪"],
        "坦白": ["坦白", "如实供述", "如实交代", "主动交代"],
        "初犯": ["初犯", "初次犯罪", "第一次犯罪"],
        "偶犯": ["偶犯", "偶然犯罪", "一时糊涂"],
        "无前科": ["无前科", "无犯罪记录", "无犯罪前科"],
        "自首": ["自首", "投案", "主动投案", "主动到公安机关投案"],
        "投案": ["投案自首", "主动投案自首", "到公安机关投案"],
        "未遂": ["未遂", "犯罪未遂", "未得逞", "未既遂"],
        "中止": ["中止", "犯罪中止", "主动中止", "自动中止"],
        "从犯": ["从犯", "从犯地位", "从属地位", "次要作用"],
        "胁从": ["胁从", "被胁迫", "被迫参与"],
        "次要作用": ["次要作用", "辅助作用", "次要地位", "辅助地位"],
        "情节轻微": ["情节轻微", "情节较轻", "情节不严重"],
        "危害不大": ["危害不大", "危害较小", "社会危害性不大"],

        # 从重因素
        "累犯": ["累犯", "累犯情形", "法定累犯"],
        "再犯": ["再犯", "重新犯罪", "再次犯罪"],
        "前科": ["有前科", "犯罪前科", "前科劣迹"],
        "情节严重": ["情节严重", "情节恶劣", "情节特别严重"],
        "后果严重": ["后果严重", "危害后果严重", "造成严重后果"],
        "主犯": ["主犯", "主犯地位", "起主要作用"],
        "首要分子": ["首要分子", "首要", "主要分子"],
        "组织者": ["组织者", "组织", "策划者", "策划"],
        "拒不认罪": ["拒不认罪", "不认罪", "抗拒认罪"],
        "态度恶劣": ["态度恶劣", "态度不好", "拒不悔改"]
    }

    # 提取特征
    features = []
    for feature, keywords in feature_keywords.items():
        for keyword in keywords:
            if keyword in text:
                features.append(feature)
                break

    return features

def main(data_dir=None, test_file=None, output_file=None, limit=None, disable_debate=False):
    """
    主函数

    Args:
        data_dir: 数据目录路径（可选）
        test_file: 测试文件名（可选）
        output_file: 输出文件路径（可选）
        limit: 处理案例数量限制（可选）
        disable_debate: 是否禁用辩论机制（可选）
    """
    # 设置默认参数
    data_dir = data_dir or "processed_data"  # 使用处理后的数据
    test_file = test_file or os.path.join(data_dir, "test_small_processed.jsonl")
    output_file = output_file or "judicial_cola_results.jsonl"
    limit = limit or 5  # 默认处理5个案例

    # 创建配置
    config = {
        "disable_debate": disable_debate
    }

    # 检查处理后的数据文件是否存在
    if not os.path.exists(test_file):
        # 如果处理后的数据不存在，尝试使用原始数据
        logging.warning(f"Processed data file {test_file} not found. Trying original data.")
        fallback_dir = "small"
        fallback_file = os.path.join(fallback_dir, "test_small.jsonl")

        if os.path.exists(fallback_file):
            logging.info(f"Using fallback data file: {fallback_file}")
            test_file = fallback_file
        else:
            logging.error(f"Original data file {fallback_file} not found. Exiting.")
            return

    # 加载数据
    logging.info(f"Loading data from {test_file}")
    data = load_jsonl_data(test_file, limit=limit)

    if not data:
        logging.error("No data loaded. Exiting.")
        return

    logging.info(f"Loaded {len(data)} cases. Starting processing...")

    # 处理案例
    results = process_judicial_cases(data, output_file, limit=limit, config=config)

    # 后处理结果，提取判决中的罪名和刑期
    processed_results = []
    for result in results:
        judge_decision = result.get('judge_decision', '')
        try:
            predicted_accusation, predicted_imprisonment = extract_accusation_from_decision(judge_decision)
        except Exception as e:
            logging.error(f"Error extracting accusation and imprisonment: {e}")
            predicted_accusation, predicted_imprisonment = None, None

        # 添加预测的罪名和刑期
        result['predicted_accusation'] = predicted_accusation
        result['predicted_imprisonment'] = predicted_imprisonment

        processed_results.append(result)

    # 保存处理后的结果
    save_results(processed_results, output_file)

    logging.info(f"Processing completed. {len(results)} cases processed.")

    # 运行评估
    try:
        logging.info("Running evaluation...")
        import judicial_evaluation
        judicial_evaluation.main()
    except Exception as e:
        logging.error(f"Error running evaluation: {e}")
        logging.error(traceback.format_exc())

    # 打印评估结果
    try:
        # 使用最新的评估结果文件
        eval_file = os.path.join('evaluation_results', 'evaluation_results.json')
        if not os.path.exists(eval_file):
            eval_file = 'evaluation_results.json'  # 尝试旧的路径

        with open(eval_file, 'r', encoding='utf-8') as f:
            eval_results = json.load(f)

        accusation_metrics = eval_results.get('accusation_metrics', {})
        imprisonment_metrics = eval_results.get('imprisonment_metrics', {})

        print("\n===== 评估结果 =====")
        print(f"罪名预测准确率: {accusation_metrics.get('accuracy', 0):.4f}")
        print(f"罪名预测精确率: {accusation_metrics.get('precision', 0):.4f}")
        print(f"罪名预测召回率: {accusation_metrics.get('recall', 0):.4f}")
        print(f"罪名预测F1分数: {accusation_metrics.get('f1', 0):.4f}")
        print(f"刑期预测准确率: {imprisonment_metrics.get('accuracy', 0):.4f}")
        print(f"刑期预测平均绝对误差: {imprisonment_metrics.get('mae', 0):.2f}个月")
        print(f"刑期预测平均绝对误差(处理极端值后): {imprisonment_metrics.get('capped_mae', 0):.2f}个月")
        print("=====================")

        # 打印详细的刑期预测结果
        true_imprisonments = eval_results.get('true_imprisonments', [])
        pred_imprisonments = eval_results.get('pred_imprisonments', [])

        if true_imprisonments and pred_imprisonments and len(true_imprisonments) == len(pred_imprisonments):
            print("\n===== 详细刑期预测结果 =====")
            for i, (true, pred) in enumerate(zip(true_imprisonments, pred_imprisonments)):
                error = abs(true - pred)
                print(f"案例 {i+1}: 预测刑期 {pred} 个月, 实际刑期 {true} 个月, 误差 {error} 个月")
            print("=====================")
    except Exception as e:
        logging.error(f"Error printing evaluation results: {e}")
        logging.error(traceback.format_exc())

def parse_arguments():
    """解析命令行参数"""
    import argparse

    parser = argparse.ArgumentParser(description='司法多智能体分析系统')

    parser.add_argument('--data_dir', type=str, default='processed_data',
                        help='数据目录路径')
    parser.add_argument('--test_file', type=str, default=None,
                        help='测试文件路径（如果不指定，将使用data_dir/test_small_processed.jsonl）')
    parser.add_argument('--output_file', type=str, default='judicial_cola_results.jsonl',
                        help='输出文件路径')
    parser.add_argument('--limit', type=int, default=5,
                        help='处理案例数量限制')
    parser.add_argument('--disable_debate', action='store_true',
                        help='禁用辩论机制')

    return parser.parse_args()

if __name__ == "__main__":
    # 解析命令行参数
    args = parse_arguments()

    # 调用主函数
    main(
        data_dir=args.data_dir,
        test_file=args.test_file,
        output_file=args.output_file,
        limit=args.limit,
        disable_debate=args.disable_debate
    )
