"""
智能体依赖关系管理器 (AgentDependencyManager)

多智能体协作的依赖关系管理基础设施，提供：
- 形式化依赖关系建模
- 拓扑排序算法
- 依赖权重计算
- 执行顺序优化
- 协作模式识别

学术价值：
- 支持复杂的智能体依赖关系建模
- 提供动态执行顺序优化
- 为协作推理提供依赖管理基础
"""

import logging
import time
from typing import Dict, List
from collections import defaultdict

# 配置日志
logger = logging.getLogger(__name__)


class AgentDependencyManager:
    """智能体依赖关系管理器"""

    def __init__(self):
        self.dependencies: Dict[str, List[str]] = defaultdict(list)
        self.dependency_weights: Dict[str, Dict[str, float]] = defaultdict(dict)
        self.execution_order: List[str] = []
        self.collaboration_patterns: Dict[str, List[Dict]] = defaultdict(list)

        logger.info("智能体依赖关系管理器初始化完成")

    def add_dependency(self, dependent: str, dependency: str, weight: float = 1.0,
                      dependency_type: str = "sequential"):
        """
        添加依赖关系
        
        Args:
            dependent: 依赖者智能体ID
            dependency: 被依赖的智能体ID
            weight: 依赖权重 (0.0-1.0)
            dependency_type: 依赖类型 (sequential/informational/collaborative)
        """
        self.dependencies[dependent].append(dependency)
        self.dependency_weights[dependent][dependency] = weight

        # 记录协作模式
        self.collaboration_patterns[dependent].append({
            "dependency": dependency,
            "type": dependency_type,
            "weight": weight,
            "created_at": time.time()
        })

        logger.info(f"添加依赖关系: {dependent} 依赖于 {dependency} (权重: {weight}, 类型: {dependency_type})")

    def get_execution_order(self, agents: List[str]) -> List[str]:
        """
        获取智能体执行顺序（拓扑排序）
        
        Args:
            agents: 智能体ID列表
            
        Returns:
            优化后的执行顺序
        """
        # 简化的拓扑排序实现
        in_degree = {agent: 0 for agent in agents}

        # 计算入度
        for agent in agents:
            for dep in self.dependencies.get(agent, []):
                if dep in in_degree:
                    in_degree[agent] += 1

        # 拓扑排序
        queue = [agent for agent in agents if in_degree[agent] == 0]
        result = []

        while queue:
            current = queue.pop(0)
            result.append(current)

            # 更新依赖于当前智能体的其他智能体
            for agent in agents:
                if current in self.dependencies.get(agent, []):
                    in_degree[agent] -= 1
                    if in_degree[agent] == 0:
                        queue.append(agent)

        self.execution_order = result
        logger.info(f"计算得到执行顺序: {result}")
        return result

    def check_dependency_satisfaction(self, agent: str, completed_agents: List[str]) -> bool:
        """
        检查依赖关系是否满足
        
        Args:
            agent: 待检查的智能体ID
            completed_agents: 已完成的智能体ID列表
            
        Returns:
            依赖关系是否满足
        """
        dependencies = self.dependencies.get(agent, [])
        satisfied = all(dep in completed_agents for dep in dependencies)
        
        if not satisfied:
            missing_deps = [dep for dep in dependencies if dep not in completed_agents]
            logger.debug(f"智能体 {agent} 缺少依赖: {missing_deps}")
        
        return satisfied

    def get_collaboration_recommendations(self, agent: str, context: Dict) -> List[Dict]:
        """
        获取协作建议
        
        Args:
            agent: 智能体ID
            context: 上下文信息
            
        Returns:
            协作建议列表
        """
        recommendations = []

        # 基于历史协作模式
        patterns = self.collaboration_patterns.get(agent, [])
        for pattern in patterns:
            if pattern["weight"] > 0.7:  # 高权重依赖
                recommendations.append({
                    "type": "high_priority_collaboration",
                    "target_agent": pattern["dependency"],
                    "reason": f"历史协作权重高 ({pattern['weight']:.2f})",
                    "priority": pattern["weight"],
                    "dependency_type": pattern["type"]
                })

        # 基于当前上下文的动态建议
        if context.get("complexity", 0) > 0.7:
            # 高复杂度任务建议增强协作
            for dep in self.dependencies.get(agent, []):
                recommendations.append({
                    "type": "complexity_driven_collaboration",
                    "target_agent": dep,
                    "reason": "任务复杂度高，建议增强协作",
                    "priority": 0.8
                })

        return recommendations

    def get_dependency_graph(self) -> Dict:
        """
        获取依赖关系图
        
        Returns:
            依赖关系图的字典表示
        """
        graph = {
            "nodes": [],
            "edges": []
        }

        # 收集所有节点
        all_agents = set()
        for dependent, dependencies in self.dependencies.items():
            all_agents.add(dependent)
            all_agents.update(dependencies)

        # 添加节点
        for agent in all_agents:
            graph["nodes"].append({
                "id": agent,
                "label": agent,
                "dependencies_count": len(self.dependencies.get(agent, [])),
                "dependents_count": sum(1 for deps in self.dependencies.values() if agent in deps)
            })

        # 添加边
        for dependent, dependencies in self.dependencies.items():
            for dependency in dependencies:
                weight = self.dependency_weights.get(dependent, {}).get(dependency, 1.0)
                graph["edges"].append({
                    "from": dependency,
                    "to": dependent,
                    "weight": weight,
                    "label": f"{weight:.2f}"
                })

        return graph

    def optimize_execution_order(self, agents: List[str], context: Dict) -> List[str]:
        """
        基于上下文优化执行顺序
        
        Args:
            agents: 智能体ID列表
            context: 优化上下文
            
        Returns:
            优化后的执行顺序
        """
        base_order = self.get_execution_order(agents)
        
        # 基于权重进行微调
        optimized_order = []
        remaining_agents = base_order.copy()
        
        while remaining_agents:
            # 找到所有可以执行的智能体（依赖已满足）
            candidates = []
            for agent in remaining_agents:
                if self.check_dependency_satisfaction(agent, optimized_order):
                    candidates.append(agent)
            
            if not candidates:
                # 如果没有候选者，可能存在循环依赖，选择第一个
                candidates = [remaining_agents[0]]
            
            # 根据权重选择最优候选者
            if len(candidates) == 1:
                chosen = candidates[0]
            else:
                chosen = self._select_optimal_candidate(candidates, optimized_order, context)
            
            optimized_order.append(chosen)
            remaining_agents.remove(chosen)
        
        logger.info(f"优化后执行顺序: {optimized_order}")
        return optimized_order

    def _select_optimal_candidate(self, candidates: List[str], completed: List[str], 
                                context: Dict) -> str:
        """
        从候选者中选择最优的智能体
        
        Args:
            candidates: 候选智能体列表
            completed: 已完成的智能体列表
            context: 选择上下文
            
        Returns:
            选择的智能体ID
        """
        scores = {}
        
        for candidate in candidates:
            score = 0.0
            
            # 基于依赖权重计算分数
            for completed_agent in completed:
                weight = self.dependency_weights.get(candidate, {}).get(completed_agent, 0.0)
                score += weight
            
            # 基于协作模式历史
            patterns = self.collaboration_patterns.get(candidate, [])
            if patterns:
                avg_weight = sum(p["weight"] for p in patterns) / len(patterns)
                score += avg_weight * 0.5
            
            # 基于上下文调整
            if context.get("priority_agent") == candidate:
                score += 1.0
            
            scores[candidate] = score
        
        # 选择分数最高的候选者
        optimal_candidate = max(candidates, key=lambda x: scores[x])
        logger.debug(f"候选者分数: {scores}, 选择: {optimal_candidate}")
        
        return optimal_candidate

    def get_dependency_stats(self) -> Dict:
        """
        获取依赖关系统计信息
        
        Returns:
            统计信息字典
        """
        total_dependencies = sum(len(deps) for deps in self.dependencies.values())
        total_agents = len(set(list(self.dependencies.keys()) + 
                             [dep for deps in self.dependencies.values() for dep in deps]))
        
        # 计算平均权重
        all_weights = []
        for agent_weights in self.dependency_weights.values():
            all_weights.extend(agent_weights.values())
        
        avg_weight = sum(all_weights) / len(all_weights) if all_weights else 0.0
        
        # 找到最复杂的智能体（依赖最多）
        most_complex_agent = max(self.dependencies.items(), 
                               key=lambda x: len(x[1]), 
                               default=(None, []))[0]
        
        return {
            "total_agents": total_agents,
            "total_dependencies": total_dependencies,
            "average_dependencies_per_agent": total_dependencies / max(len(self.dependencies), 1),
            "average_dependency_weight": avg_weight,
            "most_complex_agent": most_complex_agent,
            "dependency_types": list(set(
                pattern["type"] for patterns in self.collaboration_patterns.values() 
                for pattern in patterns
            ))
        }

    def validate_dependencies(self) -> Dict:
        """
        验证依赖关系的有效性
        
        Returns:
            验证结果
        """
        issues = []
        
        # 检查循环依赖
        all_agents = set(list(self.dependencies.keys()) + 
                        [dep for deps in self.dependencies.values() for dep in deps])
        
        try:
            execution_order = self.get_execution_order(list(all_agents))
            if len(execution_order) < len(all_agents):
                issues.append("存在循环依赖，无法确定完整执行顺序")
        except Exception as e:
            issues.append(f"拓扑排序失败: {e}")
        
        # 检查权重合理性
        for agent, weights in self.dependency_weights.items():
            for dep, weight in weights.items():
                if not 0.0 <= weight <= 1.0:
                    issues.append(f"智能体 {agent} 对 {dep} 的依赖权重 {weight} 超出合理范围 [0.0, 1.0]")
        
        # 检查孤立节点
        isolated_agents = []
        for agent in all_agents:
            has_dependencies = agent in self.dependencies and self.dependencies[agent]
            is_dependency = any(agent in deps for deps in self.dependencies.values())
            if not has_dependencies and not is_dependency:
                isolated_agents.append(agent)
        
        if isolated_agents:
            issues.append(f"发现孤立智能体: {isolated_agents}")
        
        return {
            "is_valid": len(issues) == 0,
            "issues": issues,
            "total_agents": len(all_agents),
            "validation_time": time.time()
        }
