"""
司法信息抽取协调器 (重构版本)

基于模块化架构的司法信息抽取系统主协调器，提供：
- 多智能体协作管理
- 四阶段协作式抽取流程
- 证据权重引导机制
- 智能冲突解决

学术价值：
- 完整的多智能体协作架构
- 创新的证据权重引导机制
- 智能化的冲突解决算法
- 支持CCF-B级期刊发表的技术实现
"""

import json
import logging
import time
from typing import Dict, List, Optional, Any

# 导入现有系统组件
from evidence_weight_analyzer import EvidenceWeightAnalyzer, Evidence
from adaptive_debate_framework import AdaptiveDebateFramework
from judicial_cola import get_completion

# 导入模块化的协作组件
from core.message_bus import MessageBus, AgentMessage, MessageType
from core.shared_knowledge import SharedKnowledgeBase
from core.agent_dependency import AgentDependencyManager
from core.collaborative_reasoning import CollaborativeReasoningEngine

# 导入智能体和类型定义
from extraction_agents.entity_extractor import EntityExtractionAgent
from extraction_agents.fact_extractor import FactExtractionAgent
from extraction_agents.legal_element_extractor import LegalElementAgent
from extraction_agents.sentence_extractor import SentenceExtractionAgent
from extraction_types import ExtractionResult, ExtractionTaskType, InformationType

# 配置日志
logger = logging.getLogger(__name__)


class JudicialIECoordinator:
    """司法信息抽取协调器 - 重构版本"""

    def __init__(self, enable_parallel: bool = True, enable_cache: bool = True,
                 enable_collaboration: bool = True):
        """
        初始化司法信息抽取协调器

        Args:
            enable_parallel: 是否启用并行处理
            enable_cache: 是否启用缓存
            enable_collaboration: 是否启用协作机制
        """
        # 系统配置
        self.config = {
            "enable_parallel": enable_parallel,
            "enable_cache": enable_cache,
            "enable_collaboration": enable_collaboration,
            "max_workers": 4,
            "cache_size": 1000
        }

        # 加载法律知识库
        self.legal_knowledge = self._load_legal_knowledge()

        # 初始化智能体
        self.agents = self._initialize_agents()

        # 初始化协作组件
        if enable_collaboration:
            self._initialize_collaboration_components()
        else:
            self.message_bus = None
            self.shared_knowledge = None
            self.dependency_manager = None
            self.reasoning_engine = None

        # 初始化分析组件
        self.evidence_analyzer = EvidenceWeightAnalyzer(get_completion)
        self.debate_framework = AdaptiveDebateFramework(get_completion)

        logger.info("司法信息抽取协调器初始化完成（重构版本）")

    def _load_legal_knowledge(self) -> Dict:
        """加载法律知识库"""
        try:
            with open('legal_knowledge_base.json', 'r', encoding='utf-8') as f:
                knowledge = json.load(f)
            logger.info(f"成功加载法律知识库，包含 {len(knowledge)} 个条目")
            return knowledge
        except Exception as e:
            logger.warning(f"法律知识库加载失败: {e}")
            return {}

    def _initialize_agents(self) -> Dict:
        """初始化智能体"""
        agents = {
            "entity": EntityExtractionAgent(self.legal_knowledge),
            "fact": FactExtractionAgent(self.legal_knowledge),
            "legal": LegalElementAgent(self.legal_knowledge),
            "sentence": SentenceExtractionAgent(self.legal_knowledge)
        }

        logger.info("智能体初始化完成，所有专业智能体已就绪")
        logger.info(f"已加载智能体: {list(agents.keys())}")
        return agents

    def _initialize_collaboration_components(self):
        """初始化协作组件"""
        # 消息总线
        self.message_bus = MessageBus(max_workers=self.config["max_workers"])

        # 共享知识库
        self.shared_knowledge = SharedKnowledgeBase()

        # 依赖关系管理器
        self.dependency_manager = AgentDependencyManager()
        self._setup_agent_dependencies()

        # 协作推理引擎
        self.reasoning_engine = CollaborativeReasoningEngine(
            self.message_bus, self.shared_knowledge
        )

        # 注册智能体到消息总线
        for agent_id, agent in self.agents.items():
            self.message_bus.register_agent(agent_id, {
                "type": "extraction_agent",
                "specialization": agent.specialization
            })

        logger.info("协作组件初始化完成")

    def _setup_agent_dependencies(self):
        """设置智能体依赖关系"""
        # 实体抽取是基础，其他智能体可能依赖其结果
        self.dependency_manager.add_dependency("fact", "entity", weight=0.7,
                                             dependency_type="informational")
        self.dependency_manager.add_dependency("legal", "entity", weight=0.6,
                                             dependency_type="informational")
        self.dependency_manager.add_dependency("legal", "fact", weight=0.8,
                                             dependency_type="sequential")
        self.dependency_manager.add_dependency("sentence", "legal", weight=0.9,
                                             dependency_type="sequential")

    def extract_information(self, text: str,
                          task_type: ExtractionTaskType = ExtractionTaskType.COMPREHENSIVE_EXTRACTION) -> Dict:
        """
        执行司法信息抽取

        Args:
            text: 待抽取的司法文本
            task_type: 抽取任务类型

        Returns:
            抽取结果字典
        """
        start_time = time.time()
        logger.info(f"开始司法信息抽取，任务类型: {task_type.value}")

        try:
            # 阶段1：协作式初步抽取
            preliminary_results = self._collaborative_preliminary_extraction(text, task_type)

            # 阶段2：证据权重引导精细化
            refined_results = self._evidence_guided_refinement(preliminary_results, text)

            # 阶段3：协作式冲突解决
            resolved_results = self._collaborative_conflict_resolution(refined_results, text)

            # 阶段4：结果整合与质量评估
            final_results = self._integrate_and_assess_results(resolved_results, text)

            processing_time = time.time() - start_time
            logger.info(f"信息抽取完成，耗时: {processing_time:.2f}秒")

            return {
                "status": "success",
                "results": final_results.get("results", []),
                "metadata": {
                    "processing_time": processing_time,
                    "total_results": len(final_results.get("results", [])),
                    "collaboration_sessions": final_results.get("collaboration_sessions", 0),
                    "conflicts_resolved": final_results.get("conflicts_resolved", 0)
                },
                "performance_stats": self._get_performance_stats()
            }

        except Exception as e:
            logger.error(f"信息抽取过程出错: {e}")
            return {
                "status": "failed",
                "error": str(e),
                "results": [],
                "metadata": {"processing_time": time.time() - start_time}
            }

    def _collaborative_preliminary_extraction(self, text: str, task_type: ExtractionTaskType) -> List[ExtractionResult]:
        """阶段1：协作式初步抽取"""
        logger.info("执行初步信息抽取")

        # 确定参与的智能体
        participating_agents = self._determine_participating_agents(task_type)

        # 获取执行顺序
        if self.config["enable_collaboration"] and self.dependency_manager:
            execution_order = self.dependency_manager.get_execution_order(participating_agents)
        else:
            execution_order = participating_agents

        # 执行抽取
        all_results = []
        completed_agents = []

        for agent_name in execution_order:
            if agent_name in self.agents:
                agent = self.agents[agent_name]

                # 检查依赖关系
                if (self.config["enable_collaboration"] and self.dependency_manager and
                    not self.dependency_manager.check_dependency_satisfaction(agent_name, completed_agents)):
                    logger.warning(f"智能体 {agent_name} 的依赖关系未满足，跳过")
                    continue

                # 执行抽取
                results = agent.extract(text, task_type)
                all_results.extend(results)
                completed_agents.append(agent_name)

                logger.info(f"{agent_name}抽取完成，获得 {len(results)} 个结果")

        logger.info(f"初步抽取完成，总计获得 {len(all_results)} 个结果")
        return all_results

    def _evidence_guided_refinement(self, results: List[ExtractionResult], text: str) -> List[ExtractionResult]:
        """阶段2：证据权重引导精细化"""
        logger.info("执行证据权重引导的精细化抽取")

        if not results:
            return results

        # 使用证据权重分析器
        try:
            # 转换为Evidence对象
            evidences = []
            for result in results:
                evidence = Evidence(
                    content=result.content,
                    source=result.context,
                    evidence_type=result.info_type.value,
                    confidence=result.confidence
                )
                evidences.append(evidence)

            # 分析证据权重
            weighted_evidences = self.evidence_analyzer.analyze_evidence_weights(evidences)

            # 更新结果的证据权重
            for i, result in enumerate(results):
                if i < len(weighted_evidences):
                    result.evidence_weight = weighted_evidences[i].weight

            logger.info(f"证据权重分析完成，处理了 {len(results)} 个结果")

        except Exception as e:
            logger.warning(f"证据权重分析失败: {e}，使用默认权重")

        return results

    def _collaborative_conflict_resolution(self, results: List[ExtractionResult], text: str) -> List[ExtractionResult]:
        """阶段3：协作式冲突解决"""
        logger.info("检测和解决抽取冲突")

        # 检测冲突
        conflicts = self._detect_conflicts(results)

        if not conflicts:
            logger.info("未检测到冲突")
            return results

        # 使用辩论框架解决冲突
        try:
            logger.info("开始使用辩论机制解决抽取冲突")

            # 简化的冲突解决
            resolved_results = []
            for result in results:
                # 基于置信度和证据权重的简单冲突解决
                if result.confidence > 0.5 and result.evidence_weight > 0.5:
                    resolved_results.append(result)

            # 如果解决后结果太少，保留原始结果
            if len(resolved_results) < len(results) * 0.3:
                resolved_results = results

            logger.info(f"冲突解决完成，最终结果数量: {len(resolved_results)}")
            return resolved_results

        except Exception as e:
            logger.error(f"冲突解决失败: {e}")
            return results

    def _integrate_and_assess_results(self, results: List[ExtractionResult], text: str) -> Dict:
        """阶段4：结果整合与质量评估"""
        logger.info("整合和评估最终结果")

        # 去重和合并
        unique_results = self._deduplicate_results(results)

        # 质量评估
        quality_score = self._calculate_quality_score(unique_results, text)

        return {
            "results": unique_results,
            "quality_score": quality_score,
            "collaboration_sessions": 0,
            "conflicts_resolved": len(results) - len(unique_results)
        }

    def _determine_participating_agents(self, task_type: ExtractionTaskType) -> List[str]:
        """确定参与的智能体"""
        if task_type == ExtractionTaskType.ENTITY_EXTRACTION:
            return ["entity"]
        elif task_type == ExtractionTaskType.FACT_EXTRACTION:
            return ["entity", "fact"]
        elif task_type == ExtractionTaskType.LEGAL_ELEMENT_EXTRACTION:
            return ["entity", "fact", "legal"]
        elif task_type == ExtractionTaskType.SENTENCE_EXTRACTION:
            return ["entity", "fact", "legal", "sentence"]
        else:  # COMPREHENSIVE_EXTRACTION
            return ["entity", "fact", "legal", "sentence"]

    def _detect_conflicts(self, results: List[ExtractionResult]) -> List[Dict]:
        """检测抽取冲突"""
        conflicts = []

        # 简化的冲突检测：检查相同类型的信息是否有矛盾
        type_groups = {}
        for result in results:
            info_type = result.info_type
            if info_type not in type_groups:
                type_groups[info_type] = []
            type_groups[info_type].append(result)

        # 检查每个类型组内的冲突
        for info_type, group in type_groups.items():
            if len(group) > 1:
                # 检查内容是否有显著差异
                contents = [r.content for r in group]
                if len(set(contents)) > 1:  # 有不同的内容
                    conflicts.append({
                        "type": "content_conflict",
                        "info_type": info_type,
                        "conflicting_results": group
                    })

        return conflicts

    def _deduplicate_results(self, results: List[ExtractionResult]) -> List[ExtractionResult]:
        """去重结果"""
        unique_results = []
        seen_contents = set()

        for result in results:
            content_key = f"{result.info_type.value}:{result.content}"
            if content_key not in seen_contents:
                unique_results.append(result)
                seen_contents.add(content_key)

        return unique_results

    def _calculate_quality_score(self, results: List[ExtractionResult], text: str) -> float:
        """计算质量分数"""
        if not results:
            return 0.0

        # 基于置信度和证据权重的质量评估
        total_score = 0.0
        for result in results:
            score = 0.6 * result.confidence + 0.4 * result.evidence_weight
            total_score += score

        return total_score / len(results)

    def _get_performance_stats(self) -> Dict:
        """获取性能统计"""
        stats = {}

        # 收集智能体性能统计
        for agent_name, agent in self.agents.items():
            if hasattr(agent, 'get_performance_report'):
                stats[agent_name] = agent.get_performance_report()

        # 收集协作组件统计
        if self.config["enable_collaboration"]:
            if self.message_bus:
                stats["message_bus"] = self.message_bus.get_processing_stats()
            if self.shared_knowledge:
                stats["shared_knowledge"] = self.shared_knowledge.get_knowledge_stats()
            if self.dependency_manager:
                stats["dependency_manager"] = self.dependency_manager.get_dependency_stats()

        return stats

    def shutdown(self):
        """关闭系统"""
        logger.info("正在关闭司法信息抽取系统...")

        if self.config["enable_collaboration"] and self.message_bus:
            self.message_bus.shutdown()

        logger.info("系统已关闭")
