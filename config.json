{"extraction_confidence_threshold": 0.6, "evidence_weight_threshold": 0.5, "max_extraction_rounds": 3, "performance_optimization": {"enable_short_text_mode": true, "short_text_threshold": 100, "enable_parallel_processing": false, "cache_llm_responses": true, "max_concurrent_agents": 2}, "agent_settings": {"entity_extractor": {"confidence_threshold": 0.6, "max_entities_per_type": 10}, "fact_extractor": {"confidence_threshold": 0.7, "enable_completeness_check": true}, "legal_element_extractor": {"confidence_threshold": 0.6, "enable_accuracy_validation": true}, "sentence_extractor": {"confidence_threshold": 0.6, "enable_sentence_validation": true}}, "evidence_weight_settings": {"enable_simplified_mode": true, "simplified_threshold": 15, "weight_boost_factor": 1.1}, "conflict_resolution": {"enable_debate_mechanism": true, "max_debate_rounds": 2, "consensus_threshold": 0.8}, "logging": {"level": "INFO", "enable_performance_logging": true, "log_api_calls": true}}