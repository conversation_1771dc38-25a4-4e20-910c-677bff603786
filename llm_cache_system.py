"""
LLM响应缓存系统
目标：减少30-50%的重复API调用，提升响应速度

核心特性：
1. 智能缓存键生成
2. LRU缓存策略
3. 相似度匹配
4. 缓存统计和监控
5. 持久化存储
"""

import hashlib
import json
import pickle
import time
import os
from typing import Dict, Optional, Any, List, Tuple
from dataclasses import dataclass, asdict
from collections import OrderedDict
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class CacheEntry:
    """缓存条目"""
    key: str
    prompt: str
    role: str
    response: str
    timestamp: float
    hit_count: int = 0
    similarity_threshold: float = 0.85

class TextSimilarityMatcher:
    """文本相似度匹配器"""
    
    def __init__(self):
        self.similarity_cache = {}
    
    def calculate_similarity(self, text1: str, text2: str) -> float:
        """计算文本相似度（简化版本）"""
        # 缓存键
        cache_key = f"{hash(text1)}_{hash(text2)}"
        if cache_key in self.similarity_cache:
            return self.similarity_cache[cache_key]
        
        # 简化的相似度计算
        if text1 == text2:
            similarity = 1.0
        else:
            # 基于字符级别的相似度
            set1 = set(text1)
            set2 = set(text2)
            intersection = len(set1.intersection(set2))
            union = len(set1.union(set2))
            similarity = intersection / union if union > 0 else 0.0
            
            # 考虑长度相似性
            len_similarity = 1.0 - abs(len(text1) - len(text2)) / max(len(text1), len(text2), 1)
            similarity = 0.7 * similarity + 0.3 * len_similarity
        
        # 缓存结果
        self.similarity_cache[cache_key] = similarity
        return similarity
    
    def find_similar_entries(self, target_text: str, entries: List[CacheEntry], 
                           threshold: float = 0.85) -> List[Tuple[CacheEntry, float]]:
        """查找相似的缓存条目"""
        similar_entries = []
        
        for entry in entries:
            similarity = self.calculate_similarity(target_text, entry.prompt)
            if similarity >= threshold:
                similar_entries.append((entry, similarity))
        
        # 按相似度排序
        similar_entries.sort(key=lambda x: x[1], reverse=True)
        return similar_entries

class LLMResponseCache:
    """LLM响应缓存系统"""
    
    def __init__(self, max_size: int = 1000, cache_file: str = "llm_cache.pkl"):
        self.max_size = max_size
        self.cache_file = cache_file
        self.cache: OrderedDict[str, CacheEntry] = OrderedDict()
        self.similarity_matcher = TextSimilarityMatcher()
        
        # 统计信息
        self.stats = {
            "total_requests": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "similarity_hits": 0,
            "cache_saves": 0,
            "cache_evictions": 0
        }
        
        # 加载持久化缓存
        self.load_cache()
        
        logger.info(f"LLM缓存系统初始化完成，最大容量: {max_size}, 当前条目: {len(self.cache)}")
    
    def _generate_cache_key(self, prompt: str, role: str) -> str:
        """生成缓存键"""
        # 标准化输入
        normalized_prompt = prompt.strip().lower()
        normalized_role = role.strip().lower()
        
        # 生成哈希键
        content = f"{normalized_prompt}|{normalized_role}"
        return hashlib.md5(content.encode('utf-8')).hexdigest()
    
    def get(self, prompt: str, role: str, similarity_threshold: float = 0.85) -> Optional[str]:
        """获取缓存响应"""
        self.stats["total_requests"] += 1
        
        # 精确匹配
        cache_key = self._generate_cache_key(prompt, role)
        if cache_key in self.cache:
            entry = self.cache[cache_key]
            entry.hit_count += 1
            # 移到最后（LRU策略）
            self.cache.move_to_end(cache_key)
            self.stats["cache_hits"] += 1
            logger.debug(f"缓存精确命中: {cache_key[:8]}...")
            return entry.response
        
        # 相似度匹配
        if similarity_threshold > 0:
            similar_entries = self.similarity_matcher.find_similar_entries(
                prompt, list(self.cache.values()), similarity_threshold
            )
            
            if similar_entries:
                best_entry, similarity = similar_entries[0]
                best_entry.hit_count += 1
                self.stats["similarity_hits"] += 1
                logger.info(f"缓存相似度命中: 相似度 {similarity:.3f}, key: {best_entry.key[:8]}...")
                return best_entry.response
        
        # 缓存未命中
        self.stats["cache_misses"] += 1
        logger.debug(f"缓存未命中: {cache_key[:8]}...")
        return None
    
    def set(self, prompt: str, role: str, response: str) -> None:
        """设置缓存"""
        cache_key = self._generate_cache_key(prompt, role)
        
        # 检查是否需要淘汰
        if len(self.cache) >= self.max_size and cache_key not in self.cache:
            # LRU淘汰：移除最久未使用的条目
            oldest_key, oldest_entry = self.cache.popitem(last=False)
            self.stats["cache_evictions"] += 1
            logger.debug(f"缓存淘汰: {oldest_key[:8]}... (命中次数: {oldest_entry.hit_count})")
        
        # 创建新条目
        entry = CacheEntry(
            key=cache_key,
            prompt=prompt,
            role=role,
            response=response,
            timestamp=time.time()
        )
        
        self.cache[cache_key] = entry
        self.stats["cache_saves"] += 1
        logger.debug(f"缓存保存: {cache_key[:8]}...")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        stats = self.stats.copy()
        
        if stats["total_requests"] > 0:
            stats["hit_rate"] = (stats["cache_hits"] + stats["similarity_hits"]) / stats["total_requests"]
            stats["exact_hit_rate"] = stats["cache_hits"] / stats["total_requests"]
            stats["similarity_hit_rate"] = stats["similarity_hits"] / stats["total_requests"]
        else:
            stats["hit_rate"] = 0.0
            stats["exact_hit_rate"] = 0.0
            stats["similarity_hit_rate"] = 0.0
        
        stats["cache_size"] = len(self.cache)
        stats["cache_utilization"] = len(self.cache) / self.max_size
        
        return stats
    
    def clear_cache(self) -> None:
        """清空缓存"""
        self.cache.clear()
        logger.info("缓存已清空")
    
    def save_cache(self) -> None:
        """保存缓存到文件"""
        try:
            with open(self.cache_file, 'wb') as f:
                pickle.dump(dict(self.cache), f)
            logger.info(f"缓存已保存到 {self.cache_file}")
        except Exception as e:
            logger.error(f"保存缓存失败: {e}")
    
    def load_cache(self) -> None:
        """从文件加载缓存"""
        if not os.path.exists(self.cache_file):
            logger.info("缓存文件不存在，使用空缓存")
            return
        
        try:
            with open(self.cache_file, 'rb') as f:
                cache_data = pickle.load(f)
                self.cache = OrderedDict(cache_data)
            logger.info(f"从 {self.cache_file} 加载了 {len(self.cache)} 个缓存条目")
        except Exception as e:
            logger.error(f"加载缓存失败: {e}")
            self.cache = OrderedDict()
    
    def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存详细信息"""
        entries_info = []
        for key, entry in self.cache.items():
            entries_info.append({
                "key": key[:8] + "...",
                "prompt_length": len(entry.prompt),
                "response_length": len(entry.response),
                "hit_count": entry.hit_count,
                "age_hours": (time.time() - entry.timestamp) / 3600
            })
        
        return {
            "total_entries": len(self.cache),
            "max_size": self.max_size,
            "stats": self.get_stats(),
            "entries": entries_info[:10]  # 只显示前10个条目
        }

class CachedLLMWrapper:
    """带缓存的LLM包装器"""
    
    def __init__(self, original_get_completion_func, cache_system: LLMResponseCache):
        self.original_func = original_get_completion_func
        self.cache = cache_system
        
    def get_completion(self, prompt: str, role: str = "assistant", 
                      use_cache: bool = True, similarity_threshold: float = 0.85) -> str:
        """带缓存的LLM调用"""
        if use_cache:
            # 尝试从缓存获取
            cached_response = self.cache.get(prompt, role, similarity_threshold)
            if cached_response:
                return cached_response
        
        # 调用原始函数
        response = self.original_func(prompt, role)
        
        # 保存到缓存
        if use_cache and response:
            self.cache.set(prompt, role, response)
        
        return response

# 测试和使用示例
def test_cache_system():
    """测试缓存系统"""
    print("🧪 测试LLM缓存系统")
    
    # 创建缓存系统
    cache = LLMResponseCache(max_size=100, cache_file="test_cache.pkl")
    
    # 模拟LLM调用
    test_cases = [
        ("被告人张某持木棒击打李某头部", "judicial expert", "张某涉嫌故意伤害罪"),
        ("被告人王某诽谤他人", "judicial expert", "王某涉嫌诽谤罪"),
        ("被告人张某持木棒击打李某头部", "judicial expert", "张某涉嫌故意伤害罪"),  # 重复
        ("被告人张某用木棒击打李某头部", "judicial expert", "相似案例"),  # 相似
    ]
    
    print("\n--- 测试缓存功能 ---")
    for i, (prompt, role, response) in enumerate(test_cases, 1):
        print(f"\n测试 {i}: {prompt[:20]}...")
        
        # 尝试获取缓存
        cached = cache.get(prompt, role)
        if cached:
            print(f"✅ 缓存命中: {cached}")
        else:
            print(f"❌ 缓存未命中，保存响应: {response}")
            cache.set(prompt, role, response)
    
    # 显示统计信息
    print(f"\n📊 缓存统计:")
    stats = cache.get_stats()
    for key, value in stats.items():
        if isinstance(value, float):
            print(f"  {key}: {value:.3f}")
        else:
            print(f"  {key}: {value}")
    
    # 显示缓存信息
    print(f"\n📋 缓存详情:")
    info = cache.get_cache_info()
    print(f"  总条目: {info['total_entries']}")
    print(f"  最大容量: {info['max_size']}")
    
    # 保存缓存
    cache.save_cache()
    
    # 清理测试文件
    if os.path.exists("test_cache.pkl"):
        os.remove("test_cache.pkl")

if __name__ == "__main__":
    test_cache_system()
