#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
证据权重引导机制简化验证测试

验证核心功能：
1. 证据权重引擎基本功能
2. 权重引导策略效果
3. 基类集成测试
"""

import logging
import time
import numpy as np

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_evidence_weight_engine():
    """测试证据权重引擎"""
    logger.info("测试证据权重引擎...")
    
    try:
        from core.evidence_weight_engine import EvidenceWeightEngine, EvidenceItem, EvidenceType
        from judicial_cola import get_completion
        
        # 初始化权重引擎
        weight_engine = EvidenceWeightEngine(get_completion)
        
        # 创建测试证据
        test_evidence = [
            EvidenceItem(
                id="E1",
                content="被告人张某的指纹在现场被发现",
                evidence_type=EvidenceType.PHYSICAL,
                source="公安局"
            ),
            EvidenceItem(
                id="E2", 
                content="证人李某证言",
                evidence_type=EvidenceType.TESTIMONIAL,
                source="证人"
            )
        ]
        
        case_context = "盗窃案件调查"
        
        # 计算权重
        weighted_evidence = weight_engine.calculate_evidence_weights(test_evidence, case_context)
        
        # 验证结果
        for item in weighted_evidence:
            assert 0.0 <= item.final_weight <= 1.0, f"权重超出范围: {item.final_weight}"
            logger.info(f"证据 {item.id}: 最终权重 = {item.final_weight:.3f}")
        
        logger.info("✅ 证据权重引擎测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 证据权重引擎测试失败: {e}")
        return False

def test_weight_guidance_strategies():
    """测试权重引导策略"""
    logger.info("测试权重引导策略...")
    
    try:
        from core.evidence_weight_engine import EvidenceWeightEngine, EvidenceItem, EvidenceType
        from judicial_cola import get_completion
        
        weight_engine = EvidenceWeightEngine(get_completion)
        
        # 创建测试证据
        test_evidence = [
            EvidenceItem(id="E1", content="高质量证据", evidence_type=EvidenceType.PHYSICAL),
            EvidenceItem(id="E2", content="低质量证据", evidence_type=EvidenceType.OTHER),
            EvidenceItem(id="E3", content="中等质量证据", evidence_type=EvidenceType.DOCUMENTARY)
        ]
        
        # 手动设置权重
        test_evidence[0].final_weight = 0.9
        test_evidence[1].final_weight = 0.3
        test_evidence[2].final_weight = 0.6
        
        # 测试阈值过滤
        filtered = weight_engine._apply_threshold_filter(test_evidence, threshold=0.5)
        assert len(filtered) == 2, f"阈值过滤结果错误: {len(filtered)}"
        
        # 测试优先级排序
        ranked = weight_engine._apply_priority_ranking(test_evidence.copy())
        assert ranked[0].final_weight >= ranked[-1].final_weight, "排序失败"
        
        logger.info("✅ 权重引导策略测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 权重引导策略测试失败: {e}")
        return False

def test_base_agent_integration():
    """测试基类集成"""
    logger.info("测试基类集成...")
    
    try:
        from extraction_agents.entity_extractor import EntityExtractionAgent
        from extraction_types import ExtractionTaskType
        
        # 创建智能体
        agent = EntityExtractionAgent({})
        
        # 测试文本
        test_text = "被告人张某于2023年3月在北京市盗窃财物。"
        
        # 执行抽取
        results = agent.extract(test_text, ExtractionTaskType.ENTITY_EXTRACTION)
        
        if results:
            # 测试权重引导
            case_context = "盗窃案件"
            guided_results = agent.apply_weight_guidance(results, case_context)
            
            logger.info(f"原始结果: {len(results)} 个")
            logger.info(f"引导后结果: {len(guided_results)} 个")
            
            # 验证权重引导元数据
            for result in guided_results:
                if result.metadata and result.metadata.get("weight_guided"):
                    logger.info(f"权重引导成功: {result.content[:20]}...")
        
        logger.info("✅ 基类集成测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 基类集成测试失败: {e}")
        return False

def test_weight_analysis_report():
    """测试权重分析报告"""
    logger.info("测试权重分析报告...")
    
    try:
        from core.evidence_weight_engine import EvidenceWeightEngine, EvidenceItem, EvidenceType
        from judicial_cola import get_completion
        
        weight_engine = EvidenceWeightEngine(get_completion)
        
        # 创建测试证据
        test_evidence = [
            EvidenceItem(id="E1", content="物证", evidence_type=EvidenceType.PHYSICAL),
            EvidenceItem(id="E2", content="证言", evidence_type=EvidenceType.TESTIMONIAL)
        ]
        
        # 计算权重
        weighted_evidence = weight_engine.calculate_evidence_weights(test_evidence, "测试案件")
        
        # 生成报告
        report = weight_engine.get_weight_analysis_report(weighted_evidence)
        
        # 验证报告内容
        assert "evidence_count" in report, "报告缺少证据数量"
        assert "weight_statistics" in report, "报告缺少权重统计"
        assert "quality_assessment" in report, "报告缺少质量评估"
        
        logger.info(f"证据数量: {report['evidence_count']}")
        logger.info(f"质量评估: {report['quality_assessment']}")
        logger.info(f"平均权重: {report['weight_statistics']['mean']:.3f}")
        
        logger.info("✅ 权重分析报告测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 权重分析报告测试失败: {e}")
        return False

def test_performance_metrics():
    """测试性能指标"""
    logger.info("测试性能指标...")
    
    try:
        from core.evidence_weight_engine import EvidenceWeightEngine, EvidenceItem, EvidenceType
        from judicial_cola import get_completion
        
        weight_engine = EvidenceWeightEngine(get_completion)
        
        # 性能测试数据
        test_sizes = [5, 10, 20]
        performance_data = []
        
        for size in test_sizes:
            # 创建测试证据
            test_evidence = []
            for i in range(size):
                evidence = EvidenceItem(
                    id=f"E{i+1}",
                    content=f"测试证据内容{i+1}",
                    evidence_type=EvidenceType.DOCUMENTARY
                )
                test_evidence.append(evidence)
            
            # 测量处理时间
            start_time = time.time()
            weighted_evidence = weight_engine.calculate_evidence_weights(test_evidence, "性能测试案件")
            processing_time = time.time() - start_time
            
            performance_data.append({
                "size": size,
                "time": processing_time,
                "avg_weight": np.mean([item.final_weight for item in weighted_evidence])
            })
            
            logger.info(f"证据数量 {size}: 处理时间 {processing_time:.3f}秒")
        
        # 验证性能
        max_time = max(data["time"] for data in performance_data)
        assert max_time < 30.0, f"处理时间过长: {max_time:.3f}秒"
        
        logger.info("✅ 性能指标测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 性能指标测试失败: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    logger.info("开始证据权重引导机制验证")
    logger.info("=" * 50)
    
    tests = [
        ("证据权重引擎", test_evidence_weight_engine),
        ("权重引导策略", test_weight_guidance_strategies),
        ("基类集成", test_base_agent_integration),
        ("权重分析报告", test_weight_analysis_report),
        ("性能指标", test_performance_metrics)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 执行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"测试 {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果
    logger.info("\n" + "=" * 50)
    logger.info("📋 测试结果总结")
    logger.info("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name:15} : {status}")
        if result:
            passed += 1
    
    logger.info(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        logger.info("🎉 所有测试通过！证据权重引导机制工作正常！")
        return True
    else:
        logger.warning("⚠️  部分测试失败，需要进一步检查")
        return False

def main():
    """主函数"""
    print("证据权重引导机制简化验证")
    print("=" * 40)
    
    success = run_all_tests()
    
    if success:
        print("\n✅ 证据权重引导机制验证成功")
        print("📝 核心创新点技术实现完成")
        print("🎯 系统已准备好支撑学术论文")
    else:
        print("\n❌ 验证发现问题，请检查日志")

if __name__ == "__main__":
    main()
