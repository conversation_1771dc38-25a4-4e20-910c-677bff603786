"""
动态权重调整系统 (Dynamic Weight Adjustment System)

基于SharedKnowledgeBase的上下文感知权重调整机制，提供：
- 上下文感知的权重动态调整
- 基于智能体协作信息的权重优化
- 权重学习和自适应优化机制
- 权重调整的收敛性保证算法

学术价值：
- 创新的动态权重调整理论
- 多智能体协作信息融合机制
- 自适应学习优化算法
- 为司法AI系统提供智能化权重管理
"""

import logging
import time
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass

# 导入相关模块
from .shared_knowledge import SharedKnowledgeBase
from .evidence_weight_engine import EvidenceItem, EvidenceWeightEngine

# 配置日志
logger = logging.getLogger(__name__)


@dataclass
class WeightAdjustmentContext:
    """权重调整上下文"""
    case_id: str
    case_context: str
    collaboration_feedback: Dict[str, Any]
    context_importance: float = 1.0
    adjustment_strength: float = 0.1
    timestamp: float = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()


class DynamicWeightAdjuster:
    """动态权重调整系统"""
    
    def __init__(self, shared_knowledge: SharedKnowledgeBase, 
                 weight_engine: EvidenceWeightEngine):
        """
        初始化动态权重调整系统
        
        Args:
            shared_knowledge: 共享知识库
            weight_engine: 证据权重引擎
        """
        self.shared_knowledge = shared_knowledge
        self.weight_engine = weight_engine
        
        # 调整参数
        self.learning_rate = 0.1
        self.convergence_threshold = 0.01
        self.max_iterations = 10
        self.context_decay_factor = 0.9
        
        # 调整历史
        self.adjustment_history: List[Dict] = []
        self.context_cache: Dict[str, WeightAdjustmentContext] = {}
        
        logger.info("动态权重调整系统初始化完成")
    
    def adjust_weights_dynamically(self, evidence_items: List[EvidenceItem], 
                                 context: WeightAdjustmentContext) -> List[EvidenceItem]:
        """
        动态调整证据权重
        
        Args:
            evidence_items: 证据项列表
            context: 调整上下文
            
        Returns:
            调整后的证据项列表
        """
        logger.info(f"开始动态权重调整，案件ID: {context.case_id}")
        
        # 1. 上下文感知调整
        context_adjusted_items = self._context_aware_adjustment(evidence_items, context)
        
        # 2. 协作信息调整
        collaboration_adjusted_items = self._collaboration_based_adjustment(
            context_adjusted_items, context
        )
        
        # 3. 知识库引导调整
        knowledge_guided_items = self._knowledge_guided_adjustment(
            collaboration_adjusted_items, context
        )
        
        # 4. 自适应学习调整
        learning_adjusted_items = self._adaptive_learning_adjustment(
            knowledge_guided_items, context
        )
        
        # 5. 收敛性检查和优化
        final_items = self._convergence_optimization(learning_adjusted_items, context)
        
        # 6. 记录调整历史
        self._record_adjustment_history(evidence_items, final_items, context)
        
        # 7. 更新知识库
        self._update_knowledge_base(final_items, context)
        
        logger.info(f"动态权重调整完成，案件ID: {context.case_id}")
        return final_items
    
    def _context_aware_adjustment(self, evidence_items: List[EvidenceItem], 
                                context: WeightAdjustmentContext) -> List[EvidenceItem]:
        """上下文感知的权重调整"""
        logger.debug("执行上下文感知权重调整")
        
        # 从知识库中搜索相关上下文
        context_knowledge = self._search_contextual_knowledge(context.case_context)
        
        for item in evidence_items:
            # 计算上下文相关性
            context_relevance = self._calculate_context_relevance(
                item.content, context.case_context, context_knowledge
            )
            
            # 基于上下文重要性调整相关性分数
            adjustment_factor = context.context_importance * context_relevance
            
            # 更新相关性分数
            old_relevance = item.relevance_score
            item.relevance_score = (
                (1 - self.learning_rate) * old_relevance + 
                self.learning_rate * adjustment_factor
            )
            
            # 重新计算最终权重
            self._recalculate_final_weight(item)
            
            logger.debug(f"证据 {item.id} 上下文调整: {old_relevance:.3f} -> {item.relevance_score:.3f}")
        
        return evidence_items
    
    def _collaboration_based_adjustment(self, evidence_items: List[EvidenceItem], 
                                      context: WeightAdjustmentContext) -> List[EvidenceItem]:
        """基于智能体协作信息的权重调整"""
        logger.debug("执行协作信息权重调整")
        
        collaboration_feedback = context.collaboration_feedback
        
        for item in evidence_items:
            if item.id in collaboration_feedback:
                feedback = collaboration_feedback[item.id]
                
                # 可靠性调整
                reliability_adjustment = feedback.get("reliability_adjustment", 0.0)
                if reliability_adjustment != 0.0:
                    item.reliability_score += reliability_adjustment * context.adjustment_strength
                    item.reliability_score = max(0.0, min(1.0, item.reliability_score))
                
                # 相互印证度调整
                corroboration_adjustment = feedback.get("corroboration_adjustment", 0.0)
                if corroboration_adjustment != 0.0:
                    item.corroboration_score += corroboration_adjustment * context.adjustment_strength
                    item.corroboration_score = max(0.0, min(1.0, item.corroboration_score))
                
                # 完整性调整
                completeness_adjustment = feedback.get("completeness_adjustment", 0.0)
                if completeness_adjustment != 0.0:
                    item.completeness_score += completeness_adjustment * context.adjustment_strength
                    item.completeness_score = max(0.0, min(1.0, item.completeness_score))
                
                # 重新计算最终权重
                self._recalculate_final_weight(item)
                
                logger.debug(f"证据 {item.id} 协作调整完成")
        
        return evidence_items
    
    def _knowledge_guided_adjustment(self, evidence_items: List[EvidenceItem], 
                                   context: WeightAdjustmentContext) -> List[EvidenceItem]:
        """知识库引导的权重调整"""
        logger.debug("执行知识库引导权重调整")
        
        # 搜索相似案例的权重模式
        similar_cases = self._search_similar_cases(context.case_context)
        
        if not similar_cases:
            return evidence_items
        
        # 分析相似案例的权重模式
        weight_patterns = self._analyze_weight_patterns(similar_cases)
        
        for item in evidence_items:
            # 查找匹配的权重模式
            matching_pattern = self._find_matching_pattern(item, weight_patterns)
            
            if matching_pattern:
                # 应用权重模式调整
                pattern_weight = matching_pattern.get("average_weight", item.final_weight)
                pattern_confidence = matching_pattern.get("confidence", 0.5)
                
                # 加权融合
                fusion_weight = (
                    (1 - pattern_confidence) * item.final_weight + 
                    pattern_confidence * pattern_weight
                )
                
                # 更新权重
                item.final_weight = fusion_weight
                
                # 记录调整信息
                if "adjustments" not in item.metadata:
                    item.metadata["adjustments"] = []
                
                item.metadata["adjustments"].append({
                    "type": "knowledge_guided",
                    "pattern_id": matching_pattern.get("pattern_id"),
                    "original_weight": item.final_weight,
                    "adjusted_weight": fusion_weight,
                    "confidence": pattern_confidence
                })
                
                logger.debug(f"证据 {item.id} 知识引导调整: {item.final_weight:.3f} -> {fusion_weight:.3f}")
        
        return evidence_items
    
    def _adaptive_learning_adjustment(self, evidence_items: List[EvidenceItem], 
                                    context: WeightAdjustmentContext) -> List[EvidenceItem]:
        """自适应学习权重调整"""
        logger.debug("执行自适应学习权重调整")
        
        # 分析历史调整效果
        learning_insights = self._analyze_learning_insights(context.case_id)
        
        if not learning_insights:
            return evidence_items
        
        for item in evidence_items:
            # 查找相关的学习洞察
            relevant_insights = [
                insight for insight in learning_insights 
                if self._is_insight_relevant(item, insight)
            ]
            
            if relevant_insights:
                # 计算学习调整因子
                learning_factor = self._calculate_learning_factor(relevant_insights)
                
                # 应用学习调整
                if learning_factor != 0.0:
                    adjustment = learning_factor * self.learning_rate
                    item.final_weight += adjustment
                    item.final_weight = max(0.0, min(1.0, item.final_weight))
                    
                    logger.debug(f"证据 {item.id} 学习调整: 因子={learning_factor:.3f}")
        
        return evidence_items
    
    def _convergence_optimization(self, evidence_items: List[EvidenceItem], 
                                context: WeightAdjustmentContext) -> List[EvidenceItem]:
        """收敛性优化"""
        logger.debug("执行收敛性优化")
        
        # 检查权重分布的稳定性
        weight_variance = np.var([item.final_weight for item in evidence_items])
        
        if weight_variance > 0.1:  # 权重分布不稳定
            # 应用平滑化处理
            weights = [item.final_weight for item in evidence_items]
            smoothed_weights = self._smooth_weights(weights)
            
            for i, item in enumerate(evidence_items):
                item.final_weight = smoothed_weights[i]
                
            logger.debug("应用权重平滑化处理")
        
        # 确保权重总和的合理性
        total_weight = sum(item.final_weight for item in evidence_items)
        if total_weight > 0:
            # 归一化处理（可选）
            normalization_factor = len(evidence_items) * 0.7 / total_weight
            if normalization_factor < 0.8 or normalization_factor > 1.2:
                for item in evidence_items:
                    item.final_weight *= normalization_factor
                    item.final_weight = max(0.0, min(1.0, item.final_weight))
                
                logger.debug(f"应用权重归一化，因子: {normalization_factor:.3f}")
        
        return evidence_items
    
    def _search_contextual_knowledge(self, case_context: str) -> List[Dict]:
        """搜索上下文相关知识"""
        query = {
            "type": "contextual_knowledge",
            "keywords": case_context.split()[:10],  # 取前10个关键词
            "min_confidence": 0.6
        }
        
        return self.shared_knowledge.search_knowledge(query, "dynamic_adjuster")
    
    def _calculate_context_relevance(self, content: str, case_context: str, 
                                   context_knowledge: List[Dict]) -> float:
        """计算上下文相关性"""
        # 基础相关性（内容与上下文的相似度）
        base_relevance = self._calculate_content_similarity(content, case_context)
        
        # 知识增强相关性
        knowledge_boost = 0.0
        if context_knowledge:
            for knowledge in context_knowledge:
                knowledge_content = str(knowledge.get("content", ""))
                if content in knowledge_content or any(word in knowledge_content for word in content.split()):
                    knowledge_boost += knowledge.get("confidence", 0.0) * 0.1
        
        return min(1.0, base_relevance + knowledge_boost)
    
    def _calculate_content_similarity(self, content1: str, content2: str) -> float:
        """计算内容相似度"""
        if not content1 or not content2:
            return 0.0
        
        words1 = set(content1.lower().split())
        words2 = set(content2.lower().split())
        
        if not words1 or not words2:
            return 0.0
        
        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))
        
        return intersection / union if union > 0 else 0.0
    
    def _search_similar_cases(self, case_context: str) -> List[Dict]:
        """搜索相似案例"""
        query = {
            "type": "case_weight_pattern",
            "keywords": case_context.split()[:5],
            "min_confidence": 0.7
        }
        
        return self.shared_knowledge.search_knowledge(query, "dynamic_adjuster")
    
    def _analyze_weight_patterns(self, similar_cases: List[Dict]) -> List[Dict]:
        """分析权重模式"""
        patterns = []
        
        for case in similar_cases:
            case_content = case.get("content", {})
            if "weight_patterns" in case_content:
                patterns.extend(case_content["weight_patterns"])
        
        return patterns
    
    def _find_matching_pattern(self, item: EvidenceItem, patterns: List[Dict]) -> Optional[Dict]:
        """查找匹配的权重模式"""
        best_match = None
        best_score = 0.0
        
        for pattern in patterns:
            # 计算模式匹配分数
            match_score = self._calculate_pattern_match_score(item, pattern)
            
            if match_score > best_score and match_score > 0.6:
                best_score = match_score
                best_match = pattern
        
        return best_match
    
    def _calculate_pattern_match_score(self, item: EvidenceItem, pattern: Dict) -> float:
        """计算模式匹配分数"""
        score = 0.0
        
        # 证据类型匹配
        if item.evidence_type.value == pattern.get("evidence_type", ""):
            score += 0.3
        
        # 内容相似度
        pattern_content = pattern.get("content_pattern", "")
        if pattern_content:
            content_similarity = self._calculate_content_similarity(item.content, pattern_content)
            score += 0.4 * content_similarity
        
        # 权重范围匹配
        pattern_weight_range = pattern.get("weight_range", [0.0, 1.0])
        if pattern_weight_range[0] <= item.final_weight <= pattern_weight_range[1]:
            score += 0.3
        
        return score
    
    def _analyze_learning_insights(self, case_id: str) -> List[Dict]:
        """分析学习洞察"""
        # 从调整历史中提取学习洞察
        insights = []
        
        for history in self.adjustment_history[-10:]:  # 最近10次调整
            if history.get("case_id") == case_id:
                # 分析调整效果
                improvement = history.get("improvement_score", 0.0)
                if improvement > 0.1:  # 显著改进
                    insights.append({
                        "type": "positive_adjustment",
                        "adjustment_type": history.get("adjustment_type"),
                        "improvement": improvement,
                        "context": history.get("context")
                    })
        
        return insights
    
    def _is_insight_relevant(self, item: EvidenceItem, insight: Dict) -> bool:
        """判断洞察是否相关"""
        insight_context = insight.get("context", {})
        
        # 检查证据类型相关性
        if insight_context.get("evidence_type") == item.evidence_type.value:
            return True
        
        # 检查内容相关性
        insight_content = insight_context.get("content_keywords", [])
        item_words = set(item.content.lower().split())
        
        if any(keyword in item_words for keyword in insight_content):
            return True
        
        return False
    
    def _calculate_learning_factor(self, insights: List[Dict]) -> float:
        """计算学习调整因子"""
        if not insights:
            return 0.0
        
        total_factor = 0.0
        for insight in insights:
            improvement = insight.get("improvement", 0.0)
            insight_type = insight.get("type", "")
            
            if insight_type == "positive_adjustment":
                total_factor += improvement * 0.1
            elif insight_type == "negative_adjustment":
                total_factor -= improvement * 0.1
        
        return max(-0.2, min(0.2, total_factor))  # 限制调整幅度
    
    def _smooth_weights(self, weights: List[float]) -> List[float]:
        """权重平滑化处理"""
        if len(weights) < 3:
            return weights
        
        smoothed = []
        for i in range(len(weights)):
            if i == 0:
                smoothed.append((weights[0] + weights[1]) / 2)
            elif i == len(weights) - 1:
                smoothed.append((weights[-2] + weights[-1]) / 2)
            else:
                smoothed.append((weights[i-1] + weights[i] + weights[i+1]) / 3)
        
        return smoothed
    
    def _recalculate_final_weight(self, item: EvidenceItem):
        """重新计算最终权重"""
        # 使用权重引擎的维度权重
        dimension_weights = self.weight_engine.DIMENSION_WEIGHTS
        
        item.final_weight = (
            dimension_weights['type_weight'] * item.type_weight +
            dimension_weights['reliability_score'] * item.reliability_score +
            dimension_weights['completeness_score'] * item.completeness_score +
            dimension_weights['corroboration_score'] * item.corroboration_score +
            dimension_weights['relevance_score'] * item.relevance_score
        )
        
        # 确保权重在[0,1]范围内
        item.final_weight = max(0.0, min(1.0, item.final_weight))
    
    def _record_adjustment_history(self, original_items: List[EvidenceItem], 
                                 adjusted_items: List[EvidenceItem], 
                                 context: WeightAdjustmentContext):
        """记录调整历史"""
        # 计算调整效果
        original_weights = [item.final_weight for item in original_items]
        adjusted_weights = [item.final_weight for item in adjusted_items]
        
        improvement_score = np.mean(adjusted_weights) - np.mean(original_weights)
        
        history_entry = {
            "timestamp": time.time(),
            "case_id": context.case_id,
            "adjustment_type": "dynamic_adjustment",
            "original_weight_stats": {
                "mean": np.mean(original_weights),
                "std": np.std(original_weights),
                "min": np.min(original_weights),
                "max": np.max(original_weights)
            },
            "adjusted_weight_stats": {
                "mean": np.mean(adjusted_weights),
                "std": np.std(adjusted_weights),
                "min": np.min(adjusted_weights),
                "max": np.max(adjusted_weights)
            },
            "improvement_score": improvement_score,
            "context": {
                "case_context": context.case_context[:200],  # 截取前200字符
                "context_importance": context.context_importance,
                "adjustment_strength": context.adjustment_strength
            }
        }
        
        self.adjustment_history.append(history_entry)
        
        # 保持历史记录在合理范围内
        if len(self.adjustment_history) > 100:
            self.adjustment_history = self.adjustment_history[-100:]
    
    def _update_knowledge_base(self, evidence_items: List[EvidenceItem], 
                             context: WeightAdjustmentContext):
        """更新知识库"""
        # 提取权重模式
        weight_patterns = []
        for item in evidence_items:
            pattern = {
                "evidence_type": item.evidence_type.value,
                "content_pattern": item.content[:100],  # 内容模式
                "weight_range": [max(0.0, item.final_weight - 0.1), 
                               min(1.0, item.final_weight + 0.1)],
                "average_weight": item.final_weight,
                "confidence": item.confidence,
                "pattern_id": f"pattern_{context.case_id}_{item.id}"
            }
            weight_patterns.append(pattern)
        
        # 存储到知识库
        knowledge_id = f"weight_patterns_{context.case_id}_{int(time.time())}"
        self.shared_knowledge.store_knowledge(
            knowledge_id=knowledge_id,
            content={
                "weight_patterns": weight_patterns,
                "case_context": context.case_context,
                "adjustment_timestamp": context.timestamp,
                "confidence": 0.8,
                "tags": ["weight_pattern", "dynamic_adjustment", context.case_id]
            },
            contributor_id="dynamic_adjuster",
            knowledge_type="case_weight_pattern"
        )
    
    def get_adjustment_report(self) -> Dict[str, Any]:
        """获取调整报告"""
        if not self.adjustment_history:
            return {"message": "暂无调整历史"}
        
        recent_adjustments = self.adjustment_history[-10:]
        
        improvements = [adj.get("improvement_score", 0.0) for adj in recent_adjustments]
        
        return {
            "total_adjustments": len(self.adjustment_history),
            "recent_adjustments": len(recent_adjustments),
            "average_improvement": np.mean(improvements) if improvements else 0.0,
            "improvement_trend": "positive" if np.mean(improvements) > 0 else "negative",
            "adjustment_frequency": len(recent_adjustments) / 10.0,  # 最近10次的频率
            "convergence_rate": sum(1 for imp in improvements if abs(imp) < 0.05) / len(improvements)
        }
