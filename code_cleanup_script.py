#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代码库清理和优化脚本

自动化执行代码库清理任务，包括：
1. 删除冗余和重复文件
2. 清理缓存文件
3. 统一代码风格
4. 生成清理报告

使用方法：
python code_cleanup_script.py
"""

import os
import shutil
import json
import logging
from pathlib import Path
from typing import List, Dict

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CodeCleanupManager:
    """代码库清理管理器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.cleanup_report = {
            "deleted_files": [],
            "cleaned_directories": [],
            "optimized_files": [],
            "errors": []
        }
    
    def run_cleanup(self):
        """执行完整的代码清理流程"""
        logger.info("开始代码库清理和优化")
        
        try:
            # 1. 删除缓存文件
            self._clean_cache_files()
            
            # 2. 删除冗余文件
            self._remove_redundant_files()
            
            # 3. 清理空目录
            self._clean_empty_directories()
            
            # 4. 生成清理报告
            self._generate_cleanup_report()
            
            logger.info("代码库清理完成")
            
        except Exception as e:
            logger.error(f"代码清理过程中出现错误: {e}")
            self.cleanup_report["errors"].append(str(e))
    
    def _clean_cache_files(self):
        """清理缓存文件"""
        logger.info("清理缓存文件...")
        
        cache_patterns = [
            "__pycache__",
            "*.pyc",
            "*.pyo",
            "*.pkl",
            ".pytest_cache",
            ".coverage"
        ]
        
        for pattern in cache_patterns:
            self._remove_files_by_pattern(pattern)
    
    def _remove_redundant_files(self):
        """删除冗余文件"""
        logger.info("删除冗余文件...")
        
        # 定义冗余文件列表
        redundant_files = [
            # 重复的测试文件
            "test_basic_optimization.py",
            "test_performance_optimization.py",
            "tests/test_performance_optimized.py",
            
            # 过时的文档文件
            "optimization_plan.md",
            "implementation_roadmap.md",
            "ccf_b_enhancement_strategy.md",
            "data_analysis_and_expansion_plan.md",
            "dataset_collection_plan.md",
            "execution_plan_and_timeline.md",
            "large_scale_experiment_strategy.md",
            "next_optimization_plan.md",
            "unified_judicial_system_design.md",
            
            # 临时文件
            "judicial_ie_cache.pkl",
            "expanded_judicial_cases.jsonl",
            "expansion_report.json",
            
            # 重复的实现文件
            "baseline_methods_implementation.py",
            "data_expansion_toolkit.py",
            "preprocess_data.py"
        ]
        
        for file_path in redundant_files:
            full_path = self.project_root / file_path
            if full_path.exists():
                try:
                    if full_path.is_file():
                        full_path.unlink()
                    elif full_path.is_dir():
                        shutil.rmtree(full_path)
                    
                    self.cleanup_report["deleted_files"].append(str(file_path))
                    logger.info(f"删除冗余文件: {file_path}")
                    
                except Exception as e:
                    logger.error(f"删除文件 {file_path} 失败: {e}")
                    self.cleanup_report["errors"].append(f"删除 {file_path} 失败: {e}")
    
    def _remove_files_by_pattern(self, pattern: str):
        """根据模式删除文件"""
        if pattern.startswith("*"):
            # 处理通配符模式
            for file_path in self.project_root.rglob(pattern):
                try:
                    if file_path.is_file():
                        file_path.unlink()
                        self.cleanup_report["deleted_files"].append(str(file_path.relative_to(self.project_root)))
                except Exception as e:
                    logger.error(f"删除文件 {file_path} 失败: {e}")
        else:
            # 处理目录模式
            for dir_path in self.project_root.rglob(pattern):
                try:
                    if dir_path.is_dir():
                        shutil.rmtree(dir_path)
                        self.cleanup_report["cleaned_directories"].append(str(dir_path.relative_to(self.project_root)))
                        logger.info(f"删除缓存目录: {dir_path.relative_to(self.project_root)}")
                except Exception as e:
                    logger.error(f"删除目录 {dir_path} 失败: {e}")
    
    def _clean_empty_directories(self):
        """清理空目录"""
        logger.info("清理空目录...")
        
        for dir_path in self.project_root.rglob("*"):
            if dir_path.is_dir() and not any(dir_path.iterdir()):
                try:
                    dir_path.rmdir()
                    self.cleanup_report["cleaned_directories"].append(str(dir_path.relative_to(self.project_root)))
                    logger.info(f"删除空目录: {dir_path.relative_to(self.project_root)}")
                except Exception as e:
                    logger.error(f"删除空目录 {dir_path} 失败: {e}")
    
    def _generate_cleanup_report(self):
        """生成清理报告"""
        logger.info("生成清理报告...")
        
        report_content = f"""# 代码库清理报告

## 清理统计

- **删除文件数量**: {len(self.cleanup_report['deleted_files'])}
- **清理目录数量**: {len(self.cleanup_report['cleaned_directories'])}
- **优化文件数量**: {len(self.cleanup_report['optimized_files'])}
- **错误数量**: {len(self.cleanup_report['errors'])}

## 删除的文件

"""
        
        for file_path in self.cleanup_report["deleted_files"]:
            report_content += f"- {file_path}\n"
        
        report_content += "\n## 清理的目录\n\n"
        for dir_path in self.cleanup_report["cleaned_directories"]:
            report_content += f"- {dir_path}\n"
        
        if self.cleanup_report["errors"]:
            report_content += "\n## 错误信息\n\n"
            for error in self.cleanup_report["errors"]:
                report_content += f"- {error}\n"
        
        # 保存报告
        report_path = self.project_root / "cleanup_report.md"
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        logger.info(f"清理报告已保存到: {report_path}")
        
        # 保存JSON格式的详细报告
        json_report_path = self.project_root / "cleanup_report.json"
        with open(json_report_path, 'w', encoding='utf-8') as f:
            json.dump(self.cleanup_report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"详细清理报告已保存到: {json_report_path}")


def main():
    """主函数"""
    print("司法信息提取系统代码库清理工具")
    print("=" * 50)
    
    # 创建清理管理器
    cleanup_manager = CodeCleanupManager()
    
    # 执行清理
    cleanup_manager.run_cleanup()
    
    print("\n清理完成！")
    print("请查看 cleanup_report.md 了解详细清理结果")


if __name__ == "__main__":
    main()
