"""
证据权重分析与伪证识别模块

该模块实现了证据权重分析和伪证识别功能，用于评估不同证据的重要性、
检测证据之间的一致性、识别可能的伪证，以及评估证据链的完整性。
"""

import re
import json
import logging
import itertools
from typing import List, Dict, Any, Tuple, Set, Optional, Union

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class Evidence:
    """证据类，表示一个证据项"""

    def __init__(self, id: str, content: str, type: str = None, source: str = None):
        """
        初始化证据对象

        Args:
            id: 证据ID
            content: 证据内容
            type: 证据类型（如物证、言词证据、书证等）
            source: 证据来源
        """
        self.id = id
        self.content = content
        self.type = type or self._infer_evidence_type(content)
        self.source = source
        self.weight = 0.0  # 证据权重
        self.reliability = 0.0  # 可靠性评分
        self.relevance = 0.0  # 相关性评分
        self.consistency = 0.0  # 一致性评分
        self.false_probability = 0.0  # 伪证概率
        self.is_key_evidence = False  # 是否为关键证据

    def _infer_evidence_type(self, content: str) -> str:
        """
        根据证据内容推断证据类型

        Args:
            content: 证据内容

        Returns:
            推断的证据类型
        """
        # 根据关键词推断证据类型
        if any(keyword in content for keyword in ["证人", "陈述", "证言", "口供", "询问笔录", "讯问笔录"]):
            return "言词证据"
        elif any(keyword in content for keyword in ["照片", "视频", "录音", "指纹", "DNA", "物品", "工具"]):
            return "物证"
        elif any(keyword in content for keyword in ["鉴定", "鉴定结论", "鉴定报告", "检验报告"]):
            return "鉴定意见"
        elif any(keyword in content for keyword in ["合同", "协议", "账单", "收据", "发票", "文件"]):
            return "书证"
        elif any(keyword in content for keyword in ["勘验", "检查", "现场", "勘查", "勘验笔录"]):
            return "勘验检查笔录"
        else:
            return "其他证据"

    def to_dict(self) -> Dict:
        """
        将证据对象转换为字典

        Returns:
            证据对象的字典表示
        """
        return {
            "id": self.id,
            "content": self.content,
            "type": self.type,
            "source": self.source,
            "weight": self.weight,
            "reliability": self.reliability,
            "relevance": self.relevance,
            "consistency": self.consistency,
            "false_probability": self.false_probability,
            "is_key_evidence": self.is_key_evidence
        }

    def __str__(self) -> str:
        """
        返回证据的字符串表示

        Returns:
            证据的字符串表示
        """
        return f"证据[{self.id}]({self.type}): {self.content[:50]}..."

class EvidenceWeightAnalyzer:
    """证据权重分析与伪证识别器"""

    def __init__(self, get_completion_func):
        """
        初始化证据权重分析与伪证识别器

        Args:
            get_completion_func: 获取LLM回复的函数
        """
        self.get_completion = get_completion_func
        self.evidence_list = []  # 证据列表
        self.evidence_weights = {}  # 证据权重
        self.consistency_analysis = {}  # 一致性分析结果
        self.potential_false_evidence = []  # 潜在伪证
        self.chain_completeness = 0.0  # 证据链完整性

    def extract_evidence_from_clue_analysis(self, clue_analysis_result: str) -> List[Evidence]:
        """
        从线索分析结果中提取证据

        Args:
            clue_analysis_result: 线索分析结果文本

        Returns:
            提取的证据列表
        """
        evidence_list = []

        try:
            # 查找证据链分析部分
            evidence_chain_match = re.search(r'7\.\s*证据链分析[：:]([\s\S]+?)(?=8\.|$)', clue_analysis_result)
            if evidence_chain_match:
                evidence_chain_text = evidence_chain_match.group(1).strip()

                # 提取证据列表
                evidence_items = re.findall(r'[•\-–—]\s*([^•\-–—]+?)(?=[•\-–—]|$)', evidence_chain_text)
                if not evidence_items:
                    # 尝试其他格式
                    evidence_items = re.findall(r'(\d+)[\.、]\s*([^\d]+?)(?=\d+[\.、]|$)', evidence_chain_text)
                    evidence_items = [item[1] for item in evidence_items]

                if not evidence_items:
                    # 尝试按分号或句号分割
                    evidence_items = re.split(r'[；;。\n]+', evidence_chain_text)
                    evidence_items = [item.strip() for item in evidence_items if item.strip()]

                if evidence_items:
                    # 创建证据对象列表
                    for i, item in enumerate(evidence_items):
                        # 尝试识别证据类型
                        evidence_type = "其他证据"
                        for type_keyword, type_name in [
                            (["证人", "证言", "口供", "询问笔录", "讯问笔录"], "言词证据"),
                            (["照片", "视频", "录音", "指纹", "DNA", "物品", "工具"], "物证"),
                            (["鉴定", "鉴定结论", "鉴定报告", "检验报告"], "鉴定意见"),
                            (["合同", "协议", "账单", "收据", "发票", "文件"], "书证"),
                            (["勘验", "检查", "现场", "勘查", "勘验笔录"], "勘验检查笔录")
                        ]:
                            if any(keyword in item for keyword in type_keyword):
                                evidence_type = type_name
                                break

                        # 尝试识别证据来源
                        source = None
                        source_match = re.search(r'来自[：:]\s*([^，。；]+)', item)
                        if source_match:
                            source = source_match.group(1).strip()

                        evidence = Evidence(
                            id=f"E{i+1}",
                            content=item.strip(),
                            type=evidence_type,
                            source=source
                        )
                        evidence_list.append(evidence)

            # 如果没有找到证据链分析部分，尝试从整个文本中提取
            if not evidence_list:
                # 使用LLM提取证据
                prompt = """请从以下案件线索分析中提取所有证据，包括物证、言词证据、书证、鉴定意见等。

对于每个证据，请提供以下信息：
1. 证据内容：具体描述证据的内容
2. 证据类型：如物证、言词证据、书证、鉴定意见等
3. 证据来源：如可能的话，说明证据的来源

请以JSON格式输出，格式如下：
```json
[
  {
    "content": "证据内容描述",
    "type": "证据类型",
    "source": "证据来源（如有）"
  },
  ...
]
```

案件线索分析：
"""

                response = self.get_completion(prompt + clue_analysis_result, role="legal evidence extractor")

                # 解析提取的证据
                json_match = re.search(r'```json\s*([\s\S]*?)\s*```', response)
                if json_match:
                    json_str = json_match.group(1)
                    try:
                        data = json.loads(json_str)
                        for i, item in enumerate(data):
                            evidence = Evidence(
                                id=f"E{i+1}",
                                content=item.get("content", ""),
                                type=item.get("type", None),
                                source=item.get("source", None)
                            )
                            evidence_list.append(evidence)
                    except json.JSONDecodeError:
                        logger.error(f"JSON解析失败: {json_str}")

        except Exception as e:
            logger.warning(f"Error extracting evidence from clue analysis: {e}")

        self.evidence_list = evidence_list
        return evidence_list

    def analyze_evidence_weights(self, fact: str, clue_analysis_result: str = None,
                               evidence_list: List[Evidence] = None) -> Dict:
        """
        分析证据权重

        Args:
            fact: 案件事实文本
            clue_analysis_result: 线索分析结果（可选）
            evidence_list: 证据列表（可选）

        Returns:
            证据权重分析结果
        """
        # 如果没有提供证据列表，尝试从线索分析结果中提取
        if evidence_list is None:
            if clue_analysis_result:
                evidence_list = self.extract_evidence_from_clue_analysis(clue_analysis_result)
            else:
                logger.error("未提供证据列表或线索分析结果")
                return {
                    "evidence_list": [],
                    "evidence_weights": {},
                    "consistency_analysis": {},
                    "potential_false_evidence": [],
                    "chain_completeness": 0.0
                }

        # 1. 评估每个证据的权重
        logger.info("步骤1: 评估证据权重...")
        evidence_weights = self._evaluate_evidence_weights(fact, evidence_list)

        # 2. 检测证据之间的一致性
        logger.info("步骤2: 分析证据一致性...")
        consistency_analysis = self._analyze_evidence_consistency(fact, evidence_list)

        # 3. 识别可能的伪证
        logger.info("步骤3: 识别潜在伪证...")
        potential_false_evidence = self._identify_potential_false_evidence(fact, evidence_list, consistency_analysis)

        # 4. 评估证据链完整性
        logger.info("步骤4: 评估证据链完整性...")
        chain_completeness = self._evaluate_evidence_chain_completeness(fact, evidence_list, evidence_weights)

        # 5. 标记关键证据
        logger.info("步骤5: 标记关键证据...")
        key_evidence = self._identify_key_evidence(evidence_list, evidence_weights, consistency_analysis)

        # 保存分析结果
        self.evidence_weights = evidence_weights
        self.consistency_analysis = consistency_analysis
        self.potential_false_evidence = potential_false_evidence
        self.chain_completeness = chain_completeness

        # 返回分析结果
        return {
            "evidence_list": [e.to_dict() for e in evidence_list],
            "evidence_weights": evidence_weights,
            "consistency_analysis": consistency_analysis,
            "potential_false_evidence": potential_false_evidence,
            "chain_completeness": chain_completeness,
            "key_evidence": key_evidence
        }

    def _evaluate_evidence_weights(self, fact: str, evidence_list: List[Evidence]) -> Dict[str, float]:
        """
        评估每个证据的权重

        Args:
            fact: 案件事实文本
            evidence_list: 证据列表

        Returns:
            证据权重字典，键为证据ID，值为权重
        """
        evidence_weights = {}

        # 使用LLM评估证据权重
        prompt = """请作为资深法律证据专家，评估以下案件中各证据的权重。

案件事实：
{fact}

证据列表：
{evidence_text}

请根据以下因素评估每个证据的权重（0-1之间的数值，1表示最重要）：
1. 证据类型的基础权重（直接证据 > 间接证据，物证 > 言词证据）
2. 证据来源的可靠性
3. 证据与案件关键事实的相关性
4. 证据的完整性和清晰度
5. 证据与其他证据的相互印证程度

请以JSON格式输出评估结果，格式如下：
```json
{{
  "E1": {{
    "weight": 0.8,
    "reliability": 0.7,
    "relevance": 0.9,
    "reasoning": "该证据是物证，直接证明了关键事实，来源可靠..."
  }},
  "E2": {{
    ...
  }}
}}
```

请确保评估客观、公正、全面，并提供简要的评估理由。"""

        # 准备证据文本
        evidence_text = ""
        for i, evidence in enumerate(evidence_list):
            evidence_text += f"{evidence.id}. [{evidence.type}] {evidence.content}\n"
            if evidence.source:
                evidence_text += f"   来源: {evidence.source}\n"
            evidence_text += "\n"

        # 获取LLM回复
        response = self.get_completion(prompt.format(fact=fact[:1000], evidence_text=evidence_text),
                                      role="legal evidence weight evaluator")

        # 解析回复
        json_match = re.search(r'```json\s*([\s\S]*?)\s*```', response)
        if json_match:
            json_str = json_match.group(1)
            try:
                weights_data = json.loads(json_str)
                for evidence_id, data in weights_data.items():
                    # 更新证据对象的属性
                    for evidence in evidence_list:
                        if evidence.id == evidence_id:
                            evidence.weight = data.get("weight", 0.0)
                            evidence.reliability = data.get("reliability", 0.0)
                            evidence.relevance = data.get("relevance", 0.0)
                            break

                    # 保存权重数据
                    evidence_weights[evidence_id] = data.get("weight", 0.0)
            except json.JSONDecodeError:
                logger.error(f"JSON解析失败: {json_str}")
                # 使用备用方法
                self._evaluate_evidence_weights_backup(fact, evidence_list, evidence_weights)
        else:
            # 使用备用方法
            self._evaluate_evidence_weights_backup(fact, evidence_list, evidence_weights)

        return evidence_weights

    def _evaluate_evidence_weights_backup(self, fact: str, evidence_list: List[Evidence],
                                        evidence_weights: Dict[str, float]) -> None:
        """
        备用的证据权重评估方法

        Args:
            fact: 案件事实文本
            evidence_list: 证据列表
            evidence_weights: 证据权重字典（将被修改）
        """
        # 基于证据类型的基础权重
        type_weights = {
            "物证": 0.8,
            "鉴定意见": 0.7,
            "书证": 0.6,
            "勘验检查笔录": 0.6,
            "言词证据": 0.5,
            "其他证据": 0.4
        }

        # 为每个证据分配权重
        for evidence in evidence_list:
            # 基础权重
            base_weight = type_weights.get(evidence.type, 0.4)

            # 根据内容长度和详细程度调整权重
            content_factor = min(1.0, len(evidence.content) / 200) * 0.1

            # 根据是否有来源调整权重
            source_factor = 0.1 if evidence.source else 0.0

            # 计算最终权重
            final_weight = base_weight + content_factor + source_factor

            # 确保权重在0-1范围内
            final_weight = max(0.0, min(1.0, final_weight))

            # 更新证据对象和权重字典
            evidence.weight = final_weight
            evidence.reliability = base_weight
            evidence.relevance = 0.5  # 默认相关性
            evidence_weights[evidence.id] = final_weight

    def _analyze_evidence_consistency(self, fact: str, evidence_list: List[Evidence]) -> Dict:
        """
        检测证据之间的一致性

        Args:
            fact: 案件事实文本
            evidence_list: 证据列表

        Returns:
            一致性分析结果
        """
        # 如果证据数量太少，无法进行一致性分析
        if len(evidence_list) < 2:
            return {
                "overall_consistency": 1.0,
                "consistency_matrix": {},
                "conflict_pairs": []
            }

        # 使用LLM分析证据一致性
        prompt = """请作为资深法律证据专家，分析以下案件中各证据之间的一致性。

案件事实：
{fact}

证据列表：
{evidence_text}

请分析每对证据之间的一致性（0-1之间的数值，1表示完全一致，0表示完全冲突）：
1. 证据内容是否相互支持或冲突
2. 证据描述的事实是否一致
3. 证据的时间、地点、人物等要素是否协调

请以JSON格式输出分析结果，格式如下：
```json
{{
  "overall_consistency": 0.8,
  "consistency_matrix": {{
    "E1-E2": 0.9,
    "E1-E3": 0.7,
    "E2-E3": 0.8,
    ...
  }},
  "conflict_pairs": [
    {{
      "pair": "E1-E4",
      "consistency": 0.2,
      "conflict_description": "E1描述事件发生在白天，而E4描述事件发生在夜间..."
    }},
    ...
  ]
}}
```

请确保分析客观、公正、全面，并对明显冲突的证据对提供简要的冲突描述。"""

        # 准备证据文本
        evidence_text = ""
        for evidence in evidence_list:
            evidence_text += f"{evidence.id}. [{evidence.type}] {evidence.content}\n\n"

        # 获取LLM回复
        response = self.get_completion(prompt.format(fact=fact[:1000], evidence_text=evidence_text),
                                      role="legal evidence consistency analyzer")

        # 解析回复
        json_match = re.search(r'```json\s*([\s\S]*?)\s*```', response)
        if json_match:
            json_str = json_match.group(1)
            try:
                consistency_data = json.loads(json_str)

                # 更新证据对象的一致性属性
                consistency_matrix = consistency_data.get("consistency_matrix", {})
                for evidence in evidence_list:
                    # 计算该证据与其他证据的平均一致性
                    consistency_scores = []
                    for other_evidence in evidence_list:
                        if evidence.id != other_evidence.id:
                            pair_key = f"{evidence.id}-{other_evidence.id}"
                            reverse_pair_key = f"{other_evidence.id}-{evidence.id}"

                            if pair_key in consistency_matrix:
                                consistency_scores.append(consistency_matrix[pair_key])
                            elif reverse_pair_key in consistency_matrix:
                                consistency_scores.append(consistency_matrix[reverse_pair_key])

                    if consistency_scores:
                        evidence.consistency = sum(consistency_scores) / len(consistency_scores)

                return consistency_data
            except json.JSONDecodeError:
                logger.error(f"JSON解析失败: {json_str}")
                # 使用备用方法
                return self._analyze_evidence_consistency_backup(evidence_list)
        else:
            # 使用备用方法
            return self._analyze_evidence_consistency_backup(evidence_list)

    def _analyze_evidence_consistency_backup(self, evidence_list: List[Evidence]) -> Dict:
        """
        备用的证据一致性分析方法

        Args:
            evidence_list: 证据列表

        Returns:
            一致性分析结果
        """
        # 默认所有证据都是一致的
        consistency_matrix = {}
        for i, evidence1 in enumerate(evidence_list):
            for j, evidence2 in enumerate(evidence_list):
                if i < j:  # 避免重复计算
                    pair_key = f"{evidence1.id}-{evidence2.id}"
                    consistency_matrix[pair_key] = 0.8  # 默认一致性较高

        # 更新证据对象的一致性属性
        for evidence in evidence_list:
            evidence.consistency = 0.8  # 默认一致性较高

        return {
            "overall_consistency": 0.8,
            "consistency_matrix": consistency_matrix,
            "conflict_pairs": []
        }

    def _identify_potential_false_evidence(self, fact: str, evidence_list: List[Evidence],
                                         consistency_analysis: Dict) -> List[Dict]:
        """
        识别可能的伪证

        Args:
            fact: 案件事实文本
            evidence_list: 证据列表
            consistency_analysis: 一致性分析结果

        Returns:
            潜在伪证列表
        """
        potential_false_evidence = []

        # 使用LLM识别潜在伪证
        prompt = """请作为资深法律证据专家，识别以下案件中可能的伪证或不可靠证据。

案件事实：
{fact}

证据列表：
{evidence_text}

证据一致性分析：
{consistency_text}

请识别可能的伪证或不可靠证据，考虑以下因素：
1. 证据内容是否与大多数其他证据冲突
2. 证据是否包含不合理、不符合常识的内容
3. 证据来源是否可靠
4. 证据是否有明显的偏向性或主观性
5. 证据是否存在内部逻辑矛盾

请以JSON格式输出分析结果，格式如下：
```json
[
  {{
    "evidence_id": "E1",
    "false_probability": 0.7,
    "reasoning": "该证据与其他多个证据存在明显冲突，且内容不符合常识...",
    "verification_suggestions": ["建议核实证据来源", "建议寻找其他证据印证"]
  }},
  ...
]
```

请确保分析客观、公正、全面，并提供识别伪证的理由和验证建议。"""

        # 准备证据文本
        evidence_text = ""
        for evidence in evidence_list:
            evidence_text += f"{evidence.id}. [{evidence.type}] {evidence.content}\n"
            if evidence.source:
                evidence_text += f"   来源: {evidence.source}\n"
            evidence_text += "\n"

        # 准备一致性分析文本
        consistency_text = f"整体一致性: {consistency_analysis.get('overall_consistency', 0.0)}\n\n"
        conflict_pairs = consistency_analysis.get("conflict_pairs", [])
        if conflict_pairs:
            consistency_text += "冲突证据对:\n"
            for conflict in conflict_pairs:
                consistency_text += f"- {conflict.get('pair')}: 一致性 {conflict.get('consistency')}\n"
                consistency_text += f"  冲突描述: {conflict.get('conflict_description')}\n"

        # 获取LLM回复
        response = self.get_completion(prompt.format(
            fact=fact[:1000],
            evidence_text=evidence_text,
            consistency_text=consistency_text
        ), role="legal false evidence detector")

        # 解析回复
        json_match = re.search(r'```json\s*([\s\S]*?)\s*```', response)
        if json_match:
            json_str = json_match.group(1)
            try:
                false_evidence_data = json.loads(json_str)

                # 更新证据对象的伪证概率
                for item in false_evidence_data:
                    evidence_id = item.get("evidence_id")
                    false_probability = item.get("false_probability", 0.0)

                    for evidence in evidence_list:
                        if evidence.id == evidence_id:
                            evidence.false_probability = false_probability
                            break

                return false_evidence_data
            except json.JSONDecodeError:
                logger.error(f"JSON解析失败: {json_str}")
                # 使用备用方法
                return self._identify_potential_false_evidence_backup(evidence_list, consistency_analysis)
        else:
            # 使用备用方法
            return self._identify_potential_false_evidence_backup(evidence_list, consistency_analysis)

    def _identify_potential_false_evidence_backup(self, evidence_list: List[Evidence],
                                               consistency_analysis: Dict) -> List[Dict]:
        """
        备用的潜在伪证识别方法

        Args:
            evidence_list: 证据列表
            consistency_analysis: 一致性分析结果

        Returns:
            潜在伪证列表
        """
        potential_false_evidence = []

        # 基于一致性分析识别潜在伪证
        conflict_pairs = consistency_analysis.get("conflict_pairs", [])
        consistency_matrix = consistency_analysis.get("consistency_matrix", {})

        # 统计每个证据在冲突对中的出现次数
        conflict_counts = {}
        for evidence in evidence_list:
            conflict_counts[evidence.id] = 0

        for conflict in conflict_pairs:
            pair = conflict.get("pair", "")
            if "-" in pair:
                evidence1_id, evidence2_id = pair.split("-")
                conflict_counts[evidence1_id] = conflict_counts.get(evidence1_id, 0) + 1
                conflict_counts[evidence2_id] = conflict_counts.get(evidence2_id, 0) + 1

        # 识别冲突次数较多的证据
        for evidence in evidence_list:
            conflict_count = conflict_counts.get(evidence.id, 0)
            if conflict_count > len(evidence_list) / 3:  # 如果与超过1/3的其他证据冲突
                false_probability = min(0.9, conflict_count / len(evidence_list))
                evidence.false_probability = false_probability

                potential_false_evidence.append({
                    "evidence_id": evidence.id,
                    "false_probability": false_probability,
                    "reasoning": f"该证据与{conflict_count}个其他证据存在冲突",
                    "verification_suggestions": ["建议核实证据来源", "建议寻找其他证据印证"]
                })

        return potential_false_evidence

    def _evaluate_evidence_chain_completeness(self, fact: str, evidence_list: List[Evidence],
                                           evidence_weights: Dict[str, float]) -> float:
        """
        评估证据链的完整性

        Args:
            fact: 案件事实文本
            evidence_list: 证据列表
            evidence_weights: 证据权重字典

        Returns:
            证据链完整性评分（0-1）
        """
        # 使用LLM评估证据链完整性
        prompt = """请作为资深法律证据专家，评估以下案件的证据链完整性。

案件事实：
{fact}

证据列表：
{evidence_text}

请评估证据链的完整性，考虑以下因素：
1. 案件关键事实是否有充分证据支持
2. 证据链中是否存在薄弱环节或缺失部分
3. 证据之间是否形成相互印证的完整链条
4. 证据是否覆盖案件的各个关键方面（时间、地点、人物、行为、动机等）

请以JSON格式输出评估结果，格式如下：
```json
{{
  "completeness_score": 0.8,
  "key_facts_coverage": [
    {{
      "fact": "关键事实1",
      "covered": true,
      "supporting_evidence": ["E1", "E3"]
    }},
    {{
      "fact": "关键事实2",
      "covered": false,
      "missing_aspects": ["缺少关于动机的证据"]
    }},
    ...
  ],
  "weak_links": [
    {{
      "aspect": "薄弱环节描述",
      "suggestion": "加强建议"
    }},
    ...
  ],
  "overall_assessment": "总体评估..."
}}
```

请确保评估客观、公正、全面，并提供具体的分析和建议。"""

        # 准备证据文本
        evidence_text = ""
        for evidence in evidence_list:
            weight = evidence_weights.get(evidence.id, 0.0)
            evidence_text += f"{evidence.id}. [{evidence.type}] (权重: {weight:.2f}) {evidence.content}\n\n"

        # 获取LLM回复
        response = self.get_completion(prompt.format(fact=fact[:1000], evidence_text=evidence_text),
                                      role="legal evidence chain evaluator")

        # 解析回复
        json_match = re.search(r'```json\s*([\s\S]*?)\s*```', response)
        if json_match:
            json_str = json_match.group(1)
            try:
                completeness_data = json.loads(json_str)
                return completeness_data.get("completeness_score", 0.0)
            except json.JSONDecodeError:
                logger.error(f"JSON解析失败: {json_str}")
                # 使用备用方法
                return self._evaluate_evidence_chain_completeness_backup(evidence_list, evidence_weights)
        else:
            # 使用备用方法
            return self._evaluate_evidence_chain_completeness_backup(evidence_list, evidence_weights)

    def _evaluate_evidence_chain_completeness_backup(self, evidence_list: List[Evidence],
                                                  evidence_weights: Dict[str, float]) -> float:
        """
        备用的证据链完整性评估方法

        Args:
            evidence_list: 证据列表
            evidence_weights: 证据权重字典

        Returns:
            证据链完整性评分（0-1）
        """
        # 基于证据数量和权重评估完整性
        if not evidence_list:
            return 0.0

        # 计算加权平均权重
        total_weight = sum(evidence_weights.values())
        if total_weight == 0:
            return 0.5  # 默认中等完整性

        # 证据类型覆盖度
        evidence_types = set(evidence.type for evidence in evidence_list)
        type_coverage = len(evidence_types) / 5  # 假设理想情况下有5种类型

        # 证据数量充分度
        count_factor = min(1.0, len(evidence_list) / 10)  # 假设理想情况下有10个证据

        # 计算完整性评分
        completeness = (total_weight / len(evidence_list) * 0.5) + (type_coverage * 0.3) + (count_factor * 0.2)

        return completeness

    def _identify_key_evidence(self, evidence_list: List[Evidence], evidence_weights: Dict[str, float],
                             consistency_analysis: Dict) -> List[Dict]:
        """
        标记关键证据

        该函数使用通用的证据评估方法，基于多个因素综合评估证据的重要性，
        包括证据权重、伪证概率、一致性等。不针对特定案例进行特殊处理。

        Args:
            evidence_list: 证据列表
            evidence_weights: 证据权重字典
            consistency_analysis: 一致性分析结果

        Returns:
            关键证据列表
        """
        if not evidence_list:
            return []

        key_evidence = []

        # 计算各项指标的统计特征，用于后续归一化
        weights = list(evidence_weights.values())
        if not weights:
            return []

        # 计算权重统计特征
        weight_mean = sum(weights) / len(weights)
        weight_std = (sum((w - weight_mean) ** 2 for w in weights) / len(weights)) ** 0.5 if len(weights) > 1 else 0.1

        # 收集其他指标
        false_probs = [e.false_probability for e in evidence_list]
        consistencies = [e.consistency for e in evidence_list]
        reliabilities = [e.reliability for e in evidence_list]
        relevances = [e.relevance for e in evidence_list]

        # 使用Z-score标准化各项指标，避免使用固定阈值
        for evidence in evidence_list:
            weight = evidence_weights.get(evidence.id, 0.0)
            false_probability = evidence.false_probability
            consistency = evidence.consistency
            reliability = evidence.reliability
            relevance = evidence.relevance

            # Z-score标准化权重 (如果标准差为0，则使用原始值)
            if weight_std > 0:
                weight_z = (weight - weight_mean) / weight_std
            else:
                weight_z = weight

            # 计算综合评分 - 使用更平衡的权重分配
            # 权重因素占40%，可信度(1-伪证概率)占25%，一致性占15%，可靠性占10%，相关性占10%
            score = (
                weight_z * 0.4 +
                (1 - false_probability) * 0.25 +
                consistency * 0.15 +
                reliability * 0.1 +
                relevance * 0.1
            )

            # 使用相对评分而非固定阈值 - 自适应不同案例的证据质量
            # 如果评分为正(高于平均水平)或伪证概率较低，则视为关键证据
            if (weight_z > 0 or score > 0) and false_probability < 0.5:
                evidence.is_key_evidence = True

                key_evidence.append({
                    "evidence_id": evidence.id,
                    "type": evidence.type,
                    "content": evidence.content,
                    "weight": weight,
                    "reliability": reliability,
                    "relevance": relevance,
                    "consistency": consistency,
                    "false_probability": false_probability,
                    "weight_z_score": weight_z,
                    "overall_score": score
                })

        # 确保至少有一个关键证据 - 选择综合评分最高的证据
        if not key_evidence and evidence_list:
            # 计算所有证据的综合评分
            evidence_scores = []
            for evidence in evidence_list:
                weight = evidence_weights.get(evidence.id, 0.0)
                if weight_std > 0:
                    weight_z = (weight - weight_mean) / weight_std
                else:
                    weight_z = weight

                score = (
                    weight_z * 0.4 +
                    (1 - evidence.false_probability) * 0.25 +
                    evidence.consistency * 0.15 +
                    evidence.reliability * 0.1 +
                    evidence.relevance * 0.1
                )

                evidence_scores.append((evidence, score, weight_z))

            # 按综合评分排序
            sorted_evidence = sorted(evidence_scores, key=lambda x: x[1], reverse=True)
            if sorted_evidence:
                top_evidence, top_score, top_weight_z = sorted_evidence[0]
                top_evidence.is_key_evidence = True

                key_evidence.append({
                    "evidence_id": top_evidence.id,
                    "type": top_evidence.type,
                    "content": top_evidence.content,
                    "weight": evidence_weights.get(top_evidence.id, 0.0),
                    "reliability": top_evidence.reliability,
                    "relevance": top_evidence.relevance,
                    "consistency": top_evidence.consistency,
                    "false_probability": top_evidence.false_probability,
                    "weight_z_score": top_weight_z,
                    "overall_score": top_score,
                    "note": "自动选择的最高评分证据"
                })

        # 按评分排序
        key_evidence.sort(key=lambda x: x.get("overall_score", 0), reverse=True)

        return key_evidence

    def generate_evidence_analysis_summary(self, evidence_analysis_result: Dict) -> str:
        """
        生成证据分析总结

        Args:
            evidence_analysis_result: 证据分析结果

        Returns:
            证据分析总结
        """
        # 提取关键信息
        evidence_list = evidence_analysis_result.get("evidence_list", [])
        evidence_weights = evidence_analysis_result.get("evidence_weights", {})
        consistency_analysis = evidence_analysis_result.get("consistency_analysis", {})
        potential_false_evidence = evidence_analysis_result.get("potential_false_evidence", [])
        chain_completeness = evidence_analysis_result.get("chain_completeness", 0.0)
        key_evidence = evidence_analysis_result.get("key_evidence", [])

        # 构建提示词
        prompt = """请作为资深法律证据专家，根据以下证据分析结果，生成一份简明扼要的总结报告。

证据权重分析：
{weights_text}

证据一致性分析：
{consistency_text}

潜在伪证分析：
{false_evidence_text}

证据链完整性：
完整性评分: {chain_completeness:.2f}

关键证据：
{key_evidence_text}

请生成一份总结报告，包括：
1. 证据权重分析：说明哪些证据最重要，为什么重要
2. 证据一致性分析：说明证据之间的一致性如何，是否存在冲突
3. 潜在伪证分析：说明是否存在可能的伪证，如何识别和处理
4. 证据链评估：说明证据链的完整性和可靠性，是否存在薄弱环节
5. 建议：基于证据分析，提出进一步调查或证据收集的建议

请确保分析专业、客观、简明扼要。"""

        # 准备权重分析文本
        weights_text = ""
        for evidence in evidence_list:
            weight = evidence_weights.get(evidence["id"], 0.0)
            weights_text += f"{evidence['id']}. [{evidence['type']}] 权重: {weight:.2f}\n"
            weights_text += f"   内容: {evidence['content'][:100]}...\n\n"

        # 准备一致性分析文本
        consistency_text = f"整体一致性: {consistency_analysis.get('overall_consistency', 0.0):.2f}\n\n"
        conflict_pairs = consistency_analysis.get("conflict_pairs", [])
        if conflict_pairs:
            consistency_text += "冲突证据对:\n"
            for conflict in conflict_pairs[:3]:  # 最多显示3个冲突对
                consistency_text += f"- {conflict.get('pair')}: 一致性 {conflict.get('consistency'):.2f}\n"
                consistency_text += f"  冲突描述: {conflict.get('conflict_description')}\n"

        # 准备伪证分析文本
        false_evidence_text = ""
        if potential_false_evidence:
            for item in potential_false_evidence:
                false_evidence_text += f"{item.get('evidence_id')}. 伪证概率: {item.get('false_probability', 0.0):.2f}\n"
                false_evidence_text += f"   理由: {item.get('reasoning')}\n"
                false_evidence_text += f"   验证建议: {', '.join(item.get('verification_suggestions', []))}\n\n"
        else:
            false_evidence_text = "未发现明显的伪证。\n"

        # 准备关键证据文本
        key_evidence_text = ""
        if key_evidence:
            for item in key_evidence:
                key_evidence_text += f"{item.get('evidence_id')}. [{item.get('type')}] 权重: {item.get('weight', 0.0):.2f}\n"
                key_evidence_text += f"   内容: {item.get('content')[:100]}...\n\n"
        else:
            key_evidence_text = "未标记关键证据。\n"

        # 获取LLM回复
        response = self.get_completion(prompt.format(
            weights_text=weights_text,
            consistency_text=consistency_text,
            false_evidence_text=false_evidence_text,
            chain_completeness=chain_completeness,
            key_evidence_text=key_evidence_text
        ), role="legal evidence analyst")

        return response
