"""
判决抽取智能体 (SentenceExtractionAgent)

基于LLM的智能判决抽取专家，专门负责从司法文本中智能抽取判决信息，包括：
- 判决结果（罪名认定、刑罚种类）
- 量刑依据（量刑理由、适用条文）
- 判决理由（事实认定、法律适用理由）
- 执行方式（立即执行、缓刑、假释等）

核心特色：
1. 基于GPT-4的智能判决理解和信息抽取
2. 继承统一的智能体基类架构
3. 专业化的判决分析和逻辑推理能力
4. 智能化的判决准确性验证和一致性检查
5. 与证据权重分析系统的深度集成
"""

import logging
import json
import re
from typing import Dict, List

# 导入基类和相关类型
from .base_agent import BaseExtractionAgent
from extraction_types import ExtractionTaskType, InformationType, ExtractionResult

# 配置日志
logger = logging.getLogger(__name__)


class SentenceExtractionAgent(BaseExtractionAgent):
    """基于LLM的判决抽取智能体 - 专门负责判决信息的智能抽取"""

    def __init__(self, legal_knowledge_base: Dict = None):
        super().__init__("llm_sentence_extractor", legal_knowledge_base)
        
        # 专业化领域：判决信息
        self.specialization = [
            InformationType.SENTENCE,
            InformationType.IMPRISONMENT,
            InformationType.JUDGMENT_REASONING,
            InformationType.EXECUTION_METHOD
        ]
        
        # 扩展性能统计
        self.performance_stats.update({
            "sentence_accuracy_scores": [],
            "imprisonment_extractions": 0,
            "reasoning_extractions": 0,
            "execution_method_extractions": 0
        })
        
        logger.info(f"基于LLM的判决抽取智能体 {self.agent_id} 初始化完成")

    def extract(self, text: str, task_type: ExtractionTaskType) -> List[ExtractionResult]:
        """
        执行基于LLM的智能判决抽取

        Args:
            text: 待抽取的司法文本
            task_type: 抽取任务类型

        Returns:
            判决抽取结果列表
        """
        logger.info(f"开始基于LLM的判决抽取，文本长度: {len(text)} 字符")

        try:
            # 构建LLM判决抽取提示
            prompt = self._build_extraction_prompt(text, task_type)

            # 调用LLM进行判决抽取
            logger.info("调用LLM进行判决抽取...")
            response = self._call_llm(prompt, "judicial sentence extraction expert")

            # 解析LLM响应
            parsed_results = self._parse_llm_response(response, text)

            # 转换为ExtractionResult对象
            extraction_results = self._convert_to_extraction_results(parsed_results, text)

            # 后处理：判决准确性验证和质量评估
            validated_results = self._validate_sentence_accuracy(extraction_results, text)

            # 更新性能统计
            self._update_performance_stats(validated_results)

            logger.info(f"基于LLM的判决抽取完成，共抽取 {len(validated_results)} 个判决要素")
            return validated_results

        except Exception as e:
            logger.error(f"基于LLM的判决抽取过程出错: {e}")
            return []

    def _build_extraction_prompt(self, text: str, task_type: ExtractionTaskType) -> str:
        """构建LLM判决抽取提示"""
        
        prompt = f"""作为一名专业的司法判决抽取专家，请从以下司法文本中准确抽取判决相关信息。

【司法文本】
{text}

【抽取任务】
请从上述文本中抽取以下类型的判决信息：

1. **判决结果**：
   - 具体的判决内容和刑罚
   - 罪名认定结果
   - 刑罚种类和期限

2. **刑期信息**：
   - 有期徒刑、拘役、管制等期限
   - 罚金数额
   - 缓刑期限

3. **判决理由**：
   - 事实认定的理由
   - 法律适用的理由
   - 量刑考虑的因素

4. **执行方式**：
   - 立即执行、缓刑、假释等
   - 执行的具体安排
   - 特殊执行条件

【抽取要求】
1. **准确性**：确保判决信息的准确性，严格基于文本内容
2. **完整性**：尽可能抽取所有相关的判决信息
3. **规范性**：使用标准的法律术语和格式
4. **一致性**：确保抽取的信息之间逻辑一致

【输出格式】
请严格按照以下JSON格式输出抽取结果：
{{
    "sentences": [
        {{
            "verdict": "判决结果",
            "charge": "认定罪名",
            "punishment": "刑罚内容",
            "confidence": 0.95
        }}
    ],
    "imprisonments": [
        {{
            "duration": "刑期描述",
            "amount_months": 刑期月数,
            "type": "有期徒刑/拘役/管制",
            "confidence": 0.90
        }}
    ],
    "judgment_reasoning": [
        {{
            "reasoning_type": "事实认定/法律适用/量刑考虑",
            "content": "推理内容",
            "legal_basis": "法律依据",
            "confidence": 0.85
        }}
    ],
    "execution_methods": [
        {{
            "method": "执行方式",
            "description": "执行描述",
            "conditions": "执行条件",
            "confidence": 0.80
        }}
    ]
}}

【注意事项】
1. 如果某类判决信息在文本中不存在，请返回空数组[]
2. 刑期月数请转换为数字格式（如"六个月"转换为6）
3. 置信度应根据文本中信息的明确程度进行评估
4. 只返回JSON格式的结果，不要添加其他说明文字

请开始抽取："""

        return prompt

    def _convert_to_extraction_results(self, parsed_data: Dict, original_text: str) -> List[ExtractionResult]:
        """将解析后的数据转换为ExtractionResult对象"""
        results = []
        
        # 处理判决结果
        sentences = parsed_data.get("sentences", [])
        for sentence in sentences:
            if sentence and sentence.get("verdict"):
                confidence = sentence.get("confidence", 0.8)
                evidence_weight = self._calculate_evidence_weight(sentence.get("verdict", ""), InformationType.SENTENCE)
                source_span = self._find_source_span(sentence.get("verdict", ""), original_text)
                context = self._extract_context(sentence.get("verdict", ""), original_text)

                result = ExtractionResult(
                    info_type=InformationType.SENTENCE,
                    content=sentence.get("verdict", ""),
                    confidence=confidence,
                    evidence_weight=evidence_weight,
                    source_span=source_span,
                    context=context,
                    agent_id=self.agent_id,
                    metadata={
                        "extraction_method": "llm_based",
                        "sentence_category": "sentence",
                        "charge": sentence.get("charge", ""),
                        "punishment": sentence.get("punishment", "")
                    }
                )
                results.append(result)

        # 处理刑期信息
        imprisonments = parsed_data.get("imprisonments", [])
        for imprisonment in imprisonments:
            if imprisonment and imprisonment.get("duration"):
                confidence = imprisonment.get("confidence", 0.8)
                evidence_weight = self._calculate_evidence_weight(imprisonment.get("duration", ""), InformationType.IMPRISONMENT)
                source_span = self._find_source_span(imprisonment.get("duration", ""), original_text)
                context = self._extract_context(imprisonment.get("duration", ""), original_text)

                result = ExtractionResult(
                    info_type=InformationType.IMPRISONMENT,
                    content=imprisonment.get("duration", ""),
                    confidence=confidence,
                    evidence_weight=evidence_weight,
                    source_span=source_span,
                    context=context,
                    agent_id=self.agent_id,
                    metadata={
                        "extraction_method": "llm_based",
                        "sentence_category": "imprisonment",
                        "amount_months": imprisonment.get("amount_months", 0),
                        "type": imprisonment.get("type", "")
                    }
                )
                results.append(result)

        # 处理判决理由
        judgment_reasoning = parsed_data.get("judgment_reasoning", [])
        for reasoning in judgment_reasoning:
            if reasoning and reasoning.get("content"):
                confidence = reasoning.get("confidence", 0.8)
                evidence_weight = self._calculate_evidence_weight(reasoning.get("content", ""), InformationType.JUDGMENT_REASONING)
                source_span = self._find_source_span(reasoning.get("content", ""), original_text)
                context = self._extract_context(reasoning.get("content", ""), original_text)

                result = ExtractionResult(
                    info_type=InformationType.JUDGMENT_REASONING,
                    content=reasoning.get("content", ""),
                    confidence=confidence,
                    evidence_weight=evidence_weight,
                    source_span=source_span,
                    context=context,
                    agent_id=self.agent_id,
                    metadata={
                        "extraction_method": "llm_based",
                        "sentence_category": "judgment_reasoning",
                        "reasoning_type": reasoning.get("reasoning_type", ""),
                        "legal_basis": reasoning.get("legal_basis", "")
                    }
                )
                results.append(result)

        # 处理执行方式
        execution_methods = parsed_data.get("execution_methods", [])
        for method in execution_methods:
            if method and method.get("method"):
                confidence = method.get("confidence", 0.8)
                evidence_weight = self._calculate_evidence_weight(method.get("method", ""), InformationType.EXECUTION_METHOD)
                source_span = self._find_source_span(method.get("method", ""), original_text)
                context = self._extract_context(method.get("method", ""), original_text)

                result = ExtractionResult(
                    info_type=InformationType.EXECUTION_METHOD,
                    content=method.get("method", ""),
                    confidence=confidence,
                    evidence_weight=evidence_weight,
                    source_span=source_span,
                    context=context,
                    agent_id=self.agent_id,
                    metadata={
                        "extraction_method": "llm_based",
                        "sentence_category": "execution_method",
                        "description": method.get("description", ""),
                        "conditions": method.get("conditions", "")
                    }
                )
                results.append(result)

        return results

    def _validate_sentence_accuracy(self, results: List[ExtractionResult], text: str) -> List[ExtractionResult]:
        """验证判决准确性"""
        # 计算判决准确性分数
        accuracy_score = self._calculate_sentence_accuracy_score(results, text)
        self.performance_stats["sentence_accuracy_scores"].append(accuracy_score)
        
        # 过滤低质量结果
        validated_results = []
        for result in results:
            if self._is_sentence_valid(result):
                validated_results.append(result)
        
        # 一致性检查
        validated_results = self._check_sentence_consistency(validated_results)
        
        logger.info(f"判决准确性验证完成，保留 {len(validated_results)}/{len(results)} 个结果")
        return validated_results

    def _calculate_sentence_accuracy_score(self, results: List[ExtractionResult], text: str) -> float:
        """计算判决准确性分数"""
        if not results:
            return 0.0
        
        # 基于置信度和证据权重计算
        total_score = 0.0
        for result in results:
            score = 0.6 * result.confidence + 0.4 * result.evidence_weight
            total_score += score
        
        return total_score / len(results)

    def _is_sentence_valid(self, result: ExtractionResult) -> bool:
        """检查判决信息的有效性"""
        # 基本质量检查
        if len(result.content) < 2:
            return False
        
        # 置信度阈值
        if result.confidence < 0.3:
            return False
        
        # 判决术语合理性检查
        sentence_keywords = ["判", "决", "刑", "罚", "徒刑", "拘役", "管制", "罚金", "缓刑"]
        if result.info_type in [InformationType.SENTENCE, InformationType.IMPRISONMENT]:
            if not any(keyword in result.content for keyword in sentence_keywords):
                return False
        
        return True

    def _check_sentence_consistency(self, results: List[ExtractionResult]) -> List[ExtractionResult]:
        """检查判决信息的一致性"""
        # 检查刑期信息的一致性
        imprisonment_results = [r for r in results if r.info_type == InformationType.IMPRISONMENT]
        sentence_results = [r for r in results if r.info_type == InformationType.SENTENCE]
        
        # 如果有多个刑期信息，选择置信度最高的
        if len(imprisonment_results) > 1:
            imprisonment_results.sort(key=lambda x: x.confidence, reverse=True)
            # 保留置信度最高的刑期信息
            best_imprisonment = imprisonment_results[0]
            results = [r for r in results if r.info_type != InformationType.IMPRISONMENT]
            results.append(best_imprisonment)
        
        return results

    def _extract_imprisonment_months(self, duration_text: str) -> int:
        """从刑期文本中提取月数"""
        try:
            # 匹配年份
            year_match = re.search(r'(\d+)年', duration_text)
            years = int(year_match.group(1)) if year_match else 0
            
            # 匹配月份
            month_match = re.search(r'(\d+)个?月', duration_text)
            months = int(month_match.group(1)) if month_match else 0
            
            # 匹配中文数字
            chinese_numbers = {
                '一': 1, '二': 2, '三': 3, '四': 4, '五': 5, '六': 6,
                '七': 7, '八': 8, '九': 9, '十': 10, '十一': 11, '十二': 12
            }
            
            for chinese, number in chinese_numbers.items():
                if f'{chinese}年' in duration_text:
                    years = number
                if f'{chinese}个月' in duration_text or f'{chinese}月' in duration_text:
                    months = number
            
            return years * 12 + months
            
        except Exception as e:
            logger.warning(f"刑期月数提取失败: {e}")
            return 0
