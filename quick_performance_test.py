#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Quick Performance Test for Optimization
Test the performance improvement of parallel processing and caching system
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import time
from judicial_ie_coordinator import JudicialIECoordinator, ExtractionTaskType

def test_basic_performance():
    """Test basic performance optimization"""
    print("Quick Performance Optimization Test")
    print("="*60)
    
    # Test case
    test_text = "Defendant <PERSON> hit <PERSON>'s head with a wooden stick, causing minor injury."
    
    print(f"Test text: {test_text}")
    print(f"Text length: {len(test_text)} characters")
    
    # Test 1: Serial processing (baseline)
    print("\nTest 1: Serial Processing (Baseline)")
    print("-" * 40)
    
    coordinator_serial = JudicialIECoordinator(enable_parallel=False, enable_cache=False)
    
    start_time = time.time()
    result_serial = coordinator_serial.extract_information(test_text, ExtractionTaskType.COMPREHENSIVE_EXTRACTION)
    serial_time = time.time() - start_time
    
    print(f"Execution time: {serial_time:.2f}s")
    print(f"Results count: {len(result_serial.get('results', {}).get('detailed_results', []))}")
    print(f"Status: {result_serial.get('status', 'unknown')}")
    
    # Test 2: Parallel processing
    print("\nTest 2: Parallel Processing")
    print("-" * 40)
    
    coordinator_parallel = JudicialIECoordinator(enable_parallel=True, enable_cache=False)
    
    start_time = time.time()
    result_parallel = coordinator_parallel.extract_information(test_text, ExtractionTaskType.COMPREHENSIVE_EXTRACTION)
    parallel_time = time.time() - start_time
    
    print(f"Execution time: {parallel_time:.2f}s")
    print(f"Results count: {len(result_parallel.get('results', {}).get('detailed_results', []))}")
    print(f"Status: {result_parallel.get('status', 'unknown')}")
    
    # Test 3: Parallel + Cache
    print("\nTest 3: Parallel + Cache")
    print("-" * 40)
    
    coordinator_optimized = JudicialIECoordinator(enable_parallel=True, enable_cache=True)
    
    # First run (build cache)
    print("First run (building cache):")
    start_time = time.time()
    result_opt1 = coordinator_optimized.extract_information(test_text, ExtractionTaskType.COMPREHENSIVE_EXTRACTION)
    opt_time1 = time.time() - start_time
    print(f"Execution time: {opt_time1:.2f}s")
    
    # Second run (use cache)
    print("Second run (using cache):")
    start_time = time.time()
    result_opt2 = coordinator_optimized.extract_information(test_text, ExtractionTaskType.COMPREHENSIVE_EXTRACTION)
    opt_time2 = time.time() - start_time
    print(f"Execution time: {opt_time2:.2f}s")
    print(f"Results count: {len(result_opt2.get('results', {}).get('detailed_results', []))}")
    print(f"Status: {result_opt2.get('status', 'unknown')}")
    
    # Performance analysis
    print("\nPerformance Analysis")
    print("="*60)
    
    if parallel_time < serial_time:
        parallel_improvement = (serial_time - parallel_time) / serial_time * 100
        print(f"Parallel improvement: {parallel_improvement:.1f}%")
    else:
        print("Parallel processing did not improve performance for this case")
    
    if opt_time2 < serial_time:
        cache_improvement = (serial_time - opt_time2) / serial_time * 100
        print(f"Cache improvement: {cache_improvement:.1f}%")
    else:
        print("Cache did not improve performance for this case")
    
    # Goal achievement
    print(f"\nGoal Achievement:")
    best_time = min(parallel_time, opt_time2)
    if best_time < 3.0:
        print(f"SUCCESS: Best time {best_time:.2f}s < 3s target")
    else:
        print(f"PARTIAL: Best time {best_time:.2f}s, approaching 3s target")
    
    # Get performance report
    try:
        performance_report = coordinator_optimized.get_comprehensive_performance_report()
        print(f"\nDetailed Performance Report:")
        
        cache_perf = performance_report.get('cache_performance', {})
        if cache_perf:
            print(f"Cache hit rate: {cache_perf.get('hit_rate', 0):.1%}")
            print(f"Total requests: {cache_perf.get('total_requests', 0)}")
            print(f"Cache hits: {cache_perf.get('cache_hits', 0)}")
        
        extraction_metrics = performance_report.get('extraction_metrics', {})
        if extraction_metrics:
            print(f"Parallel extractions: {extraction_metrics.get('parallel_extractions', 0)}")
            print(f"Time saved: {extraction_metrics.get('time_saved', 0):.2f}s")
    except Exception as e:
        print(f"Could not get performance report: {e}")
    
    # Cleanup
    coordinator_serial.cleanup()
    coordinator_parallel.cleanup()
    coordinator_optimized.cleanup()
    
    return {
        "serial_time": serial_time,
        "parallel_time": parallel_time,
        "optimized_time": opt_time2,
        "best_time": best_time
    }

def test_multiple_cases():
    """Test with multiple cases"""
    print("\nMultiple Cases Test")
    print("="*60)
    
    test_cases = [
        "Defendant Zhang hit Li's head with a wooden stick.",
        "Defendant Wang committed fraud against victim Li.",
        "Defendant Liu stole property from the store.",
        "Defendant Chen drove under the influence of alcohol.",
        "Defendant Zhao sold counterfeit goods to customers."
    ]
    
    coordinator = JudicialIECoordinator(enable_parallel=True, enable_cache=True)
    
    total_time = 0
    successful_cases = 0
    
    for i, text in enumerate(test_cases, 1):
        print(f"\nCase {i}: {text[:50]}...")
        
        start_time = time.time()
        try:
            result = coordinator.extract_information(text, ExtractionTaskType.COMPREHENSIVE_EXTRACTION)
            case_time = time.time() - start_time
            total_time += case_time
            
            if result.get('status') == 'success':
                successful_cases += 1
                print(f"SUCCESS - {case_time:.2f}s")
            else:
                print(f"FAILED - {case_time:.2f}s")
        except Exception as e:
            case_time = time.time() - start_time
            total_time += case_time
            print(f"ERROR - {case_time:.2f}s: {e}")
    
    # Summary
    print(f"\nBatch Processing Summary:")
    print(f"Total cases: {len(test_cases)}")
    print(f"Successful cases: {successful_cases}")
    print(f"Total time: {total_time:.2f}s")
    print(f"Average time: {total_time/len(test_cases):.2f}s/case")
    print(f"Success rate: {successful_cases/len(test_cases)*100:.1f}%")
    
    # Throughput
    throughput = len(test_cases) / total_time * 3600  # cases/hour
    print(f"Throughput: {throughput:.1f} cases/hour")
    
    coordinator.cleanup()

def main():
    """Main test function"""
    print("Multi-Agent Judicial Information Extraction System")
    print("Performance Optimization Test")
    print("Target: Optimize execution time from 4.98s to <3s")
    print("="*80)
    
    try:
        # Basic performance test
        basic_results = test_basic_performance()
        
        # Multiple cases test
        test_multiple_cases()
        
        print("\nPerformance optimization test completed!")
        
        # Final summary
        print(f"\nFinal Summary:")
        print(f"Best execution time achieved: {basic_results['best_time']:.2f}s")
        
        if basic_results['best_time'] < 3.0:
            print("TARGET ACHIEVED: Execution time < 3s")
        elif basic_results['best_time'] < 4.0:
            print("GOOD PROGRESS: Significant improvement achieved")
        else:
            print("NEEDS WORK: Further optimization required")
            
    except Exception as e:
        print(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
