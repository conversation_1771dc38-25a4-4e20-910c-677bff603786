#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的评估指标体系
包括传统指标和创新指标（证据权重利用率、冲突解决率等）

目标：为学术论文提供全面的评估框架
"""

import numpy as np
import json
import time
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass, asdict
from collections import defaultdict, Counter
import logging

# 导入共享类型
from extraction_types import ExtractionResult, InformationType

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class EvaluationMetrics:
    """评估指标数据类"""
    # 传统指标
    precision: float
    recall: float
    f1_score: float
    accuracy: float
    
    # 效率指标
    extraction_time: float
    throughput: float  # 案例/小时
    
    # 创新指标
    evidence_weight_utilization: float
    conflict_resolution_rate: float
    completeness_score: float
    consistency_score: float
    
    # 详细统计
    total_extractions: int
    correct_extractions: int
    missed_extractions: int
    false_extractions: int
    
    # 分类指标
    entity_metrics: Dict[str, float]
    fact_metrics: Dict[str, float]
    legal_metrics: Dict[str, float]
    sentence_metrics: Dict[str, float]

class InnovationMetricsCalculator:
    """创新指标计算器"""
    
    def __init__(self):
        self.name = "innovation_metrics"
    
    def calculate_evidence_weight_utilization(self, results: List[ExtractionResult]) -> float:
        """计算证据权重利用率（创新指标）"""
        if not results:
            return 0.0
        
        # 计算证据权重的有效利用程度
        total_weight = sum(r.evidence_weight for r in results)
        max_possible_weight = len(results) * 1.0  # 假设最大权重为1.0
        
        utilization = total_weight / max_possible_weight if max_possible_weight > 0 else 0.0
        
        # 考虑权重分布的合理性
        weights = [r.evidence_weight for r in results]
        weight_variance = np.var(weights) if len(weights) > 1 else 0.0
        
        # 权重分布越合理（有区分度），利用率越高
        distribution_factor = min(1.0, weight_variance * 2)  # 调整因子
        
        final_utilization = utilization * (0.7 + 0.3 * distribution_factor)
        
        return min(1.0, final_utilization)
    
    def calculate_conflict_resolution_rate(self, extraction_log: Dict[str, Any]) -> float:
        """计算冲突解决率（创新指标）"""
        conflicts_detected = extraction_log.get('conflicts_detected', 0)
        conflicts_resolved = extraction_log.get('conflicts_resolved', 0)
        
        if conflicts_detected == 0:
            return 1.0  # 无冲突情况下认为解决率为100%
        
        resolution_rate = conflicts_resolved / conflicts_detected
        return min(1.0, resolution_rate)
    
    def calculate_completeness_score(self, results: List[ExtractionResult], 
                                   expected_types: List[InformationType]) -> float:
        """计算完整性分数（创新指标）"""
        if not expected_types:
            return 1.0
        
        extracted_types = {r.info_type for r in results}
        expected_types_set = set(expected_types)
        
        # 基础完整性
        basic_completeness = len(extracted_types.intersection(expected_types_set)) / len(expected_types_set)
        
        # 考虑每种类型的抽取质量
        type_quality_scores = []
        for info_type in expected_types_set:
            type_results = [r for r in results if r.info_type == info_type]
            if type_results:
                avg_confidence = sum(r.confidence for r in type_results) / len(type_results)
                type_quality_scores.append(avg_confidence)
            else:
                type_quality_scores.append(0.0)
        
        quality_factor = np.mean(type_quality_scores) if type_quality_scores else 0.0
        
        # 综合完整性分数
        completeness = 0.6 * basic_completeness + 0.4 * quality_factor
        
        return min(1.0, completeness)
    
    def calculate_consistency_score(self, results: List[ExtractionResult]) -> float:
        """计算一致性分数（创新指标）"""
        if len(results) < 2:
            return 1.0
        
        # 按信息类型分组
        type_groups = defaultdict(list)
        for result in results:
            type_groups[result.info_type].append(result)
        
        consistency_scores = []
        
        for info_type, group_results in type_groups.items():
            if len(group_results) < 2:
                consistency_scores.append(1.0)
                continue
            
            # 计算置信度一致性
            confidences = [r.confidence for r in group_results]
            confidence_variance = np.var(confidences)
            confidence_consistency = max(0.0, 1.0 - confidence_variance)
            
            # 计算证据权重一致性
            weights = [r.evidence_weight for r in group_results]
            weight_variance = np.var(weights)
            weight_consistency = max(0.0, 1.0 - weight_variance)
            
            # 综合一致性
            group_consistency = 0.5 * confidence_consistency + 0.5 * weight_consistency
            consistency_scores.append(group_consistency)
        
        return np.mean(consistency_scores) if consistency_scores else 1.0

class ComprehensiveEvaluator:
    """综合评估器"""
    
    def __init__(self):
        self.innovation_calculator = InnovationMetricsCalculator()
        self.evaluation_history = []
    
    def evaluate_extraction_results(self, 
                                  predicted_results: List[ExtractionResult],
                                  ground_truth: List[Dict[str, Any]],
                                  extraction_time: float,
                                  extraction_log: Optional[Dict[str, Any]] = None) -> EvaluationMetrics:
        """综合评估抽取结果"""
        
        # 计算传统指标
        traditional_metrics = self._calculate_traditional_metrics(predicted_results, ground_truth)
        
        # 计算效率指标
        efficiency_metrics = self._calculate_efficiency_metrics(predicted_results, extraction_time)
        
        # 计算创新指标
        innovation_metrics = self._calculate_innovation_metrics(
            predicted_results, ground_truth, extraction_log or {}
        )
        
        # 计算分类指标
        category_metrics = self._calculate_category_metrics(predicted_results, ground_truth)
        
        # 构建综合评估结果
        evaluation = EvaluationMetrics(
            # 传统指标
            precision=traditional_metrics['precision'],
            recall=traditional_metrics['recall'],
            f1_score=traditional_metrics['f1_score'],
            accuracy=traditional_metrics['accuracy'],
            
            # 效率指标
            extraction_time=extraction_time,
            throughput=efficiency_metrics['throughput'],
            
            # 创新指标
            evidence_weight_utilization=innovation_metrics['evidence_weight_utilization'],
            conflict_resolution_rate=innovation_metrics['conflict_resolution_rate'],
            completeness_score=innovation_metrics['completeness_score'],
            consistency_score=innovation_metrics['consistency_score'],
            
            # 详细统计
            total_extractions=len(predicted_results),
            correct_extractions=traditional_metrics['correct_extractions'],
            missed_extractions=traditional_metrics['missed_extractions'],
            false_extractions=traditional_metrics['false_extractions'],
            
            # 分类指标
            entity_metrics=category_metrics['entity_metrics'],
            fact_metrics=category_metrics['fact_metrics'],
            legal_metrics=category_metrics['legal_metrics'],
            sentence_metrics=category_metrics['sentence_metrics']
        )
        
        # 记录评估历史
        self.evaluation_history.append(evaluation)
        
        return evaluation
    
    def _calculate_traditional_metrics(self, predicted: List[ExtractionResult], 
                                     ground_truth: List[Dict[str, Any]]) -> Dict[str, float]:
        """计算传统评估指标"""
        if not ground_truth:
            return {
                'precision': 0.0, 'recall': 0.0, 'f1_score': 0.0, 'accuracy': 0.0,
                'correct_extractions': 0, 'missed_extractions': 0, 'false_extractions': 0
            }
        
        # 构建预测集合和真实集合
        predicted_set = {(r.info_type.value, r.content.lower().strip()) for r in predicted}
        truth_set = {(gt.get('type', ''), gt.get('content', '').lower().strip()) for gt in ground_truth}
        
        # 计算交集
        intersection = predicted_set.intersection(truth_set)
        
        # 计算指标
        correct_extractions = len(intersection)
        false_extractions = len(predicted_set) - correct_extractions
        missed_extractions = len(truth_set) - correct_extractions
        
        precision = correct_extractions / len(predicted_set) if predicted_set else 0.0
        recall = correct_extractions / len(truth_set) if truth_set else 0.0
        f1_score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0.0
        accuracy = correct_extractions / max(len(predicted_set), len(truth_set)) if max(len(predicted_set), len(truth_set)) > 0 else 0.0
        
        return {
            'precision': precision,
            'recall': recall,
            'f1_score': f1_score,
            'accuracy': accuracy,
            'correct_extractions': correct_extractions,
            'missed_extractions': missed_extractions,
            'false_extractions': false_extractions
        }
    
    def _calculate_efficiency_metrics(self, results: List[ExtractionResult], 
                                    extraction_time: float) -> Dict[str, float]:
        """计算效率指标"""
        throughput = 3600 / extraction_time if extraction_time > 0 else 0.0  # 案例/小时
        
        return {
            'throughput': throughput
        }
    
    def _calculate_innovation_metrics(self, results: List[ExtractionResult],
                                    ground_truth: List[Dict[str, Any]],
                                    extraction_log: Dict[str, Any]) -> Dict[str, float]:
        """计算创新指标"""
        # 证据权重利用率
        evidence_weight_utilization = self.innovation_calculator.calculate_evidence_weight_utilization(results)
        
        # 冲突解决率
        conflict_resolution_rate = self.innovation_calculator.calculate_conflict_resolution_rate(extraction_log)
        
        # 完整性分数
        expected_types = [InformationType.DEFENDANT, InformationType.VICTIM, InformationType.CASE_FACTS, InformationType.CHARGES]
        completeness_score = self.innovation_calculator.calculate_completeness_score(results, expected_types)
        
        # 一致性分数
        consistency_score = self.innovation_calculator.calculate_consistency_score(results)
        
        return {
            'evidence_weight_utilization': evidence_weight_utilization,
            'conflict_resolution_rate': conflict_resolution_rate,
            'completeness_score': completeness_score,
            'consistency_score': consistency_score
        }
    
    def _calculate_category_metrics(self, results: List[ExtractionResult],
                                  ground_truth: List[Dict[str, Any]]) -> Dict[str, Dict[str, float]]:
        """计算分类指标"""
        categories = {
            'entity': [InformationType.DEFENDANT, InformationType.VICTIM, InformationType.WITNESS],
            'fact': [InformationType.CASE_FACTS, InformationType.EVIDENCE],
            'legal': [InformationType.CHARGES, InformationType.LEGAL_ARTICLES],
            'sentence': [InformationType.IMPRISONMENT, InformationType.FINE, InformationType.PROBATION]
        }
        
        category_metrics = {}
        
        for category, info_types in categories.items():
            category_results = [r for r in results if r.info_type in info_types]
            category_truth = [gt for gt in ground_truth if gt.get('category') == category]
            
            if category_results or category_truth:
                metrics = self._calculate_traditional_metrics(category_results, category_truth)
                category_metrics[f"{category}_metrics"] = {
                    'precision': metrics['precision'],
                    'recall': metrics['recall'],
                    'f1_score': metrics['f1_score']
                }
            else:
                category_metrics[f"{category}_metrics"] = {
                    'precision': 0.0,
                    'recall': 0.0,
                    'f1_score': 0.0
                }
        
        return category_metrics
    
    def generate_evaluation_report(self, evaluation: EvaluationMetrics) -> str:
        """生成评估报告"""
        report = "# 司法信息抽取系统评估报告\n\n"
        
        # 传统指标
        report += "## 传统评估指标\n\n"
        report += f"- **精确率 (Precision)**: {evaluation.precision:.3f}\n"
        report += f"- **召回率 (Recall)**: {evaluation.recall:.3f}\n"
        report += f"- **F1分数**: {evaluation.f1_score:.3f}\n"
        report += f"- **准确率 (Accuracy)**: {evaluation.accuracy:.3f}\n\n"
        
        # 效率指标
        report += "## 效率指标\n\n"
        report += f"- **抽取时间**: {evaluation.extraction_time:.2f}秒\n"
        report += f"- **处理吞吐量**: {evaluation.throughput:.1f}案例/小时\n\n"
        
        # 创新指标
        report += "## 创新指标\n\n"
        report += f"- **证据权重利用率**: {evaluation.evidence_weight_utilization:.3f}\n"
        report += f"- **冲突解决率**: {evaluation.conflict_resolution_rate:.3f}\n"
        report += f"- **完整性分数**: {evaluation.completeness_score:.3f}\n"
        report += f"- **一致性分数**: {evaluation.consistency_score:.3f}\n\n"
        
        # 详细统计
        report += "## 详细统计\n\n"
        report += f"- **总抽取数量**: {evaluation.total_extractions}\n"
        report += f"- **正确抽取**: {evaluation.correct_extractions}\n"
        report += f"- **遗漏抽取**: {evaluation.missed_extractions}\n"
        report += f"- **错误抽取**: {evaluation.false_extractions}\n\n"
        
        # 分类指标
        report += "## 分类指标\n\n"
        categories = ['entity', 'fact', 'legal', 'sentence']
        for category in categories:
            metrics = getattr(evaluation, f"{category}_metrics")
            report += f"### {category.title()}类指标\n"
            report += f"- 精确率: {metrics['precision']:.3f}\n"
            report += f"- 召回率: {metrics['recall']:.3f}\n"
            report += f"- F1分数: {metrics['f1_score']:.3f}\n\n"
        
        return report
    
    def compare_methods(self, evaluations: Dict[str, EvaluationMetrics]) -> str:
        """对比多种方法的评估结果"""
        report = "# 方法对比评估报告\n\n"
        
        # 主要指标对比表
        report += "## 主要指标对比\n\n"
        report += "| 方法 | F1分数 | 精确率 | 召回率 | 抽取时间(s) | 证据权重利用率 |\n"
        report += "|------|--------|--------|--------|-------------|----------------|\n"
        
        for method_name, evaluation in evaluations.items():
            report += f"| {method_name} | {evaluation.f1_score:.3f} | {evaluation.precision:.3f} | {evaluation.recall:.3f} | {evaluation.extraction_time:.2f} | {evaluation.evidence_weight_utilization:.3f} |\n"
        
        # 创新指标对比
        report += "\n## 创新指标对比\n\n"
        report += "| 方法 | 冲突解决率 | 完整性分数 | 一致性分数 | 吞吐量(案例/h) |\n"
        report += "|------|------------|------------|------------|----------------|\n"
        
        for method_name, evaluation in evaluations.items():
            report += f"| {method_name} | {evaluation.conflict_resolution_rate:.3f} | {evaluation.completeness_score:.3f} | {evaluation.consistency_score:.3f} | {evaluation.throughput:.1f} |\n"
        
        return report

# 测试函数
def test_evaluation_system():
    """测试评估系统"""
    print("评估指标体系测试")
    print("="*50)
    
    # 模拟抽取结果
    predicted_results = [
        ExtractionResult(
            info_type=InformationType.DEFENDANT,
            content="张某",
            confidence=0.9,
            evidence_weight=0.8,
            source_span=(0, 2),
            context="被告人张某",
            agent_id="test_agent"
        ),
        ExtractionResult(
            info_type=InformationType.VICTIM,
            content="李某",
            confidence=0.85,
            evidence_weight=0.75,
            source_span=(10, 12),
            context="被害人李某",
            agent_id="test_agent"
        )
    ]
    
    # 模拟真实标注
    ground_truth = [
        {"type": "defendant", "content": "张某", "category": "entity"},
        {"type": "victim", "content": "李某", "category": "entity"},
        {"type": "case_facts", "content": "持木棒击打", "category": "fact"}
    ]
    
    # 模拟抽取日志
    extraction_log = {
        "conflicts_detected": 2,
        "conflicts_resolved": 2,
        "evidence_guided_improvements": 3
    }
    
    # 创建评估器
    evaluator = ComprehensiveEvaluator()
    
    # 进行评估
    evaluation = evaluator.evaluate_extraction_results(
        predicted_results, ground_truth, 3.5, extraction_log
    )
    
    # 生成报告
    report = evaluator.generate_evaluation_report(evaluation)
    print(report)
    
    return evaluation

if __name__ == "__main__":
    test_evaluation_system()
