"""
工具函数模块 - 包含各种辅助函数
"""
import re
import logging
import math
from collections import Counter

def extract_key_elements(fact):
    """
    提取案件事实中的关键要素

    该函数使用通用的文本分析方法提取案件事实中的关键要素，
    包括时间、地点、人物、金额和行为等。不针对特定案例进行特殊处理。

    Args:
        fact: 案件事实文本

    Returns:
        包含关键要素的字典
    """
    if not fact:
        return {
            "时间": [],
            "地点": [],
            "人物": [],
            "金额": [],
            "行为": []
        }

    # 提取时间 - 使用更通用的时间模式
    time_patterns = [
        r'\d{4}年\d{1,2}月\d{1,2}日',  # 2020年1月1日
        r'\d{1,2}月\d{1,2}日',         # 1月1日
        r'\d{4}年\d{1,2}月',           # 2020年1月
        r'\d{1,2}时\d{1,2}分',         # 10时30分
        r'\d{1,2}时许',                # 10时许
        r'\d{4}年',                    # 2020年
        r'\d{4}\.\d{1,2}\.\d{1,2}',    # 2020.1.1
        r'\d{4}-\d{1,2}-\d{1,2}'       # 2020-1-1
    ]

    times = []
    for pattern in time_patterns:
        times.extend(re.findall(pattern, fact))

    # 提取地点 - 使用更通用的地点模式
    location_patterns = [
        r'(在|于|至|到)([\u4e00-\u9fa5]{2,15}(市|县|区|镇|村|路|街|号|楼|室|内|处|院|所|厂|公司|中心|大厦|广场))',
        r'([\u4e00-\u9fa5]{2,6}(省|自治区|市|县|区))([\u4e00-\u9fa5]{2,15})'
    ]

    locations = []
    for pattern in location_patterns:
        matches = re.findall(pattern, fact)
        if matches:
            for match in matches:
                if isinstance(match, tuple) and len(match) >= 2:
                    if match[0] in ['在', '于', '至', '到']:
                        locations.append(match[1])
                    else:
                        locations.append(match[0] + (match[2] if len(match) > 2 else ''))

    # 提取人物 - 使用更通用的人物模式
    person_patterns = [
        r'(被告人|被害人|证人)?([\u4e00-\u9fa5]{1,2}某[\u4e00-\u9fa5]?|[\u4e00-\u9fa5]{1,2}某某)',
        r'(被告人|被害人|证人)([\u4e00-\u9fa5]{1,3})'
    ]

    persons = []
    for pattern in person_patterns:
        matches = re.findall(pattern, fact)
        if matches:
            for match in matches:
                if isinstance(match, tuple) and len(match) >= 2:
                    persons.append(match[1])

    # 提取金额 - 使用更通用的金额模式
    money_patterns = [
        r'(人民币)?(\d+(\.\d+)?)元',
        r'(人民币)?(\d+(\.\d+)?)万元',
        r'(人民币)?(\d+(\.\d+)?)亿元',
        r'价值(人民币)?(\d+(\.\d+)?)元',
        r'价值(人民币)?(\d+(\.\d+)?)万元'
    ]

    money_texts = []
    for pattern in money_patterns:
        matches = re.findall(pattern, fact)
        for match in matches:
            if len(match) >= 3:
                if match[1]:  # 元
                    money_texts.append(f"{match[1]}元")
                elif len(match) > 4 and match[4]:  # 万元
                    money_texts.append(f"{match[4]}万元")

    # 提取行为 - 使用更通用的方法，不限于特定关键词
    # 首先尝试提取句子中的谓语动词及其上下文
    action_patterns = [
        r'([\u4e00-\u9fa5]{1,2})(了|着)([\u4e00-\u9fa5]{1,5})',  # 动词+了/着+宾语
        r'(实施|进行|从事|组织)(了)?([\u4e00-\u9fa5]{2,8})(行为|活动)?',  # 特定动词引导的行为
        r'(犯有|构成|涉嫌)([\u4e00-\u9fa5]{2,10})(罪)?'  # 罪名相关
    ]

    actions = []
    for pattern in action_patterns:
        matches = re.findall(pattern, fact)
        if matches:
            for match in matches:
                if isinstance(match, tuple) and len(match) >= 3:
                    action = match[0] + (match[1] if match[1] else '') + match[2]
                    if len(action) > 2:  # 避免过短的行为描述
                        actions.append(action)

    # 如果上述方法提取的行为太少，尝试使用关键句提取
    if len(actions) < 2:
        # 寻找可能包含行为描述的句子
        sentences = re.split(r'[。！？；]', fact)
        for sentence in sentences:
            if any(keyword in sentence for keyword in ['实施', '进行', '从事', '组织', '犯有', '构成', '涉嫌']):
                if 5 < len(sentence) < 50:  # 合适长度的句子
                    actions.append(sentence)

    # 去重并限制数量
    times = list(dict.fromkeys(times))
    locations = list(dict.fromkeys(locations))
    persons = list(dict.fromkeys(persons))
    money_texts = list(dict.fromkeys(money_texts))
    actions = list(dict.fromkeys(actions))

    elements = {
        "时间": times[:5],  # 适当增加数量限制
        "地点": locations[:5],
        "人物": persons[:8],
        "金额": money_texts[:5],
        "行为": actions[:8]
    }

    return elements

def smart_truncate(text, max_length, key_words):
    """
    智能截断文本，保留关键信息

    Args:
        text: 原始文本
        max_length: 最大长度限制
        key_words: 关键词列表

    Returns:
        截断后的文本
    """
    if text is None:
        return "无文本"

    if len(text) <= max_length:
        return text

    # 查找包含关键词的段落
    key_paragraphs = []
    for keyword in key_words:
        if keyword in text:
            # 找到关键词所在的段落
            keyword_pos = text.find(keyword)
            if keyword_pos > max_length//3 and keyword_pos < len(text) - max_length//3:
                start = max(max_length//3, text.rfind("\n", 0, keyword_pos) + 1)
                end = min(len(text) - max_length//3, text.find("\n", keyword_pos))
                if end == -1:
                    end = len(text) - max_length//3
                if start < end and end - start < 300:  # 限制段落长度
                    key_paragraphs.append(text[start:end])

    # 保留前部分和后部分
    first_part = text[:max_length//3]
    last_part = text[-max_length//3:]

    # 组合处理后的文本
    middle_part = "\n\n".join(key_paragraphs[:3])  # 限制关键段落数量
    return first_part + "\n\n[...内容中间部分省略，以下是提取的关键信息...]\n\n" + middle_part + "\n\n[...内容结尾部分...]\n\n" + last_part

def convert_cn_num(cn_str):
    """
    将中文数字转换为阿拉伯数字，支持更复杂的表述

    Args:
        cn_str: 中文数字字符串

    Returns:
        阿拉伯数字
    """
    if not cn_str:
        return 0

    # 数字映射
    cn_num = {
        '零': 0, '一': 1, '二': 2, '两': 2, '三': 3, '四': 4, '五': 5,
        '六': 6, '七': 7, '八': 8, '九': 9, '十': 10,
        '百': 100, '千': 1000, '万': 10000, '亿': 100000000
    }

    # 处理简单情况
    if cn_str in cn_num:
        return cn_num[cn_str]

    # 预处理：将"零"替换为空，简化后续处理
    cn_str = cn_str.replace('零', '')

    # 处理"十"开头的情况
    if cn_str.startswith('十'):
        cn_str = '一' + cn_str

    # 处理特殊情况：两位数
    if '十' in cn_str and len(cn_str) <= 3:
        parts = cn_str.split('十')
        if len(parts) == 2:
            if parts[0] and parts[1]:
                return cn_num.get(parts[0], 0) * 10 + cn_num.get(parts[1], 0)
            elif parts[0] and not parts[1]:
                return cn_num.get(parts[0], 0) * 10
            elif not parts[0] and parts[1]:
                return 10 + cn_num.get(parts[1], 0)
            else:
                return 10

    # 处理复杂情况：包含"百"、"千"、"万"、"亿"的数字
    result = 0
    temp = 0
    unit = 1

    # 从右向左处理，确保单位正确累加
    for i in range(len(cn_str) - 1, -1, -1):
        char = cn_str[i]

        if char in ['亿', '万', '千', '百', '十']:
            # 处理单位
            if char == '亿':
                unit = 100000000
                result += temp
                temp = 0
            elif char == '万':
                unit = 10000
                result += temp
                temp = 0
            elif char == '千':
                temp += unit * 1000
            elif char == '百':
                temp += unit * 100
            elif char == '十':
                temp += unit * 10
        elif char in cn_num:
            # 处理数字
            temp += cn_num[char] * unit

    # 加上最后的临时结果
    result += temp

    # 如果结果为0，尝试按位解析
    if result == 0:
        result = 0
        for char in cn_str:
            if char in cn_num and char not in ['亿', '万', '千', '百', '十']:
                result = result * 10 + cn_num[char]

    return result

def get_imprisonment_range(accusation, severity_level="一般"):
    """
    获取通用的刑期范围，基于罪名的一般严重程度分类

    该函数使用通用的刑期范围分类，不针对特定罪名进行特殊处理。
    而是基于中国刑法对不同类型犯罪的一般量刑标准进行分类。

    Args:
        accusation: 罪名
        severity_level: 严重程度级别 ("轻微", "一般", "严重", "特别严重")

    Returns:
        (最低刑期, 最高刑期, 标准刑期) 单位：月
    """
    # 基于刑法规定的通用刑期范围
    base_ranges = {
        "轻微": (6, 36, 18),  # 最低6个月，最高3年，标准18个月
        "一般": (36, 84, 60),  # 最低3年，最高7年，标准5年
        "严重": (84, 180, 120),  # 最低7年，最高15年，标准10年
        "特别严重": (180, 360, 240)  # 最低15年，最高30年，标准20年
    }

    # 使用传入的严重程度级别，不再基于罪名关键词进行特判
    # 这样可以确保函数的通用性，不会对特定罪名进行特殊处理

    # 如果传入的严重程度级别无效，使用默认值"一般"
    if severity_level not in base_ranges:
        severity_level = "一般"

    return base_ranges[severity_level]

def calculate_metrics(true_values, pred_values, tolerance=12):
    """
    计算通用评估指标

    Args:
        true_values: 真实值列表
        pred_values: 预测值列表
        tolerance: 容忍误差范围

    Returns:
        包含各种指标的字典
    """
    if not true_values or not pred_values:
        return {}

    # 计算绝对误差
    errors = [abs(p - t) for p, t in zip(pred_values, true_values)]

    # 计算在容忍范围内的预测数量
    correct = sum(1 for e in errors if e <= tolerance)

    # 计算各种指标
    metrics = {
        "accuracy": correct / len(true_values),
        "mae": sum(errors) / len(errors),
        "rmse": math.sqrt(sum(e**2 for e in errors) / len(errors)),
        "median_error": sorted(errors)[len(errors)//2],
        "max_error": max(errors),
        "min_error": min(errors),
        "within_tolerance": correct,
        "total": len(true_values)
    }

    # 计算加权平均绝对误差 - 考虑不同罪名的刑期范围差异
    if len(true_values) == len(pred_values):
        # 计算每个案例的权重 - 基于真实刑期的倒数
        weights = [1 / max(t, 1) for t in true_values]
        total_weight = sum(weights)
        weighted_mae = sum(e * w for e, w in zip(errors, weights)) / total_weight
        metrics["weighted_mae"] = weighted_mae

    return metrics
