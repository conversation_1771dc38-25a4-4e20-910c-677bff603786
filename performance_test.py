#!/usr/bin/env python3
"""
性能测试脚本
测试系统优化后的性能表现
"""

import time
from judicial_ie_coordinator import JudicialIECoordinator, ExtractionTaskType

def test_performance():
    """测试系统性能"""
    print("=== 司法信息抽取系统性能测试 ===")
    
    # 初始化协调器
    coordinator = JudicialIECoordinator()
    
    # 测试用例
    test_cases = [
        "被告人张某持木棒击打李某头部，致李某轻伤二级。",
        "被告人王某某于2023年5月通过网络平台发布虚假信息，诽谤他人，严重损害他人名誉，情节严重。",
        "被告人刘某于2023年7月在某商场内盗窃他人财物价值人民币3000元。案发后，刘某被当场抓获。"
    ]
    
    total_time = 0
    successful_tests = 0
    
    for i, test_text in enumerate(test_cases, 1):
        print(f"\n--- 测试案例 {i} ---")
        print(f"文本: {test_text[:50]}...")
        
        start_time = time.time()
        
        try:
            result = coordinator.extract_information(
                test_text, 
                ExtractionTaskType.COMPREHENSIVE_EXTRACTION
            )
            
            end_time = time.time()
            extraction_time = end_time - start_time
            total_time += extraction_time
            
            if result.get('status') == 'success':
                successful_tests += 1
                
                print(f"✅ 抽取成功")
                print(f"⏱️  抽取时间: {extraction_time:.2f}秒")
                print(f"📊 抽取结果数量: {len(result.get('results', []))}")
                print(f"🎯 平均置信度: {result.get('metrics', {}).get('average_confidence', 0):.2f}")
                print(f"🔍 证据权重引导改进: {result.get('metrics', {}).get('performance_metrics', {}).get('evidence_guided_improvements', 0)}")
                
                # 显示抽取结果概览
                results = result.get('results', [])
                if results:
                    print("📋 抽取结果概览:")
                    for j, res in enumerate(results[:3], 1):  # 只显示前3个
                        print(f"   {j}. {res.get('info_type', 'unknown')}: {res.get('content', '')[:30]}...")
                        
            else:
                print(f"❌ 抽取失败: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            end_time = time.time()
            extraction_time = end_time - start_time
            total_time += extraction_time
    
    # 性能总结
    print(f"\n=== 性能测试总结 ===")
    print(f"📈 总测试案例: {len(test_cases)}")
    print(f"✅ 成功案例: {successful_tests}")
    print(f"⏱️  总耗时: {total_time:.2f}秒")
    print(f"📊 平均耗时: {total_time/len(test_cases):.2f}秒/案例")
    print(f"🎯 成功率: {successful_tests/len(test_cases)*100:.1f}%")
    
    # 性能评估
    avg_time = total_time / len(test_cases)
    if avg_time < 5:
        print(f"🎉 性能优秀: 平均抽取时间 {avg_time:.2f}秒 < 5秒目标")
    elif avg_time < 10:
        print(f"⚠️  性能良好: 平均抽取时间 {avg_time:.2f}秒，接近目标")
    else:
        print(f"🔧 需要优化: 平均抽取时间 {avg_time:.2f}秒 > 10秒，需要进一步优化")

if __name__ == "__main__":
    test_performance()
