"""
判决抽取智能体测试模块

用于测试SentenceExtractionAgent的核心功能，包括：
1. 基本判决抽取功能测试
2. 刑期信息精确性测试
3. 判决准确性验证测试
4. 协调器集成测试

确保判决抽取智能体与系统架构的一致性和功能完整性。
"""

import json
import logging
from typing import Dict, List

# 导入测试相关模块
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from extraction_agents.sentence_extractor import SentenceExtractionAgent, ExtractionTaskType

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SentenceExtractorTester:
    """判决抽取智能体测试器"""

    def __init__(self):
        self.test_cases = self._load_test_cases()
        logger.info("判决抽取智能体测试器初始化完成")

    def _load_test_cases(self) -> List[Dict]:
        """加载测试案例"""
        test_cases = [
            {
                "id": "sentence_test_001",
                "text": """
                被告人张某于2023年3月15日晚上8时许，在某市某区某街道因琐事与被害人李某发生争执，
                张某持木棒击打李某头部，致李某轻伤二级。案发后，张某主动投案自首，如实供述犯罪事实，
                认罪态度良好。经查，张某系初犯，平时表现良好，有悔罪表现。

                本院认为，被告人张某故意伤害他人身体，致人轻伤，其行为已构成故意伤害罪。
                鉴于被告人张某犯罪后能够主动投案自首，如实供述犯罪事实，依照《中华人民共和国刑法》
                第二百三十四条第一款、第六十七条第一款的规定，可以从轻处罚。

                判决被告人张某犯故意伤害罪，判处拘役六个月。
                """,
                "expected_sentence_elements": {
                    "verdict": "犯故意伤害罪",
                    "imprisonment": "拘役六个月",
                    "amount_months": 6,
                    "reasoning": "主动投案自首，如实供述犯罪事实"
                }
            },
            {
                "id": "sentence_test_002",
                "text": """
                被告人李某某于2023年5月通过网络平台发布虚假信息，诽谤他人，
                严重损害他人名誉，情节严重。经查，李某某明知所发布信息为虚假，
                仍故意传播，造成恶劣社会影响。

                本院认为，被告人李某某捏造事实诽谤他人，情节严重，其行为构成诽谤罪。
                依照《中华人民共和国刑法》第二百四十六条的规定，应予惩处。

                判决被告人李某某犯诽谤罪，判处有期徒刑一年，缓刑二年。
                """,
                "expected_sentence_elements": {
                    "verdict": "犯诽谤罪",
                    "imprisonment": "有期徒刑一年",
                    "amount_months": 12,
                    "execution": "缓刑二年"
                }
            }
        ]
        return test_cases

    def test_basic_sentence_extraction(self):
        """测试基本判决抽取功能"""
        print("\n" + "="*60)
        print("⚖️ 测试1: 基本判决抽取功能")
        print("="*60)

        try:
            agent = SentenceExtractionAgent()

            for i, test_case in enumerate(self.test_cases, 1):
                print(f"\n📋 测试案例 {i}: {test_case['id']}")
                print(f"📄 文本长度: {len(test_case['text'])} 字符")

                # 执行判决抽取
                results = agent.extract(test_case['text'], ExtractionTaskType.SENTENCE_EXTRACTION)

                print(f"🎯 抽取结果数量: {len(results)}")

                # 按类别统计结果
                sentence_categories = {}
                for result in results:
                    category = result.metadata.get("sentence_category", "unknown")
                    sentence_categories[category] = sentence_categories.get(category, 0) + 1

                print(f"📊 判决要素类别分布: {sentence_categories}")

                # 显示部分抽取结果
                for j, result in enumerate(results[:8], 1):
                    category = result.metadata.get("sentence_category", "unknown")
                    print(f"  {j}. [{category}] {result.content[:60]}... (置信度: {result.confidence:.2f}, 权重: {result.evidence_weight:.2f})")

                if len(results) > 8:
                    print(f"  ... 还有 {len(results) - 8} 个结果")

                # 检查API调用统计
                stats = agent.get_performance_stats()
                print(f"📊 API调用次数: {stats.get('api_calls', 0)}")
                print(f"📊 判决准确性分数: {stats.get('average_sentence_accuracy', 0):.2f}")

                if results:
                    print(f"✅ 案例 {i} 判决抽取成功")
                else:
                    print(f"⚠️ 案例 {i} 未抽取到判决信息")

            return True

        except Exception as e:
            print(f"❌ 基本判决抽取测试失败: {e}")
            return False

    def test_imprisonment_precision(self):
        """测试刑期信息精确性"""
        print("\n" + "="*60)
        print("📏 测试2: 刑期信息精确性验证")
        print("="*60)

        try:
            agent = SentenceExtractionAgent()

            for i, test_case in enumerate(self.test_cases, 1):
                print(f"\n📋 测试案例 {i}: {test_case['id']}")

                results = agent.extract(test_case['text'], ExtractionTaskType.SENTENCE_EXTRACTION)

                # 查找刑期信息
                imprisonment_results = [r for r in results if r.info_type.value == "imprisonment"]

                print(f"📊 抽取的刑期信息数量: {len(imprisonment_results)}")

                for j, result in enumerate(imprisonment_results, 1):
                    amount_months = result.metadata.get("amount_months", 0)
                    print(f"  {j}. 刑期: {result.content}")
                    print(f"     月数: {amount_months} 个月")
                    print(f"     置信度: {result.confidence:.2f}")

                    # 验证期望的刑期月数
                    expected_months = test_case["expected_sentence_elements"].get("amount_months", 0)
                    if expected_months > 0:
                        if amount_months == expected_months:
                            print(f"     ✅ 刑期月数准确 (期望: {expected_months})")
                        else:
                            print(f"     ⚠️ 刑期月数不准确 (期望: {expected_months}, 实际: {amount_months})")

                if imprisonment_results:
                    print(f"✅ 案例 {i} 刑期信息抽取成功")
                else:
                    print(f"⚠️ 案例 {i} 未抽取到刑期信息")

            return True

        except Exception as e:
            print(f"❌ 刑期信息精确性测试失败: {e}")
            return False

    def test_sentence_accuracy_validation(self):
        """测试判决准确性验证机制"""
        print("\n" + "="*60)
        print("🔍 测试3: 判决准确性验证机制")
        print("="*60)

        try:
            agent = SentenceExtractionAgent()

            # 使用第一个测试案例
            test_case = self.test_cases[0]
            results = agent.extract(test_case['text'], ExtractionTaskType.SENTENCE_EXTRACTION)

            # 统计各类判决要素
            sentence_categories = {}
            for result in results:
                category = result.metadata.get("sentence_category", "unknown")
                sentence_categories[category] = sentence_categories.get(category, 0) + 1

            print(f"📊 抽取的判决要素类别: {list(sentence_categories.keys())}")

            # 检查是否包含期望的判决要素类别
            expected_categories = ["sentence", "imprisonment", "reasoning"]
            found_categories = list(sentence_categories.keys())

            coverage = len(set(expected_categories) & set(found_categories)) / len(expected_categories)
            print(f"📊 判决要素类别覆盖率: {coverage:.2f}")

            # 检查判决准确性分数
            stats = agent.get_performance_stats()
            accuracy_score = stats.get('average_sentence_accuracy', 0)
            print(f"📊 判决准确性分数: {accuracy_score:.2f}")

            # 验证判决结果和刑期的匹配性
            sentences = [r for r in results if r.info_type.value == "sentence"]
            imprisonments = [r for r in results if r.info_type.value == "imprisonment"]

            print(f"📊 抽取的判决结果数量: {len(sentences)}")
            print(f"📊 抽取的刑期信息数量: {len(imprisonments)}")

            if sentences and imprisonments:
                print("✅ 判决结果和刑期匹配验证通过")
            else:
                print("⚠️ 判决结果和刑期匹配需要改进")

            if accuracy_score >= 0.7:
                print("✅ 判决准确性验证通过")
            else:
                print("⚠️ 判决准确性需要改进")

            return True

        except Exception as e:
            print(f"❌ 判决准确性验证测试失败: {e}")
            return False

    def test_integration_with_coordinator(self):
        """测试与协调器的集成"""
        print("\n" + "="*60)
        print("🔗 测试4: 协调器集成测试")
        print("="*60)

        try:
            # 导入协调器
            from judicial_ie_coordinator import JudicialIECoordinator, ExtractionTaskType as CoordinatorTaskType

            coordinator = JudicialIECoordinator()

            # 检查判决抽取智能体是否正确集成
            if coordinator.agents.get("sentence") is not None:
                print("✅ 判决抽取智能体已集成到协调器")

                # 测试通过协调器进行判决抽取
                test_case = self.test_cases[0]
                result = coordinator.extract_information(
                    test_case['text'],
                    CoordinatorTaskType.SENTENCE_EXTRACTION
                )

                if result.get('status') == 'success':
                    results = result.get('results', {})
                    detailed_results = results.get('detailed_results', [])

                    # 统计判决抽取结果
                    sentence_results = [r for r in detailed_results if r.get('info_type') in
                                      ['sentence', 'imprisonment', 'judgment_reasoning', 'execution_method']]
                    print(f"✅ 通过协调器抽取到 {len(sentence_results)} 个判决要素")

                    # 检查系统性能指标
                    metrics = result.get('metrics', {})
                    performance = metrics.get('performance_metrics', {})
                    print(f"✅ 系统性能指标正常: {len(performance)} 个指标")

                    return True
                else:
                    print(f"❌ 协调器抽取失败: {result.get('error', 'unknown error')}")
                    return False
            else:
                print("⚠️ 判决抽取智能体尚未集成到协调器，这是正常的（将在后续集成）")
                return True

        except Exception as e:
            print(f"❌ 协调器集成测试失败: {e}")
            return False

    def run_comprehensive_test(self):
        """运行综合测试"""
        print("🚀 开始判决抽取智能体综合测试")
        print("="*80)

        test_results = []

        # 执行所有测试
        tests = [
            ("基本判决抽取功能", self.test_basic_sentence_extraction),
            ("刑期信息精确性", self.test_imprisonment_precision),
            ("判决准确性验证", self.test_sentence_accuracy_validation),
            ("协调器集成", self.test_integration_with_coordinator)
        ]

        for test_name, test_func in tests:
            try:
                result = test_func()
                test_results.append((test_name, result))
            except Exception as e:
                print(f"❌ {test_name}测试异常: {e}")
                test_results.append((test_name, False))

        # 输出测试总结
        print("\n" + "="*80)
        print("测试总结")
        print("="*80)

        passed = 0
        failed = 0

        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")
            if result:
                passed += 1
            else:
                failed += 1

        print(f"\n总计: {passed} 个测试通过, {failed} 个测试失败")

        if failed == 0:
            print("🎉 所有判决抽取测试通过！智能体功能正常。")
        else:
            print("⚠️  部分测试失败，需要进一步调试。")

        return failed == 0

def main():
    """主测试函数"""
    tester = SentenceExtractorTester()
    success = tester.run_comprehensive_test()
    return success

if __name__ == "__main__":
    main()
