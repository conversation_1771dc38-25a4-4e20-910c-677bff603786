"""
法律要素抽取智能体测试模块

用于测试LegalElementAgent的核心功能，包括：
1. 基本法律要素抽取功能测试
2. 法律准确性验证测试
3. 协调器集成测试
4. LLM API一致性测试

确保法律要素抽取智能体与系统架构的一致性和功能完整性。
"""

import json
import logging
from typing import Dict, List

# 导入测试相关模块
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from extraction_agents.legal_element_extractor import LegalElementAgent, ExtractionTaskType

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class LegalElementExtractorTester:
    """法律要素抽取智能体测试器"""

    def __init__(self):
        self.test_cases = self._load_test_cases()
        logger.info("法律要素抽取智能体测试器初始化完成")

    def _load_test_cases(self) -> List[Dict]:
        """加载测试案例"""
        test_cases = [
            {
                "id": "legal_test_001",
                "text": """
                被告人张某于2023年3月15日晚上8时许，在某市某区某街道因琐事与被害人李某发生争执，
                张某持木棒击打李某头部，致李某轻伤二级。案发后，张某主动投案自首，如实供述犯罪事实，
                认罪态度良好。经查，张某系初犯，平时表现良好，有悔罪表现。

                本院认为，被告人张某故意伤害他人身体，致人轻伤，其行为已构成故意伤害罪。
                鉴于被告人张某犯罪后能够主动投案自首，如实供述犯罪事实，依照《中华人民共和国刑法》
                第二百三十四条第一款、第六十七条第一款的规定，可以从轻处罚。

                判决被告人张某犯故意伤害罪，判处拘役六个月。
                """,
                "expected_legal_elements": {
                    "charges": ["故意伤害罪"],
                    "legal_articles": ["第二百三十四条第一款", "第六十七条第一款"],
                    "sentencing_factors": ["自首", "认罪态度良好", "初犯"]
                }
            },
            {
                "id": "legal_test_002",
                "text": """
                被告人李某某于2023年5月通过网络平台发布虚假信息，诽谤他人，
                严重损害他人名誉，情节严重。经查，李某某明知所发布信息为虚假，
                仍故意传播，造成恶劣社会影响。

                本院认为，被告人李某某捏造事实诽谤他人，情节严重，其行为构成诽谤罪。
                依照《中华人民共和国刑法》第二百四十六条的规定，应予惩处。
                """,
                "expected_legal_elements": {
                    "charges": ["诽谤罪"],
                    "legal_articles": ["第二百四十六条"],
                    "sentencing_factors": ["情节严重"]
                }
            }
        ]
        return test_cases

    def test_basic_legal_extraction(self):
        """测试基本法律要素抽取功能"""
        print("\n" + "="*60)
        print("🧠 测试1: 基本法律要素抽取功能")
        print("="*60)

        try:
            agent = LegalElementAgent()

            for i, test_case in enumerate(self.test_cases, 1):
                print(f"\n📋 测试案例 {i}: {test_case['id']}")
                print(f"📄 文本长度: {len(test_case['text'])} 字符")

                # 执行法律要素抽取
                results = agent.extract(test_case['text'], ExtractionTaskType.LEGAL_ELEMENT_EXTRACTION)

                print(f"🎯 抽取结果数量: {len(results)}")

                # 按类别统计结果
                legal_categories = {}
                for result in results:
                    category = result.metadata.get("legal_category", "unknown")
                    legal_categories[category] = legal_categories.get(category, 0) + 1

                print(f"📊 法律要素类别分布: {legal_categories}")

                # 显示部分抽取结果
                for j, result in enumerate(results[:8], 1):
                    category = result.metadata.get("legal_category", "unknown")
                    print(f"  {j}. [{category}] {result.content[:60]}... (置信度: {result.confidence:.2f}, 权重: {result.evidence_weight:.2f})")

                if len(results) > 8:
                    print(f"  ... 还有 {len(results) - 8} 个结果")

                # 检查API调用统计
                stats = agent.get_performance_stats()
                print(f"📊 API调用次数: {stats.get('api_calls', 0)}")
                print(f"📊 法律准确性分数: {stats.get('average_legal_accuracy', 0):.2f}")

                if results:
                    print(f"✅ 案例 {i} 法律要素抽取成功")
                else:
                    print(f"⚠️ 案例 {i} 未抽取到法律要素")

            return True

        except Exception as e:
            print(f"❌ 基本法律要素抽取测试失败: {e}")
            return False

    def test_legal_accuracy_validation(self):
        """测试法律准确性验证机制"""
        print("\n" + "="*60)
        print("🔍 测试2: 法律准确性验证机制")
        print("="*60)

        try:
            agent = LegalElementAgent()

            # 使用第一个测试案例
            test_case = self.test_cases[0]
            results = agent.extract(test_case['text'], ExtractionTaskType.LEGAL_ELEMENT_EXTRACTION)

            # 统计各类法律要素
            legal_categories = {}
            for result in results:
                category = result.metadata.get("legal_category", "unknown")
                legal_categories[category] = legal_categories.get(category, 0) + 1

            print(f"📊 抽取的法律要素类别: {list(legal_categories.keys())}")

            # 检查是否包含期望的法律要素类别
            expected_categories = ["charge", "legal_article", "sentencing_factor"]
            found_categories = list(legal_categories.keys())

            coverage = len(set(expected_categories) & set(found_categories)) / len(expected_categories)
            print(f"📊 法律要素类别覆盖率: {coverage:.2f}")

            # 检查法律准确性分数
            stats = agent.get_performance_stats()
            accuracy_score = stats.get('average_legal_accuracy', 0)
            print(f"📊 法律准确性分数: {accuracy_score:.2f}")

            # 验证罪名和法条的匹配性
            charges = [r for r in results if r.info_type.value == "charges"]
            legal_articles = [r for r in results if r.info_type.value == "legal_articles"]

            print(f"📊 抽取的罪名数量: {len(charges)}")
            print(f"📊 抽取的法条数量: {len(legal_articles)}")

            if charges and legal_articles:
                print("✅ 罪名和法条匹配验证通过")
            else:
                print("⚠️ 罪名和法条匹配需要改进")

            if accuracy_score >= 0.7:
                print("✅ 法律准确性验证通过")
            else:
                print("⚠️ 法律准确性需要改进")

            return True

        except Exception as e:
            print(f"❌ 法律准确性验证测试失败: {e}")
            return False

    def test_integration_with_coordinator(self):
        """测试与协调器的集成"""
        print("\n" + "="*60)
        print("🔗 测试3: 协调器集成测试")
        print("="*60)

        try:
            # 导入协调器
            from judicial_ie_coordinator import JudicialIECoordinator, ExtractionTaskType as CoordinatorTaskType

            coordinator = JudicialIECoordinator()

            # 检查法律要素智能体是否正确集成
            if coordinator.agents.get("legal") is not None:
                print("✅ 法律要素智能体已集成到协调器")

                # 测试通过协调器进行法律要素抽取
                test_case = self.test_cases[0]
                result = coordinator.extract_information(
                    test_case['text'],
                    CoordinatorTaskType.LEGAL_ELEMENT_EXTRACTION
                )

                if result.get('status') == 'success':
                    results = result.get('results', {})
                    detailed_results = results.get('detailed_results', [])

                    # 统计法律要素抽取结果
                    legal_results = [r for r in detailed_results if r.get('info_type') in
                                   ['charges', 'legal_articles', 'legal_elements', 'sentencing_factors']]
                    print(f"✅ 通过协调器抽取到 {len(legal_results)} 个法律要素")

                    # 检查系统性能指标
                    metrics = result.get('metrics', {})
                    performance = metrics.get('performance_metrics', {})
                    print(f"✅ 系统性能指标正常: {len(performance)} 个指标")

                    return True
                else:
                    print(f"❌ 协调器抽取失败: {result.get('error', 'unknown error')}")
                    return False
            else:
                print("⚠️ 法律要素智能体尚未集成到协调器，这是正常的（将在后续集成）")
                return True

        except Exception as e:
            print(f"❌ 协调器集成测试失败: {e}")
            return False

    def test_llm_api_consistency(self):
        """测试LLM API调用一致性"""
        print("\n" + "="*60)
        print("🔧 测试4: LLM API调用一致性验证")
        print("="*60)

        try:
            agent = LegalElementAgent()

            # 检查是否使用了正确的get_completion函数
            from extraction_agents.legal_element_extractor import get_completion
            print("✅ 成功导入get_completion函数，确认API一致性")

            # 测试API调用
            test_case = self.test_cases[0]
            results = agent.extract(test_case['text'], ExtractionTaskType.LEGAL_ELEMENT_EXTRACTION)

            # 检查API调用统计
            stats = agent.get_performance_stats()
            api_calls = stats.get('api_calls', 0)

            print(f"✅ API调用次数: {api_calls}")
            print(f"✅ 抽取方法: {stats.get('extraction_method', 'unknown')}")
            print(f"✅ 智能体类型: {stats.get('agent_type', 'unknown')}")

            # 验证返回结果格式
            if results and all(hasattr(r, 'info_type') and hasattr(r, 'confidence') for r in results):
                print("✅ 返回结果格式符合ExtractionResult规范")
            else:
                print("⚠️ 返回结果格式需要检查")

            if api_calls > 0:
                print("✅ LLM API调用一致性验证通过")
                return True
            else:
                print("⚠️ 未检测到API调用，可能存在问题")
                return False

        except Exception as e:
            print(f"❌ LLM API一致性测试失败: {e}")
            return False

    def run_comprehensive_test(self):
        """运行综合测试"""
        print("🚀 开始法律要素抽取智能体综合测试")
        print("="*80)

        test_results = []

        # 执行所有测试
        tests = [
            ("基本法律要素抽取功能", self.test_basic_legal_extraction),
            ("法律准确性验证", self.test_legal_accuracy_validation),
            ("协调器集成", self.test_integration_with_coordinator),
            ("LLM API一致性", self.test_llm_api_consistency)
        ]

        for test_name, test_func in tests:
            try:
                result = test_func()
                test_results.append((test_name, result))
            except Exception as e:
                print(f"❌ {test_name}测试异常: {e}")
                test_results.append((test_name, False))

        # 输出测试总结
        print("\n" + "="*80)
        print("测试总结")
        print("="*80)

        passed = 0
        failed = 0

        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")
            if result:
                passed += 1
            else:
                failed += 1

        print(f"\n总计: {passed} 个测试通过, {failed} 个测试失败")

        if failed == 0:
            print("🎉 所有法律要素抽取测试通过！智能体功能正常。")
        else:
            print("⚠️  部分测试失败，需要进一步调试。")

        return failed == 0

def main():
    """主测试函数"""
    tester = LegalElementExtractorTester()
    success = tester.run_comprehensive_test()
    return success

if __name__ == "__main__":
    main()
