"""
多智能体司法信息抽取系统集成测试

用于测试完整的多智能体协作系统，包括：
1. 所有智能体的协调器集成测试
2. 四阶段抽取流程验证
3. 证据权重引导机制测试
4. 冲突解决机制测试
5. 系统性能指标验证

确保整个系统的协作机制正常运行。
"""

import json
import logging
from typing import Dict, List

# 导入测试相关模块
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from judicial_ie_coordinator import JudicialIECoordinator, ExtractionTaskType

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class IntegratedSystemTester:
    """多智能体司法信息抽取系统集成测试器"""

    def __init__(self):
        self.test_cases = self._load_test_cases()
        logger.info("多智能体司法信息抽取系统集成测试器初始化完成")

    def _load_test_cases(self) -> List[Dict]:
        """加载测试案例"""
        test_cases = [
            {
                "id": "integrated_test_001",
                "text": """
                被告人张某于2023年3月15日晚上8时许，在某市某区某街道因琐事与被害人李某发生争执，
                张某持木棒击打李某头部，致李某轻伤二级。案发后，张某主动投案自首，如实供述犯罪事实，
                认罪态度良好。经查，张某系初犯，平时表现良好，有悔罪表现。

                本院认为，被告人张某故意伤害他人身体，致人轻伤，其行为已构成故意伤害罪。
                鉴于被告人张某犯罪后能够主动投案自首，如实供述犯罪事实，依照《中华人民共和国刑法》
                第二百三十四条第一款、第六十七条第一款的规定，可以从轻处罚。

                判决被告人张某犯故意伤害罪，判处拘役六个月。
                """,
                "expected_elements": {
                    "entities": ["张某", "李某", "某市某区某街道"],
                    "facts": ["持木棒击打", "轻伤二级", "主动投案自首"],
                    "legal_elements": ["故意伤害罪", "第二百三十四条第一款"],
                    "sentences": ["判处拘役六个月"]
                }
            }
        ]
        return test_cases

    def test_all_agents_integration(self):
        """测试所有智能体的协调器集成"""
        print("\n" + "="*60)
        print("🤝 测试1: 所有智能体协调器集成")
        print("="*60)

        try:
            coordinator = JudicialIECoordinator()

            # 检查所有智能体是否正确加载
            expected_agents = ["entity", "fact", "legal", "sentence"]
            loaded_agents = list(coordinator.agents.keys())

            print(f"📊 期望的智能体: {expected_agents}")
            print(f"📊 已加载的智能体: {loaded_agents}")

            # 检查每个智能体是否非空
            active_agents = []
            for agent_name, agent in coordinator.agents.items():
                if agent is not None:
                    active_agents.append(agent_name)
                    print(f"  ✅ {agent_name} 智能体已激活")
                else:
                    print(f"  ❌ {agent_name} 智能体未激活")

            print(f"📊 激活的智能体数量: {len(active_agents)}/{len(expected_agents)}")

            if len(active_agents) == len(expected_agents):
                print("✅ 所有智能体集成成功")
                return True
            else:
                print("⚠️ 部分智能体集成失败")
                return False

        except Exception as e:
            print(f"❌ 智能体集成测试失败: {e}")
            return False

    def test_four_stage_extraction_process(self):
        """测试四阶段抽取流程"""
        print("\n" + "="*60)
        print("🔄 测试2: 四阶段抽取流程验证")
        print("="*60)

        try:
            coordinator = JudicialIECoordinator()
            test_case = self.test_cases[0]

            print(f"📋 测试案例: {test_case['id']}")
            print(f"📄 文本长度: {len(test_case['text'])} 字符")

            # 执行综合信息抽取
            result = coordinator.extract_information(
                test_case['text'],
                ExtractionTaskType.COMPREHENSIVE_EXTRACTION
            )

            if result.get('status') == 'success':
                results = result.get('results', {})
                detailed_results = results.get('detailed_results', [])

                print(f"🎯 抽取结果总数: {len(detailed_results)}")

                # 按智能体统计结果
                agent_stats = {}
                for item in detailed_results:
                    agent_id = item.get('agent_id', 'unknown')
                    agent_stats[agent_id] = agent_stats.get(agent_id, 0) + 1

                print(f"📊 各智能体抽取结果分布:")
                for agent_id, count in agent_stats.items():
                    print(f"  {agent_id}: {count} 个结果")

                # 检查系统性能指标
                metrics = result.get('metrics', {})
                extraction_time = metrics.get('extraction_time', 0)
                performance_metrics = metrics.get('performance_metrics', {})

                print(f"⏱️ 抽取耗时: {extraction_time:.2f} 秒")
                print(f"📊 性能指标: {performance_metrics}")

                # 验证四阶段流程的效果
                quality_metrics = results.get('quality_metrics', {})
                avg_confidence = quality_metrics.get('average_confidence', 0)
                avg_evidence_weight = quality_metrics.get('average_evidence_weight', 0)

                print(f"📊 平均置信度: {avg_confidence:.2f}")
                print(f"📊 平均证据权重: {avg_evidence_weight:.2f}")

                if len(detailed_results) > 0 and avg_confidence > 0.7:
                    print("✅ 四阶段抽取流程验证通过")
                    return True
                else:
                    print("⚠️ 四阶段抽取流程需要改进")
                    return False
            else:
                print(f"❌ 抽取失败: {result.get('error', 'unknown error')}")
                return False

        except Exception as e:
            print(f"❌ 四阶段抽取流程测试失败: {e}")
            return False

    def test_evidence_guided_mechanism(self):
        """测试证据权重引导机制"""
        print("\n" + "="*60)
        print("🎯 测试3: 证据权重引导机制验证")
        print("="*60)

        try:
            coordinator = JudicialIECoordinator()
            test_case = self.test_cases[0]

            # 执行抽取并检查证据权重引导的效果
            result = coordinator.extract_information(
                test_case['text'],
                ExtractionTaskType.COMPREHENSIVE_EXTRACTION
            )

            if result.get('status') == 'success':
                results = result.get('results', {})
                detailed_results = results.get('detailed_results', [])

                # 检查是否有证据权重引导的标记
                evidence_guided_count = 0
                enhancement_factors = []

                for item in detailed_results:
                    metadata = item.get('metadata', {})
                    if metadata.get('evidence_guided', False):
                        evidence_guided_count += 1
                        enhancement_factor = metadata.get('enhancement_factor', 1.0)
                        enhancement_factors.append(enhancement_factor)

                print(f"📊 证据权重引导的结果数量: {evidence_guided_count}/{len(detailed_results)}")

                if enhancement_factors:
                    avg_enhancement = sum(enhancement_factors) / len(enhancement_factors)
                    print(f"📊 平均增强因子: {avg_enhancement:.2f}")

                # 检查创新指标
                innovation_metrics = results.get('innovation_metrics', {})
                evidence_weight_utilization = innovation_metrics.get('evidence_weight_utilization', 0)

                print(f"📊 证据权重利用率: {evidence_weight_utilization:.2f}")

                if evidence_guided_count > 0 and evidence_weight_utilization > 0.6:
                    print("✅ 证据权重引导机制验证通过")
                    return True
                else:
                    print("⚠️ 证据权重引导机制需要改进")
                    return False
            else:
                print(f"❌ 抽取失败: {result.get('error', 'unknown error')}")
                return False

        except Exception as e:
            print(f"❌ 证据权重引导机制测试失败: {e}")
            return False

    def test_conflict_resolution_mechanism(self):
        """测试冲突解决机制"""
        print("\n" + "="*60)
        print("⚔️ 测试4: 冲突解决机制验证")
        print("="*60)

        try:
            coordinator = JudicialIECoordinator()
            test_case = self.test_cases[0]

            # 执行抽取并检查冲突解决的效果
            result = coordinator.extract_information(
                test_case['text'],
                ExtractionTaskType.COMPREHENSIVE_EXTRACTION
            )

            if result.get('status') == 'success':
                # 检查性能指标中的冲突解决统计
                metrics = result.get('metrics', {})
                performance_metrics = metrics.get('performance_metrics', {})

                conflicts_resolved = performance_metrics.get('conflicts_resolved', 0)
                total_extractions = performance_metrics.get('total_extractions', 0)

                print(f"📊 解决的冲突数量: {conflicts_resolved}")
                print(f"📊 总抽取结果数量: {total_extractions}")

                # 检查创新指标中的冲突解决率
                results_data = result.get('results', {})
                innovation_metrics = results_data.get('innovation_metrics', {})
                conflict_resolution_rate = innovation_metrics.get('conflict_resolution_rate', 0)

                print(f"📊 冲突解决率: {conflict_resolution_rate:.2f}")

                # 检查是否有辩论解决的标记
                detailed_results = results_data.get('detailed_results', [])
                debate_resolved_count = 0

                for item in detailed_results:
                    metadata = item.get('metadata', {})
                    if metadata.get('resolution_method') == 'debate':
                        debate_resolved_count += 1
                        print(f"  🎯 发现辩论解决的结果: {item.get('content', '')[:50]}...")

                print(f"📊 通过辩论解决的结果数量: {debate_resolved_count}")

                if conflict_resolution_rate >= 0 and total_extractions > 0:
                    print("✅ 冲突解决机制验证通过")
                    return True
                else:
                    print("⚠️ 冲突解决机制需要改进")
                    return False
            else:
                print(f"❌ 抽取失败: {result.get('error', 'unknown error')}")
                return False

        except Exception as e:
            print(f"❌ 冲突解决机制测试失败: {e}")
            return False

    def test_system_performance_metrics(self):
        """测试系统性能指标"""
        print("\n" + "="*60)
        print("📊 测试5: 系统性能指标验证")
        print("="*60)

        try:
            coordinator = JudicialIECoordinator()
            test_case = self.test_cases[0]

            # 执行多次抽取以获得性能统计
            results_list = []
            for i in range(3):
                result = coordinator.extract_information(
                    test_case['text'],
                    ExtractionTaskType.COMPREHENSIVE_EXTRACTION
                )
                if result.get('status') == 'success':
                    results_list.append(result)

            if results_list:
                print(f"📊 成功执行 {len(results_list)} 次抽取")

                # 统计性能指标
                total_times = []
                total_results = []
                avg_confidences = []
                avg_evidence_weights = []

                for result in results_list:
                    metrics = result.get('metrics', {})
                    total_times.append(metrics.get('extraction_time', 0))
                    total_results.append(metrics.get('total_results', 0))

                    results_data = result.get('results', {})
                    quality_metrics = results_data.get('quality_metrics', {})
                    avg_confidences.append(quality_metrics.get('average_confidence', 0))
                    avg_evidence_weights.append(quality_metrics.get('average_evidence_weight', 0))

                # 计算平均性能
                avg_time = sum(total_times) / len(total_times)
                avg_results_count = sum(total_results) / len(total_results)
                avg_confidence = sum(avg_confidences) / len(avg_confidences)
                avg_evidence_weight = sum(avg_evidence_weights) / len(avg_evidence_weights)

                print(f"📊 平均抽取时间: {avg_time:.2f} 秒")
                print(f"📊 平均结果数量: {avg_results_count:.1f} 个")
                print(f"📊 平均置信度: {avg_confidence:.2f}")
                print(f"📊 平均证据权重: {avg_evidence_weight:.2f}")

                # 性能评估
                performance_score = 0
                if avg_time < 10:  # 抽取时间小于10秒
                    performance_score += 25
                if avg_results_count >= 5:  # 平均抽取结果大于等于5个
                    performance_score += 25
                if avg_confidence >= 0.7:  # 平均置信度大于等于0.7
                    performance_score += 25
                if avg_evidence_weight >= 0.6:  # 平均证据权重大于等于0.6
                    performance_score += 25

                print(f"📊 系统性能评分: {performance_score}/100")

                if performance_score >= 75:
                    print("✅ 系统性能指标验证通过")
                    return True
                else:
                    print("⚠️ 系统性能需要优化")
                    return False
            else:
                print("❌ 未能获得有效的性能数据")
                return False

        except Exception as e:
            print(f"❌ 系统性能指标测试失败: {e}")
            return False

    def run_comprehensive_integration_test(self):
        """运行综合集成测试"""
        print("🚀 开始多智能体司法信息抽取系统综合集成测试")
        print("="*80)

        test_results = []

        # 执行所有测试
        tests = [
            ("所有智能体协调器集成", self.test_all_agents_integration),
            ("四阶段抽取流程验证", self.test_four_stage_extraction_process),
            ("证据权重引导机制", self.test_evidence_guided_mechanism),
            ("冲突解决机制", self.test_conflict_resolution_mechanism),
            ("系统性能指标", self.test_system_performance_metrics)
        ]

        for test_name, test_func in tests:
            try:
                result = test_func()
                test_results.append((test_name, result))
            except Exception as e:
                print(f"❌ {test_name}测试异常: {e}")
                test_results.append((test_name, False))

        # 输出测试总结
        print("\n" + "="*80)
        print("集成测试总结")
        print("="*80)

        passed = 0
        failed = 0

        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")
            if result:
                passed += 1
            else:
                failed += 1

        print(f"\n总计: {passed} 个测试通过, {failed} 个测试失败")

        if failed == 0:
            print("🎉 所有集成测试通过！多智能体系统功能正常。")
        elif failed <= 1:
            print("⚠️  大部分测试通过，系统基本正常，需要小幅调优。")
        else:
            print("⚠️  多个测试失败，需要进一步调试和优化。")

        return failed <= 1

def main():
    """主测试函数"""
    tester = IntegratedSystemTester()
    success = tester.run_comprehensive_integration_test()
    return success

if __name__ == "__main__":
    main()
