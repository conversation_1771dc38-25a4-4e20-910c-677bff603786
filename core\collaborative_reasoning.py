"""
协作推理引擎 (CollaborativeReasoningEngine)

多智能体协作的推理增强基础设施，提供：
- 协作推理会话管理
- 多智能体联合推理
- 共识水平计算
- 洞察综合分析
- 推理过程追踪

学术价值：
- 支持复杂的多智能体协作推理
- 提供共识计算和一致性评估
- 为协作决策提供理论基础
"""

import logging
import time
from typing import Dict, List, Optional, Any

# 导入相关模块
from .message_bus import MessageBus, AgentMessage, MessageType
from .shared_knowledge import SharedKnowledgeBase

# 配置日志
logger = logging.getLogger(__name__)


class CollaborativeReasoningEngine:
    """协作推理增强引擎"""

    def __init__(self, message_bus: MessageBus, knowledge_base: SharedKnowledgeBase):
        self.message_bus = message_bus
        self.knowledge_base = knowledge_base
        self.reasoning_sessions: Dict[str, Dict] = {}
        self.inference_cache: Dict[str, Dict] = {}

        logger.info("协作推理增强引擎初始化完成")

    def start_collaborative_reasoning(self, session_id: str, participants: List[str],
                                    problem: Dict, context: Dict) -> Dict:
        """
        启动协作推理会话
        
        Args:
            session_id: 会话ID
            participants: 参与智能体列表
            problem: 推理问题描述
            context: 推理上下文
            
        Returns:
            创建的推理会话
        """
        session = {
            "id": session_id,
            "participants": participants,
            "problem": problem,
            "context": context,
            "start_time": time.time(),
            "reasoning_steps": [],
            "shared_insights": [],
            "consensus_level": 0.0,
            "status": "active"
        }

        self.reasoning_sessions[session_id] = session

        # 通知参与者
        for participant in participants:
            message = AgentMessage(
                sender_id="reasoning_engine",
                receiver_id=participant,
                message_type=MessageType.COLLABORATION_REQUEST,
                content={
                    "action": "join_reasoning_session",
                    "session_id": session_id,
                    "problem": problem,
                    "context": context
                },
                requires_response=True
            )
            self.message_bus.publish(message)

        logger.info(f"启动协作推理会话 {session_id}，参与者: {participants}")
        return session

    def add_reasoning_step(self, session_id: str, agent_id: str, reasoning: Dict) -> bool:
        """
        添加推理步骤
        
        Args:
            session_id: 会话ID
            agent_id: 智能体ID
            reasoning: 推理内容
            
        Returns:
            是否成功添加
        """
        if session_id not in self.reasoning_sessions:
            logger.error(f"推理会话 {session_id} 不存在")
            return False

        step = {
            "agent_id": agent_id,
            "reasoning": reasoning,
            "timestamp": time.time(),
            "confidence": reasoning.get("confidence", 0.8),
            "step_id": len(self.reasoning_sessions[session_id]["reasoning_steps"]) + 1
        }

        self.reasoning_sessions[session_id]["reasoning_steps"].append(step)

        # 分享推理结果给其他参与者
        session = self.reasoning_sessions[session_id]
        for participant in session["participants"]:
            if participant != agent_id:
                message = AgentMessage(
                    sender_id=agent_id,
                    receiver_id=participant,
                    message_type=MessageType.KNOWLEDGE_SHARE,
                    content={
                        "action": "reasoning_step_shared",
                        "session_id": session_id,
                        "reasoning": reasoning
                    }
                )
                self.message_bus.publish(message)

        # 存储到共享知识库
        knowledge_id = f"reasoning_{session_id}_{agent_id}_{len(session['reasoning_steps'])}"
        self.knowledge_base.store_knowledge(
            knowledge_id=knowledge_id,
            content={
                "reasoning": reasoning,
                "session_id": session_id,
                "confidence": step["confidence"],
                "tags": ["reasoning", "collaborative", session_id]
            },
            contributor_id=agent_id,
            knowledge_type="reasoning_step"
        )

        logger.info(f"智能体 {agent_id} 在会话 {session_id} 中添加了推理步骤")
        return True

    def calculate_consensus_level(self, session_id: str) -> float:
        """
        计算共识水平
        
        Args:
            session_id: 会话ID
            
        Returns:
            共识水平 (0.0-1.0)
        """
        if session_id not in self.reasoning_sessions:
            return 0.0

        session = self.reasoning_sessions[session_id]
        reasoning_steps = session["reasoning_steps"]

        if len(reasoning_steps) < 2:
            return 0.0

        # 计算推理步骤间的一致性
        consensus_scores = []
        
        for i in range(len(reasoning_steps)):
            for j in range(i + 1, len(reasoning_steps)):
                step1 = reasoning_steps[i]
                step2 = reasoning_steps[j]
                
                # 计算两个推理步骤的相似度
                similarity = self._calculate_reasoning_similarity(
                    step1["reasoning"], step2["reasoning"]
                )
                consensus_scores.append(similarity)

        # 计算平均共识水平
        if consensus_scores:
            consensus_level = sum(consensus_scores) / len(consensus_scores)
        else:
            consensus_level = 0.0

        # 更新会话中的共识水平
        session["consensus_level"] = consensus_level
        
        logger.debug(f"会话 {session_id} 的共识水平: {consensus_level:.3f}")
        return consensus_level

    def _calculate_reasoning_similarity(self, reasoning1: Dict, reasoning2: Dict) -> float:
        """
        计算两个推理的相似度
        
        Args:
            reasoning1: 第一个推理
            reasoning2: 第二个推理
            
        Returns:
            相似度分数 (0.0-1.0)
        """
        # 简化的相似度计算
        similarity = 0.0
        
        # 1. 结论相似度
        conclusion1 = reasoning1.get("conclusion", "")
        conclusion2 = reasoning2.get("conclusion", "")
        if conclusion1 and conclusion2:
            # 简单的字符串相似度
            common_words = set(conclusion1.lower().split()) & set(conclusion2.lower().split())
            total_words = set(conclusion1.lower().split()) | set(conclusion2.lower().split())
            if total_words:
                similarity += 0.4 * (len(common_words) / len(total_words))

        # 2. 置信度相似度
        conf1 = reasoning1.get("confidence", 0.5)
        conf2 = reasoning2.get("confidence", 0.5)
        conf_similarity = 1.0 - abs(conf1 - conf2)
        similarity += 0.3 * conf_similarity

        # 3. 推理类型相似度
        type1 = reasoning1.get("type", "")
        type2 = reasoning2.get("type", "")
        if type1 == type2:
            similarity += 0.3

        return min(1.0, similarity)

    def synthesize_collaborative_insights(self, session_id: str) -> Dict:
        """
        综合协作洞察
        
        Args:
            session_id: 会话ID
            
        Returns:
            综合洞察结果
        """
        if session_id not in self.reasoning_sessions:
            return {}

        session = self.reasoning_sessions[session_id]
        reasoning_steps = session["reasoning_steps"]

        if not reasoning_steps:
            return {"insights": [], "consensus_level": 0.0}

        # 提取关键洞察
        insights = []
        confidence_scores = []
        conclusions = []

        for step in reasoning_steps:
            reasoning = step["reasoning"]
            
            # 收集洞察
            if "insights" in reasoning:
                insights.extend(reasoning["insights"])
            
            # 收集置信度
            confidence_scores.append(step["confidence"])
            
            # 收集结论
            if "conclusion" in reasoning:
                conclusions.append(reasoning["conclusion"])

        # 计算综合指标
        avg_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0.0
        consensus_level = self.calculate_consensus_level(session_id)

        # 识别共同洞察
        common_insights = self._identify_common_insights(insights)

        # 生成综合结论
        synthesized_conclusion = self._synthesize_conclusions(conclusions)

        result = {
            "session_id": session_id,
            "insights": common_insights,
            "synthesized_conclusion": synthesized_conclusion,
            "consensus_level": consensus_level,
            "average_confidence": avg_confidence,
            "total_reasoning_steps": len(reasoning_steps),
            "active_participants": len(set(step["agent_id"] for step in reasoning_steps)),
            "synthesis_time": time.time()
        }

        # 存储综合洞察到知识库
        knowledge_id = f"synthesis_{session_id}_{int(time.time())}"
        self.knowledge_base.store_knowledge(
            knowledge_id=knowledge_id,
            content=result,
            contributor_id="reasoning_engine",
            knowledge_type="collaborative_synthesis"
        )

        logger.info(f"完成会话 {session_id} 的协作洞察综合")
        return result

    def _identify_common_insights(self, insights: List[str]) -> List[Dict]:
        """
        识别共同洞察
        
        Args:
            insights: 洞察列表
            
        Returns:
            共同洞察列表
        """
        if not insights:
            return []

        # 简化的共同洞察识别
        insight_counts = {}
        for insight in insights:
            insight_lower = insight.lower().strip()
            if insight_lower:
                insight_counts[insight_lower] = insight_counts.get(insight_lower, 0) + 1

        # 选择出现频率高的洞察
        common_insights = []
        for insight, count in insight_counts.items():
            if count > 1:  # 至少被两个智能体提及
                common_insights.append({
                    "insight": insight,
                    "frequency": count,
                    "confidence": min(1.0, count / len(insights))
                })

        # 按频率排序
        common_insights.sort(key=lambda x: x["frequency"], reverse=True)
        return common_insights[:5]  # 返回前5个最常见的洞察

    def _synthesize_conclusions(self, conclusions: List[str]) -> str:
        """
        综合结论
        
        Args:
            conclusions: 结论列表
            
        Returns:
            综合结论
        """
        if not conclusions:
            return ""

        if len(conclusions) == 1:
            return conclusions[0]

        # 简化的结论综合
        # 在实际应用中，这里可以使用更复杂的NLP技术
        unique_conclusions = list(set(conclusions))
        
        if len(unique_conclusions) == 1:
            return unique_conclusions[0]
        else:
            return f"基于 {len(conclusions)} 个推理步骤，形成了 {len(unique_conclusions)} 个不同的结论，需要进一步协商达成共识。"

    def get_session_stats(self, session_id: str) -> Dict:
        """
        获取会话统计信息
        
        Args:
            session_id: 会话ID
            
        Returns:
            会话统计信息
        """
        if session_id not in self.reasoning_sessions:
            return {}

        session = self.reasoning_sessions[session_id]
        reasoning_steps = session["reasoning_steps"]

        # 计算参与度统计
        participant_stats = {}
        for step in reasoning_steps:
            agent_id = step["agent_id"]
            if agent_id not in participant_stats:
                participant_stats[agent_id] = {
                    "steps_count": 0,
                    "avg_confidence": 0.0,
                    "total_confidence": 0.0
                }
            
            participant_stats[agent_id]["steps_count"] += 1
            participant_stats[agent_id]["total_confidence"] += step["confidence"]

        # 计算平均置信度
        for agent_id, stats in participant_stats.items():
            if stats["steps_count"] > 0:
                stats["avg_confidence"] = stats["total_confidence"] / stats["steps_count"]

        return {
            "session_id": session_id,
            "status": session["status"],
            "duration": time.time() - session["start_time"],
            "total_participants": len(session["participants"]),
            "active_participants": len(participant_stats),
            "total_reasoning_steps": len(reasoning_steps),
            "consensus_level": session.get("consensus_level", 0.0),
            "participant_stats": participant_stats
        }

    def close_session(self, session_id: str) -> bool:
        """
        关闭推理会话
        
        Args:
            session_id: 会话ID
            
        Returns:
            是否成功关闭
        """
        if session_id not in self.reasoning_sessions:
            return False

        session = self.reasoning_sessions[session_id]
        session["status"] = "closed"
        session["end_time"] = time.time()

        # 通知参与者会话结束
        for participant in session["participants"]:
            message = AgentMessage(
                sender_id="reasoning_engine",
                receiver_id=participant,
                message_type=MessageType.TASK_COORDINATION,
                content={
                    "action": "reasoning_session_closed",
                    "session_id": session_id,
                    "final_consensus": session.get("consensus_level", 0.0)
                }
            )
            self.message_bus.publish(message)

        logger.info(f"关闭推理会话 {session_id}")
        return True

    def get_all_sessions_stats(self) -> Dict:
        """
        获取所有会话的统计信息
        
        Returns:
            所有会话统计信息
        """
        total_sessions = len(self.reasoning_sessions)
        active_sessions = sum(1 for s in self.reasoning_sessions.values() if s["status"] == "active")
        
        total_steps = sum(len(s["reasoning_steps"]) for s in self.reasoning_sessions.values())
        
        if self.reasoning_sessions:
            avg_consensus = sum(s.get("consensus_level", 0.0) for s in self.reasoning_sessions.values()) / total_sessions
        else:
            avg_consensus = 0.0

        return {
            "total_sessions": total_sessions,
            "active_sessions": active_sessions,
            "completed_sessions": total_sessions - active_sessions,
            "total_reasoning_steps": total_steps,
            "average_consensus_level": avg_consensus,
            "cache_size": len(self.inference_cache)
        }
